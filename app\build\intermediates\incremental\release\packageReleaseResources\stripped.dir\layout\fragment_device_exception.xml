<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_exception"
        android:layout_width="228dp"
        android:layout_height="200dp"
        tools:src="@drawable/ic_network_exception"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="120dp"
        android:layout_marginEnd="140dp"/>

    <TextView
        android:id="@+id/tv_exception"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_network_exception"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:gravity="center"
        android:layout_marginTop="35dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_exception"
        app:layout_constraintRight_toRightOf="@+id/iv_exception"
        app:layout_constraintTop_toBottomOf="@+id/iv_exception"/>

    <ImageView
        android:id="@+id/iv_refresh"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_refresh_white"
        android:background="@drawable/common_eb4e89_round_bg"
        android:scaleType="centerInside"
        android:layout_marginTop="16dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_exception"
        app:layout_constraintRight_toRightOf="@+id/iv_exception"
        app:layout_constraintTop_toBottomOf="@+id/tv_exception"/>

</androidx.constraintlayout.widget.ConstraintLayout>