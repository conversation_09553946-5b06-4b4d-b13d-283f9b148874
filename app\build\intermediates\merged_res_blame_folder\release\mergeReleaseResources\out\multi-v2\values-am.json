{"logs": [{"outputFile": "com.airdoc.mpd.app-mergeReleaseResources-69:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37eb7cb3503359f8e3891a2adf804078\\transformed\\jetified-media3-ui-1.3.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3201,3265,3315,3371,3441,3511,3563", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51,63", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3196,3260,3310,3366,3436,3506,3558,3622"}, "to": {"startLines": "2,11,15,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,577,6940,7020,7101,7177,7262,7343,7409,7471,7555,7638,7705,7768,7829,7895,7995,8097,8198,8267,8343,8411,8477,8556,8636,8698,9376,9429,9486,9532,9593,9651,9726,9785,9847,9906,9963,10027,10090,10154,10204,10260,10330,10400,10452", "endLines": "10,14,18,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51,63", "endOffsets": "373,572,754,7015,7096,7172,7257,7338,7404,7466,7550,7633,7700,7763,7824,7890,7990,8092,8193,8262,8338,8406,8472,8551,8631,8693,8758,9424,9481,9527,9588,9646,9721,9780,9842,9901,9958,10022,10085,10149,10199,10255,10325,10395,10447,10511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ede9995337ea73b4d0233d500609b091\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1018,1116,1222,1308,1411,1528,1606,1682,1773,1866,1958,2052,2152,2245,2340,2433,2524,2615,2695,2795,2895,2991,3093,3193,3292,3442,14874", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "1013,1111,1217,1303,1406,1523,1601,1677,1768,1861,1953,2047,2147,2240,2335,2428,2519,2610,2690,2790,2890,2986,3088,3188,3287,3437,3533,14949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\efd467293e0b9b5a51777a2be79e83eb\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8763,8825,8883,8945,9005,9077,9140,9229,9310", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "8820,8878,8940,9000,9072,9135,9224,9305,9371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f784686b41df3e3e9ff94a38ce261387\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "5795", "endColumns": "131", "endOffsets": "5922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\09e94a311f42d674eb715371ac8d596c\\transformed\\material-1.10.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,970,1055,1117,1175,1260,1323,1385,1443,1509,1571,1626,1722,1779,1838,1894,1961,2066,2146,2227,2356,2429,2500,2614,2696,2772,2823,2874,2940,3006,3079,3160,3235,3303,3376,3447,3514,3612,3697,3764,3851,3939,4013,4081,4166,4217,4295,4359,4439,4521,4583,4647,4710,4776,4871,4966,5051,5142,5197,5252", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "256,327,395,470,552,633,722,824,901,965,1050,1112,1170,1255,1318,1380,1438,1504,1566,1621,1717,1774,1833,1889,1956,2061,2141,2222,2351,2424,2495,2609,2691,2767,2818,2869,2935,3001,3074,3155,3230,3298,3371,3442,3509,3607,3692,3759,3846,3934,4008,4076,4161,4212,4290,4354,4434,4516,4578,4642,4705,4771,4866,4961,5046,5137,5192,5247,5323"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "759,3538,3609,3677,3752,3834,4602,4691,4793,6876,10516,10601,10663,10721,10806,10869,10931,10989,11055,11117,11172,11268,11325,11384,11440,11507,11612,11692,11773,11902,11975,12046,12160,12242,12318,12369,12420,12486,12552,12625,12706,12781,12849,12922,12993,13060,13158,13243,13310,13397,13485,13559,13627,13712,13763,13841,13905,13985,14067,14129,14193,14256,14322,14417,14512,14597,14688,14743,14798", "endLines": "22,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "endColumns": "12,70,67,74,81,80,88,101,76,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,128,72,70,113,81,75,50,50,65,65,72,80,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75", "endOffsets": "915,3604,3672,3747,3829,3910,4686,4788,4865,6935,10596,10658,10716,10801,10864,10926,10984,11050,11112,11167,11263,11320,11379,11435,11502,11607,11687,11768,11897,11970,12041,12155,12237,12313,12364,12415,12481,12547,12620,12701,12776,12844,12917,12988,13055,13153,13238,13305,13392,13480,13554,13622,13707,13758,13836,13900,13980,14062,14124,14188,14251,14317,14412,14507,14592,14683,14738,14793,14869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c63b318755d78145d01b8b87b88f3c2\\transformed\\core-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "55,56,57,58,59,60,61,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3915,4008,4108,4205,4304,4400,4502,14954", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "4003,4103,4200,4299,4395,4497,4597,15050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf04cdc715fc93d5a24d642c24f51c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4972,5111,5233,5335,5462,5585,5693,5927,6055,6158,6303,6426,6561,6688,6748,6805", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4967,5106,5228,5330,5457,5580,5688,5790,6050,6153,6298,6421,6556,6683,6743,6800,6871"}}]}]}