// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDetectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ShapeableImageView ivDetection;

  @NonNull
  public final TextView tvDetection;

  private ItemDetectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull ShapeableImageView ivDetection, @NonNull TextView tvDetection) {
    this.rootView = rootView;
    this.ivDetection = ivDetection;
    this.tvDetection = tvDetection;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDetectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDetectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_detection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDetectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_detection;
      ShapeableImageView ivDetection = ViewBindings.findChildViewById(rootView, id);
      if (ivDetection == null) {
        break missingId;
      }

      id = R.id.tv_detection;
      TextView tvDetection = ViewBindings.findChildViewById(rootView, id);
      if (tvDetection == null) {
        break missingId;
      }

      return new ItemDetectionBinding((ConstraintLayout) rootView, ivDetection, tvDetection);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
