<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="160dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:background="@color/white">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_detection"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        app:shapeAppearance="@style/shape_image_round_10dp"
        app:strokeColor="@null"
        android:scaleType="centerCrop"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_detection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:maxWidth="160dp"
        tools:text="压力检测"
        android:textColor="#2B2F36"
        android:textSize="14sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:gravity="center"
        android:layout_marginTop="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_detection"/>

</androidx.constraintlayout.widget.ConstraintLayout>