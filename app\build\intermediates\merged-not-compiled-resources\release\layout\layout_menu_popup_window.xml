<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_white_round_15_bg"
    android:padding="15dp"
    android:minWidth="150dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/ll_view_report"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/iv_view_report"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_view_report" />

        <TextView
            android:id="@+id/tv_view_report"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_view_report"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_device_info"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_device_info"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_device_info" />

        <TextView
            android:id="@+id/tv_device_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_device_info"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_language"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_language"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_language_settings" />

        <TextView
            android:id="@+id/tv_language"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_language_settings"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_scanner_settings"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_scanner_settings"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_scanner_settings" />

        <TextView
            android:id="@+id/tv_scanner_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_scanner_settings"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_more_settings"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_more_settings"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_more_settings" />

        <TextView
            android:id="@+id/tv_more_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_more_settings"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_version"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/iv_version"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_version"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent" />

        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="版本号：1.0.0"
            android:textColor="@color/color_333333"
            android:textSize="15sp"
            android:includeFontPadding="false"
            android:layout_marginStart="10dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/iv_version"/>

        <View
            android:id="@+id/version_red_dot"
            android:layout_width="10dp"
            android:layout_height="10dp"
            app:layout_constraintTop_toTopOf="@+id/tv_version"
            app:layout_constraintRight_toRightOf="@+id/tv_version"
            android:background="@drawable/common_ea4e3d_round_bg"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>