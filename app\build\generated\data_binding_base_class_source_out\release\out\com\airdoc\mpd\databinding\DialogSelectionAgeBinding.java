// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.NumberPicker;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogSelectionAgeBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final NumberPicker npSelectAge;

  @NonNull
  public final TextView tvCancel;

  @NonNull
  public final TextView tvOk;

  private DialogSelectionAgeBinding(@NonNull ConstraintLayout rootView,
      @NonNull NumberPicker npSelectAge, @NonNull TextView tvCancel, @NonNull TextView tvOk) {
    this.rootView = rootView;
    this.npSelectAge = npSelectAge;
    this.tvCancel = tvCancel;
    this.tvOk = tvOk;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogSelectionAgeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogSelectionAgeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_selection_age, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogSelectionAgeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.np_select_age;
      NumberPicker npSelectAge = ViewBindings.findChildViewById(rootView, id);
      if (npSelectAge == null) {
        break missingId;
      }

      id = R.id.tv_cancel;
      TextView tvCancel = ViewBindings.findChildViewById(rootView, id);
      if (tvCancel == null) {
        break missingId;
      }

      id = R.id.tv_ok;
      TextView tvOk = ViewBindings.findChildViewById(rootView, id);
      if (tvOk == null) {
        break missingId;
      }

      return new DialogSelectionAgeBinding((ConstraintLayout) rootView, npSelectAge, tvCancel,
          tvOk);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
