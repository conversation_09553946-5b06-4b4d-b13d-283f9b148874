// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ActivityCalibrationBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final FrameLayout flCalibrationRoot;

  private ActivityCalibrationBinding(@NonNull FrameLayout rootView,
      @NonNull FrameLayout flCalibrationRoot) {
    this.rootView = rootView;
    this.flCalibrationRoot = flCalibrationRoot;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCalibrationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCalibrationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_calibration, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCalibrationBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    FrameLayout flCalibrationRoot = (FrameLayout) rootView;

    return new ActivityCalibrationBinding((FrameLayout) rootView, flCalibrationRoot);
  }
}
