<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="380dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_device_info_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_scanner_settings"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_left"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintRight_toLeftOf="@+id/tv_title"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_right"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="357dp"
        android:layout_height="211dp"
        android:background="@drawable/ic_device_info_dialog_content_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

<!--        <TextView-->
<!--            android:id="@+id/tv_select_qr_code_type"-->
<!--            android:layout_width="wrap_content"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:text="@string/str_select_qr_code_type"-->
<!--            android:textColor="@color/color_333333"-->
<!--            android:textSize="18sp"-->
<!--            android:layout_marginTop="30dp"-->
<!--            android:layout_marginStart="35dp"-->
<!--            android:includeFontPadding="false"-->
<!--            app:layout_constraintTop_toTopOf="parent"-->
<!--            app:layout_constraintLeft_toLeftOf="parent"/>-->

        <RadioGroup
            android:id="@+id/rg_scan_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="30dp"
            android:layout_marginStart="35dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <RadioButton
                android:id="@+id/rb_h5_qr_code"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                tools:checked="true"
                android:text="@string/str_browser_scan"
                android:button="@drawable/selector_common_radio_button"
                android:buttonTint="@color/selector_common_radio_button_tint"
                app:buttonTint="@color/selector_common_radio_button_tint"
                android:paddingStart="9dp"
                android:textSize="16sp"
                android:textColor="@color/color_333333"/>

            <RadioButton
                android:id="@+id/rb_whatsapp_qr_code"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/str_whatsapp_scan"
                android:button="@drawable/selector_common_radio_button"
                android:buttonTint="@color/selector_common_radio_button_tint"
                app:buttonTint="@color/selector_common_radio_button_tint"
                android:paddingStart="9dp"
                android:textSize="16sp"
                android:textColor="@color/color_333333" />

        </RadioGroup>

        <Space
            android:id="@+id/space_line1"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginBottom="90dp"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/tv_select_qr_code_type_tips"
            android:layout_width="310dp"
            android:layout_height="wrap_content"
            android:text="@string/str_h5_qr_code_tips"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:includeFontPadding="false"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/space_line1"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d6dce1_round_bg"
        android:focusable="true"
        android:layout_marginBottom="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="25dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>