<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <EditText
        android:id="@+id/et_detection_code"
        android:layout_width="406dp"
        android:layout_height="55dp"
        android:textSize="18sp"
        android:textColor="@color/color_333333"
        android:textColorHint="#ABADB0"
        android:hint="@string/str_please_enter_detection_code"
        android:maxLines="1"
        android:includeFontPadding="false"
        android:textCursorDrawable="@drawable/input_cursor"
        android:background="@drawable/main_input_bg"
        android:imeOptions="actionDone"
        android:singleLine="true"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:gravity="center"
        android:layout_marginTop="180dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/iv_scan"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/ic_scan"
        android:padding="10dp"
        app:layout_constraintTop_toTopOf="@+id/et_detection_code"
        app:layout_constraintBottom_toBottomOf="@+id/et_detection_code"
        app:layout_constraintRight_toRightOf="@+id/et_detection_code"/>

    <ImageView
        android:id="@+id/iv_cross"
        android:layout_width="38dp"
        android:layout_height="38dp"
        android:src="@drawable/ic_cross"
        android:padding="10dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/et_detection_code"
        app:layout_constraintBottom_toBottomOf="@+id/et_detection_code"
        app:layout_constraintRight_toLeftOf="@+id/iv_scan"/>

    <TextView
        android:id="@+id/tv_start_detection"
        android:layout_width="406dp"
        android:layout_height="55dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:text="@string/str_start_detection"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:gravity="center"
        android:focusable="false"
        android:layout_marginTop="20dp"
        app:layout_constraintTop_toBottomOf="@+id/et_detection_code"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>