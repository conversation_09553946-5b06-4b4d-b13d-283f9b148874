<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle" >

<!--    <padding-->
<!--        android:bottom="5dp"-->
<!--        android:left="5dp"-->
<!--        android:right="5dp"-->
<!--        android:top="5dp" />-->

    <!-- 设置圆角矩形
         radius:圆角弧度，核心代码
    -->
    <corners android:radius="1000dp" />

    <!-- 设置描边效果
         width:描边宽度
         color:描边颜色
     -->
<!--    <stroke-->
<!--        android:width="1dp"-->
<!--        android:color="#CE005C" />-->

    <!-- 设置填充效果
         color:填充颜色
     -->
    <solid android:color="#D6DCE1" />

    <!--  渐变  -->
<!--    <gradient-->
<!--        android:startColor="#C6A2D4"-->
<!--        android:endColor="#7960A9"-->
<!--        android:type="linear"/>-->

</shape>