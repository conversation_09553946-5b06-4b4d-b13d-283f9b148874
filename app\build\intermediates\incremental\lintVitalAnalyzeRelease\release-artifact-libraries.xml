<libraries>
  <library
      name="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified@jar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\jars\classes.jar"
      resolved="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.databinding:viewbinding:8.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\107ef3c689762754d687a6eca283bfe6\transformed\jetified-viewbinding-8.3.1\jars\classes.jar"
      resolved="androidx.databinding:viewbinding:8.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\107ef3c689762754d687a6eca283bfe6\transformed\jetified-viewbinding-8.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.getstream:stream-log-android-file:1.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\jars\classes.jar"
      resolved="io.getstream:stream-log-android-file:1.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.getstream:stream-log-android:1.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4\jars\classes.jar"
      resolved="io.getstream:stream-log-android:1.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\01b40dbe62c6fbba0eb13e7ba0fdf334\transformed\jetified-stream-log-android-1.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.material:material:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0\jars\classes.jar"
      resolved="com.google.android.material:material:1.10.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\09e94a311f42d674eb715371ac8d596c\transformed\material-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning:17.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning:17.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\dc2893f89eae3ba97224142d0dcaa4a8\transformed\jetified-barcode-scanning-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:face-detection:16.1.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7\jars\classes.jar"
      resolved="com.google.mlkit:face-detection:16.1.7"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bf6c3cb5840c02cc9f768a2f9f39e79c\transformed\jetified-face-detection-16.1.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\98cb6cffa8655ab6507ce428bc5ad66e\transformed\jetified-barcode-scanning-common-17.0.0\jars\classes.jar"
      resolved="com.google.mlkit:barcode-scanning-common:17.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\98cb6cffa8655ab6507ce428bc5ad66e\transformed\jetified-barcode-scanning-common-17.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-mlkit-face-detection:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-common:17.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-common:17.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:common:18.11.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\jars\classes.jar"
      resolved="com.google.mlkit:common:18.11.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ede9995337ea73b4d0233d500609b091\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:okhttp3-integration:4.15.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\jars\classes.jar"
      resolved="com.github.bumptech.glide:okhttp3-integration:4.15.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:glide:4.15.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1\jars\classes.jar"
      resolved="com.github.bumptech.glide:glide:4.15.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8bd3eae0f86996815ebc23cc6048bb94\transformed\jetified-glide-4.15.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager2:viewpager2:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0041beb24dec71b8781b322d4e2ed1b8\transformed\jetified-viewpager2-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager2:viewpager2:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0041beb24dec71b8781b322d4e2ed1b8\transformed\jetified-viewpager2-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.jeremyliao:live-event-bus-x:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\72c76e291e940458346c0f7672d72a79\transformed\jetified-live-event-bus-x-1.8.0\jars\classes.jar"
      resolved="io.github.jeremyliao:live-event-bus-x:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\72c76e291e940458346c0f7672d72a79\transformed\jetified-live-event-bus-x-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-extensions:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\37c0572a772a3210daa7784c0db88cd4\transformed\lifecycle-extensions-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-base:18.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-base:18.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.mlkit:vision-interfaces:16.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e4b928c7bad8580dee32a0714ef5a363\transformed\jetified-vision-interfaces-16.3.0\jars\classes.jar"
      resolved="com.google.mlkit:vision-interfaces:16.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e4b928c7bad8580dee32a0714ef5a363\transformed\jetified-vision-interfaces-16.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-tasks:18.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-tasks:18.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e558dea2d0a70667137efa91d48cf54c\transformed\jetified-play-services-tasks-18.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.gms:play-services-basement:18.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\jars\classes.jar"
      resolved="com.google.android.gms:play-services-basement:18.4.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\448265791efcfaa1041f3e7728cd0633\transformed\fragment-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment-ktx:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\97b2440c29059681c04a5336dc04ae7e\transformed\jetified-fragment-ktx-1.6.1\jars\classes.jar"
      resolved="androidx.fragment:fragment-ktx:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\97b2440c29059681c04a5336dc04ae7e\transformed\jetified-fragment-ktx-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\42c76c7d0438bd8e341c573f2b121faa\transformed\jetified-activity-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\42c76c7d0438bd8e341c573f2b121faa\transformed\jetified-activity-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\73c31c96b3aaf5c40e722e923ba58c4e\transformed\jetified-activity-ktx-1.8.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\73c31c96b3aaf5c40e722e923ba58c4e\transformed\jetified-activity-ktx-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-video:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4ea2378e1282e0fb536810f07b7bf93b\transformed\jetified-camera-video-1.4.2\jars\classes.jar"
      resolved="androidx.camera:camera-video:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4ea2378e1282e0fb536810f07b7bf93b\transformed\jetified-camera-video-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-lifecycle:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\728f2b84f64e685ae69899a1bdc516b0\transformed\jetified-camera-lifecycle-1.4.2\jars\classes.jar"
      resolved="androidx.camera:camera-lifecycle:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\728f2b84f64e685ae69899a1bdc516b0\transformed\jetified-camera-lifecycle-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-view:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d2b2867e1f7a04ce611c1533259dee9d\transformed\jetified-camera-view-1.4.2\jars\classes.jar"
      resolved="androidx.camera:camera-view:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d2b2867e1f7a04ce611c1533259dee9d\transformed\jetified-camera-view-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-camera2:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\jars\classes.jar"
      resolved="androidx.camera:camera-camera2:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.camera:camera-core:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2\jars\classes.jar"
      resolved="androidx.camera:camera-core:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5dcf23016f5fd81271748c4a43ea45ef\transformed\jetified-camera-core-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\96ee904e48e7583859b6a0d2569ec642\transformed\jetified-lifecycle-service-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-service:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\96ee904e48e7583859b6a0d2569ec642\transformed\jetified-lifecycle-service-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a38bec121c9b55f1b7513221109f9a56\transformed\lifecycle-livedata-core-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a38bec121c9b55f1b7513221109f9a56\transformed\lifecycle-livedata-core-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.2\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.2.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.2"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\90e6daa8b8789c4f9a5c06e0fc803fe1\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\90e6daa8b8789c4f9a5c06e0fc803fe1\transformed\jetified-lifecycle-livedata-core-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\40c7a5108dba4e3b8da3f0a7ddc771f9\transformed\work-runtime-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime-ktx:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\40c7a5108dba4e3b8da3f0a7ddc771f9\transformed\work-runtime-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.work:work-runtime:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\jars\classes.jar"
      resolved="androidx.work:work-runtime:2.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0845ad2cb2b2447decc3bb48754d66bf\transformed\dynamicanimation-1.0.0\jars\classes.jar"
      resolved="androidx.dynamicanimation:dynamicanimation:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0845ad2cb2b2447decc3bb48754d66bf\transformed\dynamicanimation-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f8923ccca9bc9c475bff05ec6a0afba2\transformed\legacy-support-core-utils-1.0.0\jars\classes.jar"
      resolved="androidx.legacy:legacy-support-core-utils:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f8923ccca9bc9c475bff05ec6a0afba2\transformed\legacy-support-core-utils-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\37c9fa3b7c8e77334333447bae5f1b85\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\37c9fa3b7c8e77334333447bae5f1b85\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ea1c800477ce1b3a767b6ea904c5be97\transformed\lifecycle-livedata-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ea1c800477ce1b3a767b6ea904c5be97\transformed\lifecycle-livedata-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\cef52979e41f21c52c2ea8c565eba0fd\transformed\lifecycle-viewmodel-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5f3a99595410223d342783841888f712\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ed7ca423da9b209cd5734a0b83a7b176\transformed\jetified-core-ktx-1.12.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ed7ca423da9b209cd5734a0b83a7b176\transformed\jetified-core-ktx-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\06bc9697558447f752b97a507c06bb1b\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\06bc9697558447f752b97a507c06bb1b\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7e01ee8b33fbdfb5ae0ab71b40012f92\transformed\drawerlayout-1.1.1\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7e01ee8b33fbdfb5ae0ab71b40012f92\transformed\drawerlayout-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0\jars\classes.jar"
      resolved="androidx.coordinatorlayout:coordinatorlayout:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\48c0bc05c01b91db76e154bf0589d5bb\transformed\coordinatorlayout-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.recyclerview:recyclerview:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0\jars\classes.jar"
      resolved="androidx.recyclerview:recyclerview:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a614db70bfd78a9cd3f1844282edd760\transformed\recyclerview-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.transition:transition:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0\jars\classes.jar"
      resolved="androidx.transition:transition:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\2ee250af5b9082ea1e2e85e68e589d22\transformed\transition-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c1ccf6ad3c1a3ed7a07102cfe1748e55\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4c192c6c8af2a0e318e157ddf9673a0b\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4c192c6c8af2a0e318e157ddf9673a0b\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f2a22b45a408eabbd10f0ec3a4ed4ba7\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f2a22b45a408eabbd10f0ec3a4ed4ba7\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\65d57ebc8021fcfcca12f6c2e8e7b242\transformed\customview-1.1.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\65d57ebc8021fcfcca12f6c2e8e7b242\transformed\customview-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.12.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\jars\classes.jar"
      resolved="androidx.core:core:1.12.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\df8e87f1265f72089fb8491f41a438a4\transformed\lifecycle-runtime-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9f43da10b30857b33feca34b3cd03c34\transformed\jetified-lifecycle-runtime-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9f43da10b30857b33feca34b3cd03c34\transformed\jetified-lifecycle-runtime-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\60bf26a59901bc3bb9011380fbd89190\transformed\jetified-lifecycle-livedata-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\60bf26a59901bc3bb9011380fbd89190\transformed\jetified-lifecycle-livedata-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e09d87722e8e31fc053c3f58a118f51a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e09d87722e8e31fc053c3f58a118f51a\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.lzy.net:okgo:3.0.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\jars\classes.jar"
      resolved="com.lzy.net:okgo:3.0.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\converter-gson\2.11.0\9d1fe9d1662de0548e08e293041140a8e4026f81\converter-gson-2.11.0.jar"
      resolved="com.squareup.retrofit2:converter-gson:2.11.0"/>
  <library
      name="com.squareup.retrofit2:retrofit:2.11.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.retrofit2\retrofit\2.11.0\6ca8c6caf842271f3232e075519fe04081ef7069\retrofit-2.11.0.jar"
      resolved="com.squareup.retrofit2:retrofit:2.11.0"/>
  <library
      name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\logging-interceptor\4.12.0\e922c1f14d365c0f2bed140cc0825e18462c2778\logging-interceptor-4.12.0.jar"
      resolved="com.squareup.okhttp3:logging-interceptor:4.12.0"/>
  <library
      name="com.aliyun.alink.linksdk:lp-iot-linkkit:*******@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-*******\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-iot-linkkit:*******"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7d78d3da81bb6f8253ff6eae65f505b9\transformed\jetified-lp-iot-linkkit-*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:lp-iot-device-manager:********@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-********\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-iot-device-manager:********"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\81829f000756b83ac96a389d07d95466\transformed\jetified-lp-iot-device-manager-********"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:lp-network-core:*******@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.aliyun.alink.linksdk\lp-network-core\*******\47647fad85a9a739af21362f99e8f94eed4182fc\lp-network-core-*******.jar"
      resolved="com.aliyun.alink.linksdk:lp-network-core:*******"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"/>
  <library
      name="com.airdoc.component:common:0.2.11-SNAPSHOT@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\jars\classes.jar"
      resolved="com.airdoc.component:common:0.2.11-SNAPSHOT"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-parcelize-runtime\1.9.22\de4a21d6560cadd035c69ba3af3ad1afecc95299\kotlin-parcelize-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6e8f5581e8c0d1be90b1111773aeb1ef\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6e8f5581e8c0d1be90b1111773aeb1ef\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fb7343f306845a798c394ec294e9cc5b\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-exoplayer:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-exoplayer:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\efd467293e0b9b5a51777a2be79e83eb\transformed\jetified-media3-exoplayer-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-common:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-common:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7a53940ce7983efb289ed3d9246f4a3b\transformed\jetified-media3-common-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0168da5f54e3519cfdce27f2e9820640\transformed\jetified-annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0168da5f54e3519cfdce27f2e9820640\transformed\jetified-annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.1.0\f807b2f366f7b75142a67d2f3c10031065b5168\collection-ktx-1.1.0.jar"
      resolved="androidx.collection:collection-ktx:1.1.0"/>
  <library
      name="no.nordicsemi.android:ble:2.2.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4\jars\classes.jar"
      resolved="no.nordicsemi.android:ble:2.2.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b28b1524e2778f44671542e19c926bed\transformed\jetified-ble-2.2.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\772c204bca40e9f044202cb0c35a6a24\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\772c204bca40e9f044202cb0c35a6a24\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cardview:cardview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\be756bf3eb4e1c85b3fe0da5bfd9d804\transformed\cardview-1.0.0\jars\classes.jar"
      resolved="androidx.cardview:cardview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\be756bf3eb4e1c85b3fe0da5bfd9d804\transformed\cardview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.github.bumptech.glide:gifdecoder:4.15.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1\jars\classes.jar"
      resolved="com.github.bumptech.glide:gifdecoder:4.15.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8528862a611804d35d26316bb37d3b92\transformed\jetified-gifdecoder-4.15.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-backend-cct:2.3.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-runtime:2.2.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.android.datatransport:transport-api:2.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1\jars\classes.jar"
      resolved="com.google.android.datatransport:transport-api:2.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a33641c1b8e8c40de42d22eab0154eb5\transformed\jetified-transport-api-2.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-components:16.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-components:16.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b81fee8ac482d588760b3e70ccd1be41\transformed\jetified-firebase-components-16.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0\jars\classes.jar"
      resolved="com.google.firebase:firebase-encoders-json:17.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7be3f3db6c752ace90a9d984f67f1bed\transformed\jetified-firebase-encoders-json-17.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-encoders:16.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-encoders\16.1.0\267565db8531da1483692e27eb58f93ec894c78\firebase-encoders-16.1.0.jar"
      resolved="com.google.firebase:firebase-encoders:16.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4b5777ed0d4bffa3f904e5b76ee57f60\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.2.0\34dbc21d203cc4d4d623ac572a21acd4ccd716af\collection-1.2.0.jar"
      resolved="androidx.collection:collection:1.2.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7dcaaec5891935906304315c5e8854e0\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7dcaaec5891935906304315c5e8854e0\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\709da5e1d2fd883e92f87ad1d3d223db\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\709da5e1d2fd883e92f87ad1d3d223db\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.documentfile:documentfile:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\8e33cfc4915e44ae2c9dba5d1887dc68\transformed\documentfile-1.0.0\jars\classes.jar"
      resolved="androidx.documentfile:documentfile:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\8e33cfc4915e44ae2c9dba5d1887dc68\transformed\documentfile-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d822517d86f4768802c1e5413aee44b7\transformed\localbroadcastmanager-1.0.0\jars\classes.jar"
      resolved="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d822517d86f4768802c1e5413aee44b7\transformed\localbroadcastmanager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.print:print:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b9cce644cafebd40ffd9b00f6518ccfc\transformed\print-1.0.0\jars\classes.jar"
      resolved="androidx.print:print:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b9cce644cafebd40ffd9b00f6518ccfc\transformed\print-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-android-extensions-runtime\1.9.22\ee3bc0c3b55cb516ac92d6a093e1b939166b86a2\kotlin-android-extensions-runtime-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.22\d6c44cd08d8f3f9bece8101216dbe6553365c6e3\kotlin-stdlib-1.9.22.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.22"/>
  <library
      name="io.getstream:stream-log:1.1.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.getstream\stream-log\1.1.4\cdf79e99d4290870dd8f7d902d0f9a7903e35ce7\stream-log-1.1.4.jar"
      resolved="io.getstream:stream-log:1.1.4"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\50ccd012f8006cd9a6316ace317828ed\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.java-websocket:Java-WebSocket:1.5.7@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.java-websocket\Java-WebSocket\1.5.7\ae98d3490567504af347bd6b24f4a0a520f24cc4\Java-WebSocket-1.5.7.jar"
      resolved="org.java-websocket:Java-WebSocket:1.5.7"/>
  <library
      name="me.jessyan:autosize:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\jars\classes.jar"
      resolved="me.jessyan:autosize:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.multidex:multidex:2.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\52482d92e32156ad3b52bb767e08bae5\transformed\multidex-2.0.1\jars\classes.jar"
      resolved="androidx.multidex:multidex:2.0.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\52482d92e32156ad3b52bb767e08bae5\transformed\multidex-2.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.github.jeremyliao:lebx-processor-gson:1.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9aed58bccf8b32935a6e7dd7029d817a\transformed\jetified-lebx-processor-gson-1.8.0\jars\classes.jar"
      resolved="io.github.jeremyliao:lebx-processor-gson:1.8.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9aed58bccf8b32935a6e7dd7029d817a\transformed\jetified-lebx-processor-gson-1.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:lp-public-tmp:2.0.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-public-tmp:2.0.5"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:lp-public-cmp:*******@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-*******\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-public-cmp:*******"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9805520c9e9935306579be110e607219\transformed\jetified-lp-public-cmp-*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:iot-apiclient:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.aliyun.alink.linksdk\iot-apiclient\1.0.1\6645aa8a49b05519326af0fc95f416cd8b375397\iot-apiclient-1.0.1.jar"
      resolved="com.aliyun.alink.linksdk:iot-apiclient:1.0.1"/>
  <library
      name="com.aliyun.alink.linksdk:lp-connectsdk:1.0.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-connectsdk:1.0.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\12b3884315fe7041a0ff571808979902\transformed\jetified-lp-connectsdk-1.0.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.gson:gson:2.10.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar"
      resolved="com.google.code.gson:gson:2.10.1"/>
  <library
      name="com.tencent:mmkv-static:1.2.16@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16\jars\classes.jar"
      resolved="com.tencent:mmkv-static:1.2.16"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e0558eea8bbe483897f828c22b0e5ef0\transformed\jetified-mmkv-static-1.2.16"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="jp.wasabeef:glide-transformations:4.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0\jars\classes.jar"
      resolved="jp.wasabeef:glide-transformations:4.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9254240051e84bb84ac5061829e25df2\transformed\jetified-glide-transformations-4.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.zhy:okhttputils:2.6.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d045de4695537f487ca01dc3ca7c12ca\transformed\jetified-okhttputils-2.6.2\jars\classes.jar"
      resolved="com.zhy:okhttputils:2.6.2"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d045de4695537f487ca01dc3ca7c12ca\transformed\jetified-okhttputils-2.6.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.airbnb.android:lottie:6.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0\jars\classes.jar"
      resolved="com.airbnb.android:lottie:6.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1a7dc3ec56c635a7873ef2281b1cede4\transformed\jetified-lottie-6.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:guava:32.1.3-android@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.1.3-android\ea090dd85ca2fa12d42d054369df888665230dd7\guava-32.1.3-android.jar"
      resolved="com.google.guava:guava:32.1.3-android"/>
  <library
      name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\84b527003f3a0e5a3267cc12425ea2d6\transformed\jetified-MPAndroidChart-v3.1.0\jars\classes.jar"
      resolved="com.github.PhilJay:MPAndroidChart:v3.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\84b527003f3a0e5a3267cc12425ea2d6\transformed\jetified-MPAndroidChart-v3.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-ui:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-ui:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\37eb7cb3503359f8e3891a2adf804078\transformed\jetified-media3-ui-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar"
      resolved="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\83cce0b53247cf5607abf9a7fd5167ef\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\377b6447c16dbc5d000cfe1af300650d\transformed\jetified-tracing-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\377b6447c16dbc5d000cfe1af300650d\transformed\jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="com.google.errorprone:error_prone_annotations:2.21.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.21.1\6d9b10773b5237df178a7b3c1b4208df7d0e7f94\error_prone_annotations-2.21.1.jar"
      resolved="com.google.errorprone:error_prone_annotations:2.21.1"/>
  <library
      name="com.google.guava:failureaccess:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar"
      resolved="com.google.guava:failureaccess:1.0.1"/>
  <library
      name="com.google.code.findbugs:jsr305:3.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:3.0.2"/>
  <library
      name="org.checkerframework:checker-qual:3.37.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.37.0\ba74746d38026581c12166e164bb3c15e90cc4ea\checker-qual-3.37.0.jar"
      resolved="org.checkerframework:checker-qual:3.37.0"/>
  <library
      name="com.github.bumptech.glide:disklrucache:4.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\disklrucache\4.15.1\e47ade981aefb5b975382750490d195fa569bbdf\disklrucache-4.15.1.jar"
      resolved="com.github.bumptech.glide:disklrucache:4.15.1"/>
  <library
      name="com.github.bumptech.glide:annotations:4.15.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.bumptech.glide\annotations\4.15.1\d3721a4986a833e4ea71126489d12e08964b6f07\annotations-4.15.1.jar"
      resolved="com.github.bumptech.glide:annotations:4.15.1"/>
  <library
      name="androidx.exifinterface:exifinterface:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\de074a4a358f597debea3416bcaa465b\transformed\exifinterface-1.3.6\jars\classes.jar"
      resolved="androidx.exifinterface:exifinterface:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\de074a4a358f597debea3416bcaa465b\transformed\exifinterface-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.slf4j:slf4j-api:2.0.6@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.6\88c40d8b4f33326f19a7d3c0aaf2c7e8721d4953\slf4j-api-2.0.6.jar"
      resolved="org.slf4j:slf4j-api:2.0.6"/>
  <library
      name="androidx.media3:media3-container:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fe8e4784db1986d494d8c00c089bb056\transformed\jetified-media3-container-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-container:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fe8e4784db1986d494d8c00c089bb056\transformed\jetified-media3-container-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-database:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f44bfcd6e0d1777129c96309cc8d8cd7\transformed\jetified-media3-database-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-database:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f44bfcd6e0d1777129c96309cc8d8cd7\transformed\jetified-media3-database-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-datasource:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-datasource:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\88dbb572ec5c7cefe6d224189160b960\transformed\jetified-media3-datasource-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-decoder:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\83aa90154ad1dd42a85ec1dfee1708ab\transformed\jetified-media3-decoder-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-decoder:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\83aa90154ad1dd42a85ec1dfee1708ab\transformed\jetified-media3-decoder-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media3:media3-extractor:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1\jars\classes.jar"
      resolved="androidx.media3:media3-extractor:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fc8f3dcb384bdb4af8140e654e58b5f6\transformed\jetified-media3-extractor-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:lp-public-channel-core:*******@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-*******\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:lp-public-channel-core:*******"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\66dca75771be273545d8101ea90a7820\transformed\jetified-lp-public-channel-core-*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:android_alink_id2:1.1.3@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:android_alink_id2:1.1.3"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:tools:*******@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-*******\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:tools:*******"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1c9ad81920ad96590b6c7c72106da5b5\transformed\jetified-tools-*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.alibaba:fastjson:1.2.40@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.alibaba\fastjson\1.2.40\c4006cf005d83676144264ad018e6dcff8658c16\fastjson-1.2.40.jar"
      resolved="com.alibaba:fastjson:1.2.40"/>
  <library
      name="com.aliyun.alink.linksdk:api-client-biz:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:api-client-biz:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7b5c364f047faafa411b81befedb2ecf\transformed\jetified-api-client-biz-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\31345907932cb16ab07cdd18e821ff6d\transformed\jetified-public-alcs-cmp-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0e43030a83b65b1cf7711375223358c6\transformed\jetified-android_alcs_lpbs-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3d8ec38105a6c5713fc8f00515a94451\transformed\jetified-coap-sdk-1.0.2sp1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:public-channel-gateway:1.6.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:public-channel-gateway:1.6.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\89a67f32f2b6dbb5229bb80cd6225108\transformed\jetified-public-channel-gateway-1.6.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:opensource-paho:*******@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-*******\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:opensource-paho:*******"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f3424d509a08750eee3f2c151cac5884\transformed\jetified-opensource-paho-*******"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:iot-h2-stream:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:iot-h2-stream:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ffb78f1d2813c7eaae96662549550197\transformed\jetified-iot-h2-stream-1.1.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.aliyun.alink.linksdk:iot-h2:1.1.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6\jars\classes.jar"
      resolved="com.aliyun.alink.linksdk:iot-h2:1.1.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9ae93d416fb6b56551d4040db1b3ec30\transformed\jetified-iot-h2-1.1.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="commons-codec:commons-codec:1.11@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.11\3acb4705652e16236558f0f4f2192cc33c3bd189\commons-codec-1.11.jar"
      resolved="commons-codec:commons-codec:1.11"/>
  <library
      name="io.netty:netty-all:4.1.23.Final@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-all\4.1.23.Final\e5e42b90fb9232c43eb79a43941f0a02ab87ba37\netty-all-4.1.23.Final.jar"
      resolved="io.netty:netty-all:4.1.23.Final"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="com.google.android.odml:image:1.0.0-beta1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1\jars\classes.jar"
      resolved="com.google.android.odml:image:1.0.0-beta1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\a6a3a9b2b75d99a97087666cb907f0ff\transformed\jetified-image-1.0.0-beta1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.firebase:firebase-annotations:16.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.firebase\firebase-annotations\16.0.0\dbeae20d6c97b747b59ef47b6dcf770ba1a60fa6\firebase-annotations-16.0.0.jar"
      resolved="com.google.firebase:firebase-annotations:16.0.0"/>
  <library
      name="com.google.j2objc:j2objc-annotations:2.8@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar"
      resolved="com.google.j2objc:j2objc-annotations:2.8"
      provided="true"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\fe94a204f7553dc38c178bdc8bcdfa60\transformed\jetified-emoji2-views-helper-1.2.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\fe94a204f7553dc38c178bdc8bcdfa60\transformed\jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4273d528f5a5d74e4a86eb85c2428961\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4273d528f5a5d74e4a86eb85c2428961\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.media:media:1.7.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0\jars\classes.jar"
      resolved="androidx.media:media:1.7.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\afc9fcec63ee624454c543e8d82e4d76\transformed\media-1.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures-ktx\1.1.0\b4c245baf36d1a9e7defaf3be84f7a2ad4e1c797\concurrent-futures-ktx-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures-ktx:1.1.0"/>
  <library
      name="androidx.room:room-ktx:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5f165c8addaa916cc537873b884351d0\transformed\jetified-room-ktx-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5f165c8addaa916cc537873b884351d0\transformed\jetified-room-ktx-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup.okhttp3:okhttp-sse:4.10.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp-sse\4.10.0\b46942ca331d7f8a7b99a7b8d02c675dfdc6df68\okhttp-sse-4.10.0.jar"
      resolved="com.squareup.okhttp3:okhttp-sse:4.10.0"/>
  <library
      name="io.getstream:stream-log-file:1.1.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.getstream\stream-log-file\1.1.4\65f22da7665e561f9f9d224f4d16273c81f10f0c\stream-log-file-1.1.4.jar"
      resolved="io.getstream:stream-log-file:1.1.4"/>
  <library
      name="com.orhanobut:logger:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\1a4aaf90b674c44aae35bd13a5aeec7b\transformed\jetified-logger-2.2.0\jars\classes.jar"
      resolved="com.orhanobut:logger:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\1a4aaf90b674c44aae35bd13a5aeec7b\transformed\jetified-logger-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing-ktx:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7c75f16fb3532e702d63c57107fce1c7\transformed\jetified-tracing-ktx-1.2.0\jars\classes.jar"
      resolved="androidx.tracing:tracing-ktx:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7c75f16fb3532e702d63c57107fce1c7\transformed\jetified-tracing-ktx-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.room:room-runtime:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.5.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0e4ce545fcd52fce8e813db51c5cadbd\transformed\sqlite-framework-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0e4ce545fcd52fce8e813db51c5cadbd\transformed\sqlite-framework-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.5.0\829a83fb92f1696a8a32f3beea884dfc87b2693\room-common-2.5.0.jar"
      resolved="androidx.room:room-common:2.5.0"/>
  <library
      name="androidx.sqlite:sqlite:2.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4f15a1f0b657363ac1155c0f1898a532\transformed\sqlite-2.3.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4f15a1f0b657363ac1155c0f1898a532\transformed\sqlite-2.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.auto.value\auto-value-annotations\1.6.3\b88c1bb7f149f6d2cc03898359283e57b08f39cc\auto-value-annotations-1.6.3.jar"
      resolved="com.google.auto.value:auto-value-annotations:1.6.3"/>
</libraries>
