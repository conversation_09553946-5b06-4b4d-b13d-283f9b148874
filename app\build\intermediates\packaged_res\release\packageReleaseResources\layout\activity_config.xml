<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_detection_bg">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_sn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="SN：123456"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:layout_marginTop="40dp"
            android:layout_marginStart="20dp"/>

        <LinearLayout
            android:id="@+id/ll_domain"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:orientation="horizontal"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_environment_configuration"
                android:textColor="@color/color_333333"
                android:textSize="15sp" />

            <Spinner
                android:id="@+id/sp_domain_name"
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:entries="@array/domain_name"
                android:layout_marginStart="10dp"
                android:focusable="true"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_display_viewpoint"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tv_display_viewpoint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_display_viewpoint"
                android:textColor="@color/color_333333"
                android:textSize="15sp"
                android:layout_marginStart="10dp"
                android:includeFontPadding="false" />

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_display_viewpoint"
                android:layout_width="70dp"
                android:layout_height="28dp"
                app:switchMinWidth="70dp"
                android:splitTrack="true"
                android:thumb="@drawable/switch_mask_therapy_thumb"
                app:track="@drawable/switch_mask_therapy_style"
                android:thumbTint="@color/white"
                app:thumbTint="@color/white"
                android:trackTint="@color/selector_common_switch_compat_track_tint"
                app:trackTint="@color/selector_common_switch_compat_track_tint"
                android:layout_marginEnd="5dp"
                android:layout_marginStart="5dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_mqtt_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="MQTT状态：未连接"
            android:textColor="@color/color_333333"
            android:textSize="20sp"
            android:layout_marginTop="10dp"
            android:layout_marginStart="20dp"
            android:visibility="gone"/>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>