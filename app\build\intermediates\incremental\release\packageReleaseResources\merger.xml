<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\main\res"><file name="anim_common_loading" path="D:\mpd_app_dev\app\src\main\res\anim\anim_common_loading.xml" qualifiers="" type="anim"/><file name="color_selector_param_setting_mode" path="D:\mpd_app_dev\app\src\main\res\color\color_selector_param_setting_mode.xml" qualifiers="" type="color"/><file name="selector_common_radio_button_tint" path="D:\mpd_app_dev\app\src\main\res\color\selector_common_radio_button_tint.xml" qualifiers="" type="color"/><file name="selector_common_switch_compat_track_tint" path="D:\mpd_app_dev\app\src\main\res\color\selector_common_switch_compat_track_tint.xml" qualifiers="" type="color"/><file name="selector_input_gender_radio_button_tint" path="D:\mpd_app_dev\app\src\main\res\color\selector_input_gender_radio_button_tint.xml" qualifiers="" type="color"/><file name="selector_input_gender_text_color" path="D:\mpd_app_dev\app\src\main\res\color\selector_input_gender_text_color.xml" qualifiers="" type="color"/><file name="app_launcher_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\app_launcher_bg.xml" qualifiers="" type="drawable"/><file name="common_8d82c6_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_8d82c6_round_bg.xml" qualifiers="" type="drawable"/><file name="common_8f959e_stroke_20_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_8f959e_stroke_20_bg.xml" qualifiers="" type="drawable"/><file name="common_black_20_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_black_20_round_bg.xml" qualifiers="" type="drawable"/><file name="common_blue_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_blue_round_bg.xml" qualifiers="" type="drawable"/><file name="common_ce005c_stroke_20_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_ce005c_stroke_20_bg.xml" qualifiers="" type="drawable"/><file name="common_d6dce1_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_d6dce1_round_bg.xml" qualifiers="" type="drawable"/><file name="common_d7dce9_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_d7dce9_round_bg.xml" qualifiers="" type="drawable"/><file name="common_ea4e3d_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_ea4e3d_round_bg.xml" qualifiers="" type="drawable"/><file name="common_eb4e89_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_eb4e89_round_bg.xml" qualifiers="" type="drawable"/><file name="common_eff3f6_round_20_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_eff3f6_round_20_bg.xml" qualifiers="" type="drawable"/><file name="common_gray_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_gray_round_bg.xml" qualifiers="" type="drawable"/><file name="common_green_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_green_round_bg.xml" qualifiers="" type="drawable"/><file name="common_light_blue_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_light_blue_round_bg.xml" qualifiers="" type="drawable"/><file name="common_red_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_red_round_bg.xml" qualifiers="" type="drawable"/><file name="common_stroke_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_stroke_round_bg.xml" qualifiers="" type="drawable"/><file name="common_white_20_round_20_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_white_20_round_20_bg.xml" qualifiers="" type="drawable"/><file name="common_white_round_15_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_white_round_15_bg.xml" qualifiers="" type="drawable"/><file name="common_white_round_20_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_white_round_20_bg.xml" qualifiers="" type="drawable"/><file name="common_white_round_25_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_white_round_25_bg.xml" qualifiers="" type="drawable"/><file name="common_white_round_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\common_white_round_bg.xml" qualifiers="" type="drawable"/><file name="config_switch_mask_therapy_selected" path="D:\mpd_app_dev\app\src\main\res\drawable\config_switch_mask_therapy_selected.xml" qualifiers="" type="drawable"/><file name="config_switch_mask_therapy_unselected" path="D:\mpd_app_dev\app\src\main\res\drawable\config_switch_mask_therapy_unselected.xml" qualifiers="" type="drawable"/><file name="finish_read_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\finish_read_bg.xml" qualifiers="" type="drawable"/><file name="gradient_pink_background" path="D:\mpd_app_dev\app\src\main\res\drawable\gradient_pink_background.xml" qualifiers="" type="drawable"/><file name="icon_advanced_settings" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_advanced_settings.xml" qualifiers="" type="drawable"/><file name="icon_airdoc_digital_therapy_center" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_airdoc_digital_therapy_center.xml" qualifiers="" type="drawable"/><file name="icon_ai_train_guide_instructions" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_ai_train_guide_instructions.xml" qualifiers="" type="drawable"/><file name="icon_back_black_coarse" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_back_black_coarse.xml" qualifiers="" type="drawable"/><file name="icon_back_black_fine" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_back_black_fine.xml" qualifiers="" type="drawable"/><file name="icon_back_fine_line_arrow" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_back_fine_line_arrow.xml" qualifiers="" type="drawable"/><file name="icon_back_white_coarse" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_back_white_coarse.xml" qualifiers="" type="drawable"/><file name="icon_calibration_m" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_calibration_m.xml" qualifiers="" type="drawable"/><file name="icon_close_airdoc_ai" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_close_airdoc_ai.xml" qualifiers="" type="drawable"/><file name="icon_device_exception_menu" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_device_exception_menu.xml" qualifiers="" type="drawable"/><file name="icon_help_center_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_help_center_bg.xml" qualifiers="" type="drawable"/><file name="icon_launcher_network_anomaly" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_launcher_network_anomaly.xml" qualifiers="" type="drawable"/><file name="icon_login_account_number" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_login_account_number.xml" qualifiers="" type="drawable"/><file name="icon_login_password" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_login_password.xml" qualifiers="" type="drawable"/><file name="icon_main_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_main_bg.xml" qualifiers="" type="drawable"/><file name="icon_new_patient" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_new_patient.xml" qualifiers="" type="drawable"/><file name="icon_param_preview_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_param_preview_bg.xml" qualifiers="" type="drawable"/><file name="icon_patient_library" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_patient_library.xml" qualifiers="" type="drawable"/><file name="icon_read_assessment_report" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_read_assessment_report.xml" qualifiers="" type="drawable"/><file name="icon_read_content" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_read_content.xml" qualifiers="" type="drawable"/><file name="icon_read_main_ai_logo" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_read_main_ai_logo.xml" qualifiers="" type="drawable"/><file name="icon_read_main_logo" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_read_main_logo.xml" qualifiers="" type="drawable"/><file name="icon_settings_unselect" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_settings_unselect.xml" qualifiers="" type="drawable"/><file name="icon_tsc_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_tsc_bg.xml" qualifiers="" type="drawable"/><file name="icon_version" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_version.xml" qualifiers="" type="drawable"/><file name="icon_visual_train_no_open" path="D:\mpd_app_dev\app\src\main\res\drawable\icon_visual_train_no_open.xml" qualifiers="" type="drawable"/><file name="ic_cross" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_cross.xml" qualifiers="" type="drawable"/><file name="ic_language_en_us" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_language_en_us.xml" qualifiers="" type="drawable"/><file name="ic_language_settings" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_language_settings.xml" qualifiers="" type="drawable"/><file name="ic_language_zh_cn" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_language_zh_cn.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_more_settings" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_more_settings.xml" qualifiers="" type="drawable"/><file name="ic_scanner_settings" path="D:\mpd_app_dev\app\src\main\res\drawable\ic_scanner_settings.xml" qualifiers="" type="drawable"/><file name="input_cursor" path="D:\mpd_app_dev\app\src\main\res\drawable\input_cursor.xml" qualifiers="" type="drawable"/><file name="login_input_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\login_input_bg.xml" qualifiers="" type="drawable"/><file name="main_bt_common_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\main_bt_common_bg.xml" qualifiers="" type="drawable"/><file name="main_input_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\main_input_bg.xml" qualifiers="" type="drawable"/><file name="read_common_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\read_common_bg.xml" qualifiers="" type="drawable"/><file name="read_count_down_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\read_count_down_bg.xml" qualifiers="" type="drawable"/><file name="read_speed_standard_annotation_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\read_speed_standard_annotation_bg.xml" qualifiers="" type="drawable"/><file name="rounded_pink_button" path="D:\mpd_app_dev\app\src\main\res\drawable\rounded_pink_button.xml" qualifiers="" type="drawable"/><file name="seekbar_shaded_area_progress_drawable" path="D:\mpd_app_dev\app\src\main\res\drawable\seekbar_shaded_area_progress_drawable.xml" qualifiers="" type="drawable"/><file name="seekbar_shaded_area_thumb" path="D:\mpd_app_dev\app\src\main\res\drawable\seekbar_shaded_area_thumb.xml" qualifiers="" type="drawable"/><file name="selector_check_protocol_red" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_check_protocol_red.xml" qualifiers="" type="drawable"/><file name="selector_common_radio_button" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_common_radio_button.xml" qualifiers="" type="drawable"/><file name="selector_input_gender_radio_button" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_input_gender_radio_button.xml" qualifiers="" type="drawable"/><file name="selector_param_setting_mode_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_param_setting_mode_bg.xml" qualifiers="" type="drawable"/><file name="selector_param_setting_scale_icon" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_param_setting_scale_icon.xml" qualifiers="" type="drawable"/><file name="selector_play_read_track_head_map_icon" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_play_read_track_head_map_icon.xml" qualifiers="" type="drawable"/><file name="selector_read_init_tab_bg" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_read_init_tab_bg.xml" qualifiers="" type="drawable"/><file name="selector_read_track_figure_icon" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_read_track_figure_icon.xml" qualifiers="" type="drawable"/><file name="selector_read_track_head_map_icon" path="D:\mpd_app_dev\app\src\main\res\drawable\selector_read_track_head_map_icon.xml" qualifiers="" type="drawable"/><file name="switch_behavior_guidance_style" path="D:\mpd_app_dev\app\src\main\res\drawable\switch_behavior_guidance_style.xml" qualifiers="" type="drawable"/><file name="switch_behavior_guidance_thumb" path="D:\mpd_app_dev\app\src\main\res\drawable\switch_behavior_guidance_thumb.xml" qualifiers="" type="drawable"/><file name="switch_mask_therapy_style" path="D:\mpd_app_dev\app\src\main\res\drawable\switch_mask_therapy_style.xml" qualifiers="" type="drawable"/><file name="switch_mask_therapy_thumb" path="D:\mpd_app_dev\app\src\main\res\drawable\switch_mask_therapy_thumb.xml" qualifiers="" type="drawable"/><file name="update_progress_drawable" path="D:\mpd_app_dev\app\src\main\res\drawable\update_progress_drawable.xml" qualifiers="" type="drawable"/><file name="ic_main_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-en-hdpi\ic_main_bg.webp" qualifiers="en-hdpi-v4" type="drawable"/><file name="ic_main_bg_1" path="D:\mpd_app_dev\app\src\main\res\drawable-en-hdpi\ic_main_bg_1.webp" qualifiers="en-hdpi-v4" type="drawable"/><file name="ic_main_logo" path="D:\mpd_app_dev\app\src\main\res\drawable-en-hdpi\ic_main_logo.webp" qualifiers="en-hdpi-v4" type="drawable"/><file name="ic_close_scan" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_close_scan.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_configuration_exception" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_configuration_exception.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_detection_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_detection_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_detection_hrv_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_detection_hrv_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_device_info" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_device_info.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_device_info_dialog_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_device_info_dialog_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_device_info_dialog_content_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_device_info_dialog_content_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_device_info_dialog_left" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_device_info_dialog_left.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_device_info_dialog_right" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_device_info_dialog_right.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_female_avatar_round" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_female_avatar_round.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_input_age" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_input_age.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_input_gender" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_input_gender.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_input_name" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_input_name.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_input_phone" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_input_phone.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_loading" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_loading.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_main_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_main_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_main_bg_1" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_main_bg_1.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_main_logo" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_main_logo.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_male_avatar_round" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_male_avatar_round.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_menu" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_menu.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_network_exception" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_network_exception.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_red_dot" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_red_dot.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_refresh_red" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_refresh_red.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_refresh_white" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_refresh_white.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_scan" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_scan.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_scan_anim" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_scan_anim.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_scan_camera_tips" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_scan_camera_tips.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_scan_frame" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_scan_frame.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_update_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_update_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_user_info_bg" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_user_info_bg.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_version" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_version.webp" qualifiers="hdpi-v4" type="drawable"/><file name="ic_view_report" path="D:\mpd_app_dev\app\src\main\res\drawable-hdpi\ic_view_report.webp" qualifiers="hdpi-v4" type="drawable"/><file name="activity_calibration" path="D:\mpd_app_dev\app\src\main\res\layout\activity_calibration.xml" qualifiers="" type="layout"/><file name="activity_config" path="D:\mpd_app_dev\app\src\main\res\layout\activity_config.xml" qualifiers="" type="layout"/><file name="activity_detection" path="D:\mpd_app_dev\app\src\main\res\layout\activity_detection.xml" qualifiers="" type="layout"/><file name="activity_detection1" path="D:\mpd_app_dev\app\src\main\res\layout\activity_detection1.xml" qualifiers="" type="layout"/><file name="activity_detection_web" path="D:\mpd_app_dev\app\src\main\res\layout\activity_detection_web.xml" qualifiers="" type="layout"/><file name="activity_hrv" path="D:\mpd_app_dev\app\src\main\res\layout\activity_hrv.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\mpd_app_dev\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_more_settings" path="D:\mpd_app_dev\app\src\main\res\layout\activity_more_settings.xml" qualifiers="" type="layout"/><file name="activity_scan" path="D:\mpd_app_dev\app\src\main\res\layout\activity_scan.xml" qualifiers="" type="layout"/><file name="activity_update" path="D:\mpd_app_dev\app\src\main\res\layout\activity_update.xml" qualifiers="" type="layout"/><file name="dialog_common_loading" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_common_loading.xml" qualifiers="" type="layout"/><file name="dialog_device_info" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_device_info.xml" qualifiers="" type="layout"/><file name="dialog_device_reminder" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_device_reminder.xml" qualifiers="" type="layout"/><file name="dialog_language_settings" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_language_settings.xml" qualifiers="" type="layout"/><file name="dialog_selection_age" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_selection_age.xml" qualifiers="" type="layout"/><file name="dialog_startup_mode_settings" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_startup_mode_settings.xml" qualifiers="" type="layout"/><file name="dialog_update" path="D:\mpd_app_dev\app\src\main\res\layout\dialog_update.xml" qualifiers="" type="layout"/><file name="fragment_detection_code_detection" path="D:\mpd_app_dev\app\src\main\res\layout\fragment_detection_code_detection.xml" qualifiers="" type="layout"/><file name="fragment_device_exception" path="D:\mpd_app_dev\app\src\main\res\layout\fragment_device_exception.xml" qualifiers="" type="layout"/><file name="fragment_input_info_detection" path="D:\mpd_app_dev\app\src\main\res\layout\fragment_input_info_detection.xml" qualifiers="" type="layout"/><file name="fragment_scan_code_detection" path="D:\mpd_app_dev\app\src\main\res\layout\fragment_scan_code_detection.xml" qualifiers="" type="layout"/><file name="item_detection" path="D:\mpd_app_dev\app\src\main\res\layout\item_detection.xml" qualifiers="" type="layout"/><file name="item_language_settings" path="D:\mpd_app_dev\app\src\main\res\layout\item_language_settings.xml" qualifiers="" type="layout"/><file name="layout_menu_popup_window" path="D:\mpd_app_dev\app\src\main\res\layout\layout_menu_popup_window.xml" qualifiers="" type="layout"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-en-hdpi\ic_launcher.webp" qualifiers="en-hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-en-mdpi\ic_launcher.webp" qualifiers="en-mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-en-xhdpi\ic_launcher.webp" qualifiers="en-xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-en-xxhdpi\ic_launcher.webp" qualifiers="en-xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-en-xxxhdpi\ic_launcher.webp" qualifiers="en-xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\mpd_app_dev\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\mpd_app_dev\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="detection_code_last_number" path="D:\mpd_app_dev\app\src\main\res\raw\detection_code_last_number.mp3" qualifiers="" type="raw"/><file name="mobile_browser" path="D:\mpd_app_dev\app\src\main\res\raw\mobile_browser.mp3" qualifiers="" type="raw"/><file name="phone_last_number" path="D:\mpd_app_dev\app\src\main\res\raw\phone_last_number.mp3" qualifiers="" type="raw"/><file name="please_enter_detection_code" path="D:\mpd_app_dev\app\src\main\res\raw\please_enter_detection_code.mp3" qualifiers="" type="raw"/><file name="please_use" path="D:\mpd_app_dev\app\src\main\res\raw\please_use.mp3" qualifiers="" type="raw"/><file name="proceed_detection" path="D:\mpd_app_dev\app\src\main\res\raw\proceed_detection.mp3" qualifiers="" type="raw"/><file name="scan_code" path="D:\mpd_app_dev\app\src\main\res\raw\scan_code.mp3" qualifiers="" type="raw"/><file name="speech_0" path="D:\mpd_app_dev\app\src\main\res\raw\speech_0.mp3" qualifiers="" type="raw"/><file name="speech_1" path="D:\mpd_app_dev\app\src\main\res\raw\speech_1.mp3" qualifiers="" type="raw"/><file name="speech_2" path="D:\mpd_app_dev\app\src\main\res\raw\speech_2.mp3" qualifiers="" type="raw"/><file name="speech_3" path="D:\mpd_app_dev\app\src\main\res\raw\speech_3.mp3" qualifiers="" type="raw"/><file name="speech_4" path="D:\mpd_app_dev\app\src\main\res\raw\speech_4.mp3" qualifiers="" type="raw"/><file name="speech_5" path="D:\mpd_app_dev\app\src\main\res\raw\speech_5.mp3" qualifiers="" type="raw"/><file name="speech_6" path="D:\mpd_app_dev\app\src\main\res\raw\speech_6.mp3" qualifiers="" type="raw"/><file name="speech_7" path="D:\mpd_app_dev\app\src\main\res\raw\speech_7.mp3" qualifiers="" type="raw"/><file name="speech_8" path="D:\mpd_app_dev\app\src\main\res\raw\speech_8.mp3" qualifiers="" type="raw"/><file name="speech_9" path="D:\mpd_app_dev\app\src\main\res\raw\speech_9.mp3" qualifiers="" type="raw"/><file name="welcome_use_stress_tolerance_assessment" path="D:\mpd_app_dev\app\src\main\res\raw\welcome_use_stress_tolerance_assessment.mp3" qualifiers="" type="raw"/><file name="we_chat" path="D:\mpd_app_dev\app\src\main\res\raw\we_chat.mp3" qualifiers="" type="raw"/><file name="whatsapp" path="D:\mpd_app_dev\app\src\main\res\raw\whatsapp.mp3" qualifiers="" type="raw"/><file name="detection_code_last_number" path="D:\mpd_app_dev\app\src\main\res\raw-en\detection_code_last_number.mp3" qualifiers="en" type="raw"/><file name="mobile_browser" path="D:\mpd_app_dev\app\src\main\res\raw-en\mobile_browser.mp3" qualifiers="en" type="raw"/><file name="phone_last_number" path="D:\mpd_app_dev\app\src\main\res\raw-en\phone_last_number.mp3" qualifiers="en" type="raw"/><file name="please_enter_detection_code" path="D:\mpd_app_dev\app\src\main\res\raw-en\please_enter_detection_code.mp3" qualifiers="en" type="raw"/><file name="please_use" path="D:\mpd_app_dev\app\src\main\res\raw-en\please_use.mp3" qualifiers="en" type="raw"/><file name="proceed_detection" path="D:\mpd_app_dev\app\src\main\res\raw-en\proceed_detection.mp3" qualifiers="en" type="raw"/><file name="scan_code" path="D:\mpd_app_dev\app\src\main\res\raw-en\scan_code.mp3" qualifiers="en" type="raw"/><file name="speech_0" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_0.mp3" qualifiers="en" type="raw"/><file name="speech_1" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_1.mp3" qualifiers="en" type="raw"/><file name="speech_2" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_2.mp3" qualifiers="en" type="raw"/><file name="speech_3" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_3.mp3" qualifiers="en" type="raw"/><file name="speech_4" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_4.mp3" qualifiers="en" type="raw"/><file name="speech_5" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_5.mp3" qualifiers="en" type="raw"/><file name="speech_6" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_6.mp3" qualifiers="en" type="raw"/><file name="speech_7" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_7.mp3" qualifiers="en" type="raw"/><file name="speech_8" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_8.mp3" qualifiers="en" type="raw"/><file name="speech_9" path="D:\mpd_app_dev\app\src\main\res\raw-en\speech_9.mp3" qualifiers="en" type="raw"/><file name="welcome_use_stress_tolerance_assessment" path="D:\mpd_app_dev\app\src\main\res\raw-en\welcome_use_stress_tolerance_assessment.mp3" qualifiers="en" type="raw"/><file name="we_chat" path="D:\mpd_app_dev\app\src\main\res\raw-en\we_chat.mp3" qualifiers="en" type="raw"/><file path="D:\mpd_app_dev\app\src\main\res\values\arrays.xml" qualifiers=""><string-array name="domain_name">
        <item>RELEASE</item>
        <item>TEST</item>
    </string-array></file><file path="D:\mpd_app_dev\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="color_primary">#FF6200EE</color><color name="color_cccccc">#FFCCCCCC</color><color name="color_999999">#FF999999</color><color name="color_333333">#FF333333</color><color name="color_666666">#FF666666</color><color name="color_4A90E2">#FF4A90E2</color></file><file path="D:\mpd_app_dev\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">鹰瞳抗压能力检测</string><string name="str_main_all_rights_reserved">©2025 Airdoc鹰瞳 版权所有</string><string name="str_wechat_scan_code">微信扫码检测</string><string name="str_settings">设置</string><string name="str_please_enter_name">请输入您的姓名</string><string name="str_id_s">ID：%1$s</string><string name="str_gender_male">男</string><string name="str_gender_female">女</string><string name="str_confidential">保密</string><string name="str_gender_">性别：%1$s</string><string name="str_phone_last_number_">手机尾号：%1$s</string><string name="str_please_enter_age">请输入您的年龄</string><string name="str_please_enter_phone">请输入您的手机号</string><string name="str_start_detection">开始检测</string><string name="str_cancel_detection">退出检测</string><string name="str_exit">退出</string><string name="str_device_info">设备信息</string><string name="str_i_know">我知道了</string><string name="str_opening_date">开通日期：%1$s</string><string name="str_valid_until">有效期至：%1$s</string><string name="str_residual_degree">剩余次数：%1$d次</string><string name="str_customer_service_hotline">客户服务热线：400–100–3999</string><string name="str_network_exception">当前网络异常，检查网络后再试试吧</string><string name="str_configuration_exception">机构配置异常，请联系支持团队</string><string name="str_selection_age">选择年龄</string><string name="str_ok">确定</string><string name="str_cancel">取消</string><string name="str_discovering_new_versions">发现新版本</string><string name="str_details">详情</string><string name="str_experience_now">立即体验</string><string name="str_app_name_s">应用：%1$s</string><string name="str_version_number_s">版本号：%1$s</string><string name="str_app_size_d">应用大小：%1$dM</string><string name="str_upgrade_process_may_take_a_few_minutes">升级过程可能会持续几分钟，请耐心等待。升级完成之前请勿关闭电源。</string><string name="str_get_download_resource_exception">获取下载资源异常</string><string name="str_view_report">查看报告</string><string name="str_hrv_waiting_text">深呼吸，即将开始检测…</string><string name="str_detection_not_available">该项检测尚未开通</string><string name="str_detection_code_loading">检测码加载中…</string><string name="str_device_service_expired">设备服务已到期，\n请联系业务人员续期！</string><string name="str_number_available_times_0">可用次数为0\n请尽快充值后使用! </string><string name="str_environment_configuration">环境配置</string><string name="str_align_qr_code_recognition">对准二维码进行识别</string><string name="str_camera_is_here">摄像头在这里</string><string name="str_please_enter_detection_code">请输入检测码</string><string name="str_detection_code_s">检测码：%1$s</string><string name="str_request_timeout">请求超时</string><string name="str_startup_detection_failed_check_network_and_retry">启动检测失败，检查网络并重试</string><string name="str_language_settings">语言设置</string><string name="str_scanner_settings">扫码设置</string><string name="str_browser_scan">浏览器扫码</string><string name="str_whatsapp_scan">WhatsApp扫码</string><string name="str_select_scan_method">请选择扫码方式</string><string name="str_h5_qr_code_tips">使用浏览器扫码，完善个人信息，邮箱接收报告</string><string name="str_whatsapp_qr_code_tips">使用WhatsApp扫码，完善个人信息，并接受报告推送</string><string name="str_hrv_detection_overview">·90秒完成检测\n·多模态数据采集\n·AI人工智能评估\n·五大核心指标精准评估\n·健康建议助力压力管理</string><string name="str_unknown">未知</string><string name="str_invalid_parameters_required_calibration">参数无效，需要校准</string><string name="str_more_settings">更多设置</string><string name="str_proactively_greet">首页语音引导</string><string name="str_display_viewpoint">显示注视点</string><string name="str_model_version">模型版本</string><string name="str_test_version">测试版版本</string><string name="str_data_cache">数据缓存</string><string name="str_fingertip_data_collection">指尖式数据采集</string><string name="str_collector_number">采集器编号</string><string name="str_update">更新</string><string name="str_upload">上传</string><string name="str_change">变更</string><string name="str_none">无</string><string name="str_version_format">V%1$s</string></file><file path="D:\mpd_app_dev\app\src\main\res\values\styles.xml" qualifiers=""><style name="shape_image_circle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style><style name="shape_image_round_25dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">25dp</item>
    </style><style name="shape_image_top_round_12dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">12dp</item>
        <item name="cornerSizeTopRight">12dp</item>
    </style><style name="shape_image_round_20dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style><style name="shape_image_round_10dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style><style name="shape_image_round_5dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">5dp</item>
    </style></file><file path="D:\mpd_app_dev\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Mpd" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.AppStartLoad" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowNoTitle">true</item>
    </style></file><file path="D:\mpd_app_dev\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">Stress resistance test</string><string name="str_main_all_rights_reserved">©2025 Airdoc All rights reserved</string><string name="str_wechat_scan_code">QR scan check (WeChat)</string><string name="str_settings">Settings</string><string name="str_please_enter_name">Please enter your name</string><string name="str_id_s">ID：%1$s</string><string name="str_gender_male">Male</string><string name="str_gender_female">Female</string><string name="str_confidential">Confidential</string><string name="str_gender_">Gender：%1$s</string><string name="str_phone_last_number_">Phone：%1$s</string><string name="str_please_enter_age">>Please enter your age</string><string name="str_please_enter_phone">Please enter your mobile phone number</string><string name="str_start_detection">Start Detection</string><string name="str_cancel_detection">Exit Detection</string><string name="str_exit">Exit</string><string name="str_device_info">Device information</string><string name="str_i_know">I know</string><string name="str_opening_date">Opening date：%1$s</string><string name="str_valid_until">Valid until：%1$s</string><string name="str_residual_degree">Residual degree：%1$d次</string><string name="str_customer_service_hotline">Customer service hotline：400–100–3999</string><string name="str_network_exception">The current network is abnormal, check the network and try again</string><string name="str_configuration_exception">If the organization configuration is abnormal, contact the support team</string><string name="str_selection_age">Selection age</string><string name="str_ok">OK</string><string name="str_cancel">Cancel</string><string name="str_discovering_new_versions">Update Available</string><string name="str_details">Learn More</string><string name="str_experience_now">Try Now</string><string name="str_app_name_s">Program: %1$s</string><string name="str_version_number_s">Version %1$s</string><string name="str_app_size_d">Size：%1$dM</string><string name="str_upgrade_process_may_take_a_few_minutes">Upgrade may take several minutes, please be patient. Do not power off before completion.</string><string name="str_get_download_resource_exception">Download Error</string><string name="str_view_report">View report</string><string name="str_hrv_waiting_text">Take a deep breath.The detection is about to start…</string><string name="str_detection_not_available">The detection not available</string><string name="str_detection_code_loading">Detection code loading…</string><string name="str_device_service_expired">The device service has expired.\nPlease contact the business personnel to renew!</string><string name="str_number_available_times_0">The number of available times is 0,\nplease recharge and use as soon as possible!</string><string name="str_environment_configuration">Setup Guide</string><string name="str_align_qr_code_recognition">Align the QR code for recognition</string><string name="str_camera_is_here">The camera is here</string><string name="str_please_enter_detection_code">Please enter the detection code</string><string name="str_detection_code_s">Detection Code：%1$s</string><string name="str_request_timeout">Request Timeout</string><string name="str_startup_detection_failed_check_network_and_retry">Startup detection failed.Check your network and retry.</string><string name="str_language_settings">Language Settings</string><string name="str_scanner_settings">Scanner Settings</string><string name="str_browser_scan">Browser Scan Code</string><string name="str_whatsapp_scan">WhatsApp Scan Code</string><string name="str_select_scan_method">Please select the scan method</string><string name="str_h5_qr_code_tips">Scan (Browser),Complete Info,Report to Email.</string><string name="str_whatsapp_qr_code_tips">Scan (WhatsApp),Complete Info,Receive report.</string><string name="str_hrv_detection_overview">·90-Second Detection\n·Multimodal Data Collection\n·AI-Powered Assessment\n·5 Core Metrics Assessed Precisely\n·Health Tips for Stress Management</string><string name="str_unknown">Unknown</string><string name="str_more_settings">More settings</string><string name="str_proactively_greet">Voice guide for home screen (on/off)</string><string name="str_display_viewpoint">Display viewpoint</string><string name="str_model_version">Model version</string><string name="str_test_version">Test version</string><string name="str_data_cache">Data cache</string><string name="str_fingertip_data_collection">Fingertip data collection</string><string name="str_collector_number">Collector number</string><string name="str_update">Update</string><string name="str_upload">Upload</string><string name="str_change">Change</string><string name="str_none">None</string><string name="str_version_format">V%1$s</string></file><file path="D:\mpd_app_dev\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Mpd" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="D:\mpd_app_dev\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\mpd_app_dev\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\mpd_app_dev\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="D:\mpd_app_dev\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\build\generated\res\resValues\release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\build\generated\res\resValues\release"/></dataSet><mergedItems/></merger>