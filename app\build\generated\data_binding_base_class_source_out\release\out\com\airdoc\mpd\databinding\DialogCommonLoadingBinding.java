// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCommonLoadingBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clLoadingRoot;

  @NonNull
  public final ImageView ivLoading;

  @NonNull
  public final TextView tvPrompt;

  private DialogCommonLoadingBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clLoadingRoot, @NonNull ImageView ivLoading,
      @NonNull TextView tvPrompt) {
    this.rootView = rootView;
    this.clLoadingRoot = clLoadingRoot;
    this.ivLoading = ivLoading;
    this.tvPrompt = tvPrompt;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCommonLoadingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCommonLoadingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_common_loading, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCommonLoadingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout clLoadingRoot = (ConstraintLayout) rootView;

      id = R.id.iv_loading;
      ImageView ivLoading = ViewBindings.findChildViewById(rootView, id);
      if (ivLoading == null) {
        break missingId;
      }

      id = R.id.tv_prompt;
      TextView tvPrompt = ViewBindings.findChildViewById(rootView, id);
      if (tvPrompt == null) {
        break missingId;
      }

      return new DialogCommonLoadingBinding((ConstraintLayout) rootView, clLoadingRoot, ivLoading,
          tvPrompt);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
