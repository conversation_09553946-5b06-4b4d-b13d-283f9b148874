<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_device_info" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\dialog_device_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_device_info_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="50"/></Target><Target id="@+id/tv_open_date" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="58" endOffset="55"/></Target><Target id="@+id/tv_valid_until" view="TextView"><Expressions/><location startLine="60" startOffset="8" endLine="71" endOffset="69"/></Target><Target id="@+id/tv_residual_degree" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="84" endOffset="71"/></Target><Target id="@+id/tv_exception" view="TextView"><Expressions/><location startLine="86" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/tv_customer_service_hotline" view="TextView"><Expressions/><location startLine="100" startOffset="8" endLine="111" endOffset="60"/></Target><Target id="@+id/tv_i_know" view="TextView"><Expressions/><location startLine="115" startOffset="4" endLine="128" endOffset="57"/></Target></Targets></Layout>