// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Space;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogStartupModeSettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final RadioButton rbH5QrCode;

  @NonNull
  public final RadioButton rbWhatsappQrCode;

  @NonNull
  public final RadioGroup rgScanType;

  @NonNull
  public final Space spaceLine1;

  @NonNull
  public final TextView tvCancel;

  @NonNull
  public final TextView tvOk;

  @NonNull
  public final TextView tvSelectQrCodeTypeTips;

  @NonNull
  public final TextView tvTitle;

  private DialogStartupModeSettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull RadioButton rbH5QrCode, @NonNull RadioButton rbWhatsappQrCode,
      @NonNull RadioGroup rgScanType, @NonNull Space spaceLine1, @NonNull TextView tvCancel,
      @NonNull TextView tvOk, @NonNull TextView tvSelectQrCodeTypeTips, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.rbH5QrCode = rbH5QrCode;
    this.rbWhatsappQrCode = rbWhatsappQrCode;
    this.rgScanType = rgScanType;
    this.spaceLine1 = spaceLine1;
    this.tvCancel = tvCancel;
    this.tvOk = tvOk;
    this.tvSelectQrCodeTypeTips = tvSelectQrCodeTypeTips;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogStartupModeSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogStartupModeSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_startup_mode_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogStartupModeSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rb_h5_qr_code;
      RadioButton rbH5QrCode = ViewBindings.findChildViewById(rootView, id);
      if (rbH5QrCode == null) {
        break missingId;
      }

      id = R.id.rb_whatsapp_qr_code;
      RadioButton rbWhatsappQrCode = ViewBindings.findChildViewById(rootView, id);
      if (rbWhatsappQrCode == null) {
        break missingId;
      }

      id = R.id.rg_scan_type;
      RadioGroup rgScanType = ViewBindings.findChildViewById(rootView, id);
      if (rgScanType == null) {
        break missingId;
      }

      id = R.id.space_line1;
      Space spaceLine1 = ViewBindings.findChildViewById(rootView, id);
      if (spaceLine1 == null) {
        break missingId;
      }

      id = R.id.tv_cancel;
      TextView tvCancel = ViewBindings.findChildViewById(rootView, id);
      if (tvCancel == null) {
        break missingId;
      }

      id = R.id.tv_ok;
      TextView tvOk = ViewBindings.findChildViewById(rootView, id);
      if (tvOk == null) {
        break missingId;
      }

      id = R.id.tv_select_qr_code_type_tips;
      TextView tvSelectQrCodeTypeTips = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectQrCodeTypeTips == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new DialogStartupModeSettingsBinding((ConstraintLayout) rootView, rbH5QrCode,
          rbWhatsappQrCode, rgScanType, spaceLine1, tvCancel, tvOk, tvSelectQrCodeTypeTips,
          tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
