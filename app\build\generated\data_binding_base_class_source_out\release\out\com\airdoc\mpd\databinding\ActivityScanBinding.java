// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.camera.view.PreviewView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityScanBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final ImageView ivScanAnim;

  @NonNull
  public final ImageView ivScanFrame;

  @NonNull
  public final PreviewView previewView;

  private ActivityScanBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView ivBack,
      @NonNull ImageView ivScanAnim, @NonNull ImageView ivScanFrame,
      @NonNull PreviewView previewView) {
    this.rootView = rootView;
    this.ivBack = ivBack;
    this.ivScanAnim = ivScanAnim;
    this.ivScanFrame = ivScanFrame;
    this.previewView = previewView;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityScanBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityScanBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_scan, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityScanBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_back;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.iv_scan_anim;
      ImageView ivScanAnim = ViewBindings.findChildViewById(rootView, id);
      if (ivScanAnim == null) {
        break missingId;
      }

      id = R.id.iv_scan_frame;
      ImageView ivScanFrame = ViewBindings.findChildViewById(rootView, id);
      if (ivScanFrame == null) {
        break missingId;
      }

      id = R.id.preview_view;
      PreviewView previewView = ViewBindings.findChildViewById(rootView, id);
      if (previewView == null) {
        break missingId;
      }

      return new ActivityScanBinding((ConstraintLayout) rootView, ivBack, ivScanAnim, ivScanFrame,
          previewView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
