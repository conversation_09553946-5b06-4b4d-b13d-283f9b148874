// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import com.airdoc.mpd.detection.hrv.HrvWebView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDetection1Binding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clContent;

  @NonNull
  public final ConstraintLayout clDetectionRoot;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ImageView ivUserAvatar;

  @NonNull
  public final ImageView ivUserInfoBg;

  @NonNull
  public final LinearLayout llLoading;

  @NonNull
  public final TextView tvCancelDetection;

  @NonNull
  public final TextView tvDetectionCode;

  @NonNull
  public final TextView tvUserGender;

  @NonNull
  public final TextView tvUserName;

  @NonNull
  public final TextView tvUserPhone;

  @NonNull
  public final HrvWebView wbHrv;

  private ActivityDetection1Binding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clContent, @NonNull ConstraintLayout clDetectionRoot,
      @NonNull ImageView ivLogo, @NonNull ImageView ivUserAvatar, @NonNull ImageView ivUserInfoBg,
      @NonNull LinearLayout llLoading, @NonNull TextView tvCancelDetection,
      @NonNull TextView tvDetectionCode, @NonNull TextView tvUserGender,
      @NonNull TextView tvUserName, @NonNull TextView tvUserPhone, @NonNull HrvWebView wbHrv) {
    this.rootView = rootView;
    this.clContent = clContent;
    this.clDetectionRoot = clDetectionRoot;
    this.ivLogo = ivLogo;
    this.ivUserAvatar = ivUserAvatar;
    this.ivUserInfoBg = ivUserInfoBg;
    this.llLoading = llLoading;
    this.tvCancelDetection = tvCancelDetection;
    this.tvDetectionCode = tvDetectionCode;
    this.tvUserGender = tvUserGender;
    this.tvUserName = tvUserName;
    this.tvUserPhone = tvUserPhone;
    this.wbHrv = wbHrv;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDetection1Binding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDetection1Binding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_detection1, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDetection1Binding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cl_content;
      ConstraintLayout clContent = ViewBindings.findChildViewById(rootView, id);
      if (clContent == null) {
        break missingId;
      }

      ConstraintLayout clDetectionRoot = (ConstraintLayout) rootView;

      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.iv_user_avatar;
      ImageView ivUserAvatar = ViewBindings.findChildViewById(rootView, id);
      if (ivUserAvatar == null) {
        break missingId;
      }

      id = R.id.iv_user_info_bg;
      ImageView ivUserInfoBg = ViewBindings.findChildViewById(rootView, id);
      if (ivUserInfoBg == null) {
        break missingId;
      }

      id = R.id.ll_loading;
      LinearLayout llLoading = ViewBindings.findChildViewById(rootView, id);
      if (llLoading == null) {
        break missingId;
      }

      id = R.id.tv_cancel_detection;
      TextView tvCancelDetection = ViewBindings.findChildViewById(rootView, id);
      if (tvCancelDetection == null) {
        break missingId;
      }

      id = R.id.tv_detection_code;
      TextView tvDetectionCode = ViewBindings.findChildViewById(rootView, id);
      if (tvDetectionCode == null) {
        break missingId;
      }

      id = R.id.tv_user_gender;
      TextView tvUserGender = ViewBindings.findChildViewById(rootView, id);
      if (tvUserGender == null) {
        break missingId;
      }

      id = R.id.tv_user_name;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      id = R.id.tv_user_phone;
      TextView tvUserPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvUserPhone == null) {
        break missingId;
      }

      id = R.id.wb_hrv;
      HrvWebView wbHrv = ViewBindings.findChildViewById(rootView, id);
      if (wbHrv == null) {
        break missingId;
      }

      return new ActivityDetection1Binding((ConstraintLayout) rootView, clContent, clDetectionRoot,
          ivLogo, ivUserAvatar, ivUserInfoBg, llLoading, tvCancelDetection, tvDetectionCode,
          tvUserGender, tvUserName, tvUserPhone, wbHrv);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
