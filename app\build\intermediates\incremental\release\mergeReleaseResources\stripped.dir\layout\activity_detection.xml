<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_detection_bg">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="73dp"
        android:src="@drawable/ic_main_logo"
        android:scaleType="fitStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginStart="5dp"/>

    <ImageView
        android:id="@+id/iv_user_info_bg"
        android:layout_width="300dp"
        android:layout_height="465dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@drawable/ic_user_info_bg"/>

    <ImageView
        android:id="@+id/iv_user_avatar"
        android:layout_width="106dp"
        android:layout_height="106dp"
        android:src="@drawable/ic_male_avatar_round"
        android:layout_marginTop="65dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintTop_toTopOf="@+id/iv_user_info_bg"/>

    <TextView
        android:id="@+id/tv_user_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintTop_toBottomOf="@+id/iv_user_avatar"
        android:layout_marginTop="15dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        tools:text="张先生"/>

    <TextView
        android:id="@+id/tv_user_gender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_666666"
        android:textSize="16sp"
        tools:text="性别：男"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_user_name"/>

    <TextView
        android:id="@+id/tv_user_phone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_666666"
        android:textSize="16sp"
        tools:text="手机：156****6611"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_user_gender"/>

    <TextView
        android:id="@+id/tv_detection_code"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:includeFontPadding="false"
        android:textColor="@color/color_666666"
        android:textSize="16sp"
        tools:text="检测码：922025051600703"
        android:gravity="center"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintTop_toBottomOf="@+id/tv_user_phone"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"/>

    <TextView
        android:id="@+id/tv_cancel_detection"
        android:layout_width="200dp"
        android:layout_height="40dp"
        app:layout_constraintLeft_toLeftOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintBottom_toBottomOf="@+id/iv_user_info_bg"
        android:layout_marginBottom="47dp"
        android:includeFontPadding="false"
        android:textColor="#646A73"
        android:textSize="17sp"
        android:text="@string/str_exit"
        android:background="@drawable/common_8f959e_stroke_20_bg"
        android:gravity="center"
        android:focusable="false"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_detection_project"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="90dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:paddingStart="60dp"
        android:paddingEnd="60dp"
        android:paddingTop="40dp"
        android:paddingBottom="40dp"
        android:background="@drawable/common_white_round_25_bg"
        app:layout_constraintLeft_toRightOf="@+id/iv_user_info_bg"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>