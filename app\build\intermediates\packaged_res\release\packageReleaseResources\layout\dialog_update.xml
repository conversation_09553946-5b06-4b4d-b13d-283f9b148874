<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="360dp"
    android:layout_height="310dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/common_white_round_15_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_discovering_new_versions"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:includeFontPadding="false"
        android:layout_marginTop="17dp"
        android:layout_marginStart="24dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="应用：视觉训练系统"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:textSize="15sp"
        android:layout_marginStart="22dp"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="版本号：V1.0.1"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:layout_marginStart="22dp"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_name"/>

    <TextView
        android:id="@+id/tv_app_size"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="应用大小：100M"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:layout_marginStart="22dp"
        android:layout_marginTop="5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_version"/>

    <TextView
        android:id="@+id/tv_details"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_details"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:layout_marginStart="22dp"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_size"/>

    <TextView
        android:id="@+id/tv_introduction"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        tools:text="增加了XXXXX功能，修复了已知问题增加了XXXXX功能，修复了已知问题增加了XXXXX功能，修复了已知问题"
        android:includeFontPadding="false"
        android:textColor="@color/color_333333"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_marginStart="22dp"
        android:layout_marginEnd="22dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_details"
        app:layout_constraintBottom_toTopOf="@+id/tv_update"/>

    <TextView
        android:id="@+id/tv_update"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_experience_now"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>