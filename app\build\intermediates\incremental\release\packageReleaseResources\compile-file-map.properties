#Tue Aug 05 14:42:45 CST 2025
com.airdoc.mpd.app-main-4\:/anim/anim_common_loading.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\anim\\anim_common_loading.xml
com.airdoc.mpd.app-main-4\:/color/color_selector_param_setting_mode.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\color_selector_param_setting_mode.xml
com.airdoc.mpd.app-main-4\:/color/selector_common_radio_button_tint.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\selector_common_radio_button_tint.xml
com.airdoc.mpd.app-main-4\:/color/selector_common_switch_compat_track_tint.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\selector_common_switch_compat_track_tint.xml
com.airdoc.mpd.app-main-4\:/color/selector_input_gender_radio_button_tint.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\selector_input_gender_radio_button_tint.xml
com.airdoc.mpd.app-main-4\:/color/selector_input_gender_text_color.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\selector_input_gender_text_color.xml
com.airdoc.mpd.app-main-4\:/drawable-en-hdpi/ic_main_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-en-hdpi-v4\\ic_main_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-en-hdpi/ic_main_bg_1.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-en-hdpi-v4\\ic_main_bg_1.webp
com.airdoc.mpd.app-main-4\:/drawable-en-hdpi/ic_main_logo.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-en-hdpi-v4\\ic_main_logo.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_close_scan.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_close_scan.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_configuration_exception.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_configuration_exception.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_detection_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_detection_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_detection_hrv_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_detection_hrv_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_device_info.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_device_info.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_device_info_dialog_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_device_info_dialog_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_device_info_dialog_content_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_device_info_dialog_content_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_device_info_dialog_left.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_device_info_dialog_left.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_device_info_dialog_right.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_device_info_dialog_right.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_female_avatar_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_female_avatar_round.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_input_age.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_input_age.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_input_gender.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_input_gender.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_input_name.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_input_name.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_input_phone.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_input_phone.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_loading.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_loading.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_main_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_main_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_main_bg_1.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_main_bg_1.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_main_logo.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_main_logo.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_male_avatar_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_male_avatar_round.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_menu.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_menu.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_network_exception.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_network_exception.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_red_dot.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_red_dot.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_refresh_red.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_refresh_red.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_refresh_white.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_refresh_white.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_scan.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_scan.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_scan_anim.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_scan_anim.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_scan_camera_tips.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_scan_camera_tips.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_scan_frame.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_scan_frame.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_update_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_update_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_user_info_bg.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_user_info_bg.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_version.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_version.webp
com.airdoc.mpd.app-main-4\:/drawable-hdpi/ic_view_report.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable-hdpi-v4\\ic_view_report.webp
com.airdoc.mpd.app-main-4\:/drawable/app_launcher_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\app_launcher_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_8d82c6_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_8d82c6_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_8f959e_stroke_20_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_8f959e_stroke_20_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_black_20_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_black_20_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_blue_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_blue_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_ce005c_stroke_20_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_ce005c_stroke_20_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_d6dce1_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_d6dce1_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_d7dce9_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_d7dce9_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_ea4e3d_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_ea4e3d_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_eb4e89_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_eb4e89_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_eff3f6_round_20_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_eff3f6_round_20_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_gray_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_gray_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_green_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_green_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_light_blue_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_light_blue_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_red_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_red_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_stroke_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_stroke_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_white_20_round_20_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_white_20_round_20_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_white_round_15_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_white_round_15_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_white_round_20_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_white_round_20_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_white_round_25_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_white_round_25_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/common_white_round_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\common_white_round_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/config_switch_mask_therapy_selected.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\config_switch_mask_therapy_selected.xml
com.airdoc.mpd.app-main-4\:/drawable/config_switch_mask_therapy_unselected.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\config_switch_mask_therapy_unselected.xml
com.airdoc.mpd.app-main-4\:/drawable/finish_read_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\finish_read_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/gradient_pink_background.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\gradient_pink_background.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_cross.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_cross.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_language_en_us.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_language_en_us.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_language_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_language_settings.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_language_zh_cn.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_language_zh_cn.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_launcher_background.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_background.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_launcher_foreground.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_launcher_foreground.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_more_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_more_settings.xml
com.airdoc.mpd.app-main-4\:/drawable/ic_scanner_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_scanner_settings.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_advanced_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_advanced_settings.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_ai_train_guide_instructions.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_ai_train_guide_instructions.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_airdoc_digital_therapy_center.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_airdoc_digital_therapy_center.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_back_black_coarse.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_back_black_coarse.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_back_black_fine.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_back_black_fine.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_back_fine_line_arrow.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_back_fine_line_arrow.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_back_white_coarse.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_back_white_coarse.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_calibration_m.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_calibration_m.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_close_airdoc_ai.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_close_airdoc_ai.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_device_exception_menu.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_device_exception_menu.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_help_center_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_help_center_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_launcher_network_anomaly.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_launcher_network_anomaly.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_login_account_number.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_login_account_number.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_login_password.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_login_password.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_main_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_main_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_new_patient.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_new_patient.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_param_preview_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_param_preview_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_patient_library.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_patient_library.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_read_assessment_report.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_read_assessment_report.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_read_content.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_read_content.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_read_main_ai_logo.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_read_main_ai_logo.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_read_main_logo.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_read_main_logo.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_settings_unselect.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_settings_unselect.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_tsc_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_tsc_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_version.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_version.xml
com.airdoc.mpd.app-main-4\:/drawable/icon_visual_train_no_open.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\icon_visual_train_no_open.xml
com.airdoc.mpd.app-main-4\:/drawable/input_cursor.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\input_cursor.xml
com.airdoc.mpd.app-main-4\:/drawable/login_input_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\login_input_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/main_bt_common_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\main_bt_common_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/main_input_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\main_input_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/read_common_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\read_common_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/read_count_down_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\read_count_down_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/read_speed_standard_annotation_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\read_speed_standard_annotation_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/rounded_pink_button.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\rounded_pink_button.xml
com.airdoc.mpd.app-main-4\:/drawable/seekbar_shaded_area_progress_drawable.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\seekbar_shaded_area_progress_drawable.xml
com.airdoc.mpd.app-main-4\:/drawable/seekbar_shaded_area_thumb.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\seekbar_shaded_area_thumb.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_check_protocol_red.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_check_protocol_red.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_common_radio_button.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_common_radio_button.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_input_gender_radio_button.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_input_gender_radio_button.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_param_setting_mode_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_param_setting_mode_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_param_setting_scale_icon.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_param_setting_scale_icon.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_play_read_track_head_map_icon.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_play_read_track_head_map_icon.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_read_init_tab_bg.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_read_init_tab_bg.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_read_track_figure_icon.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_read_track_figure_icon.xml
com.airdoc.mpd.app-main-4\:/drawable/selector_read_track_head_map_icon.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\selector_read_track_head_map_icon.xml
com.airdoc.mpd.app-main-4\:/drawable/switch_behavior_guidance_style.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\switch_behavior_guidance_style.xml
com.airdoc.mpd.app-main-4\:/drawable/switch_behavior_guidance_thumb.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\switch_behavior_guidance_thumb.xml
com.airdoc.mpd.app-main-4\:/drawable/switch_mask_therapy_style.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\switch_mask_therapy_style.xml
com.airdoc.mpd.app-main-4\:/drawable/switch_mask_therapy_thumb.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\switch_mask_therapy_thumb.xml
com.airdoc.mpd.app-main-4\:/drawable/update_progress_drawable.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\update_progress_drawable.xml
com.airdoc.mpd.app-main-4\:/mipmap-anydpi/ic_launcher_round.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.airdoc.mpd.app-main-4\:/mipmap-en-hdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-en-hdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-en-mdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-en-mdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-en-xhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-en-xhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-en-xxhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-en-xxhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-en-xxxhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-en-xxxhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-hdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.airdoc.mpd.app-main-4\:/mipmap-mdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.airdoc.mpd.app-main-4\:/mipmap-xhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.airdoc.mpd.app-main-4\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.airdoc.mpd.app-main-4\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.airdoc.mpd.app-main-4\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.airdoc.mpd.app-main-4\:/raw-en/detection_code_last_number.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\detection_code_last_number.mp3
com.airdoc.mpd.app-main-4\:/raw-en/mobile_browser.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\mobile_browser.mp3
com.airdoc.mpd.app-main-4\:/raw-en/phone_last_number.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\phone_last_number.mp3
com.airdoc.mpd.app-main-4\:/raw-en/please_enter_detection_code.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\please_enter_detection_code.mp3
com.airdoc.mpd.app-main-4\:/raw-en/please_use.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\please_use.mp3
com.airdoc.mpd.app-main-4\:/raw-en/proceed_detection.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\proceed_detection.mp3
com.airdoc.mpd.app-main-4\:/raw-en/scan_code.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\scan_code.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_0.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_0.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_1.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_1.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_2.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_2.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_3.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_3.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_4.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_4.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_5.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_5.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_6.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_6.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_7.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_7.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_8.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_8.mp3
com.airdoc.mpd.app-main-4\:/raw-en/speech_9.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\speech_9.mp3
com.airdoc.mpd.app-main-4\:/raw-en/we_chat.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\we_chat.mp3
com.airdoc.mpd.app-main-4\:/raw-en/welcome_use_stress_tolerance_assessment.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw-en\\welcome_use_stress_tolerance_assessment.mp3
com.airdoc.mpd.app-main-4\:/raw/detection_code_last_number.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\detection_code_last_number.mp3
com.airdoc.mpd.app-main-4\:/raw/mobile_browser.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\mobile_browser.mp3
com.airdoc.mpd.app-main-4\:/raw/phone_last_number.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\phone_last_number.mp3
com.airdoc.mpd.app-main-4\:/raw/please_enter_detection_code.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\please_enter_detection_code.mp3
com.airdoc.mpd.app-main-4\:/raw/please_use.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\please_use.mp3
com.airdoc.mpd.app-main-4\:/raw/proceed_detection.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\proceed_detection.mp3
com.airdoc.mpd.app-main-4\:/raw/scan_code.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\scan_code.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_0.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_0.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_1.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_1.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_2.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_2.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_3.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_3.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_4.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_4.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_5.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_5.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_6.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_6.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_7.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_7.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_8.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_8.mp3
com.airdoc.mpd.app-main-4\:/raw/speech_9.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\speech_9.mp3
com.airdoc.mpd.app-main-4\:/raw/we_chat.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\we_chat.mp3
com.airdoc.mpd.app-main-4\:/raw/welcome_use_stress_tolerance_assessment.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\welcome_use_stress_tolerance_assessment.mp3
com.airdoc.mpd.app-main-4\:/raw/whatsapp.mp3=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\raw\\whatsapp.mp3
com.airdoc.mpd.app-main-4\:/xml/backup_rules.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\backup_rules.xml
com.airdoc.mpd.app-main-4\:/xml/data_extraction_rules.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\data_extraction_rules.xml
com.airdoc.mpd.app-main-4\:/xml/file_paths.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\file_paths.xml
com.airdoc.mpd.app-main-4\:/xml/network_security_config.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\xml\\network_security_config.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_calibration.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_calibration.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_config.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_config.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_detection.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_detection.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_detection1.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_detection1.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_detection_web.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_detection_web.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_hrv.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_hrv.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_main.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_main.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_more_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_more_settings.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_scan.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_scan.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/activity_update.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\activity_update.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_common_loading.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_common_loading.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_device_info.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_device_info.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_device_reminder.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_device_reminder.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_language_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_language_settings.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_selection_age.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_selection_age.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_startup_mode_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_startup_mode_settings.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/dialog_update.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_update.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/fragment_detection_code_detection.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_detection_code_detection.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/fragment_device_exception.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_device_exception.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/fragment_input_info_detection.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_input_info_detection.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/fragment_scan_code_detection.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\fragment_scan_code_detection.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/item_detection.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_detection.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/item_language_settings.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\item_language_settings.xml
com.airdoc.mpd.app-packageReleaseResources-2\:/layout/layout_menu_popup_window.xml=D\:\\mpd_app_dev\\app\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\layout_menu_popup_window.xml
