<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\main\jniLibs"><file name="arm64-v8a/libalibabacloud-oss-cpp-sdk.so" path="D:\mpd_app_dev\app\src\main\jniLibs\arm64-v8a\libalibabacloud-oss-cpp-sdk.so"/><file name="arm64-v8a/libjsoncpp.so" path="D:\mpd_app_dev\app\src\main\jniLibs\arm64-v8a\libjsoncpp.so"/><file name="arm64-v8a/libopencv_java4.so" path="D:\mpd_app_dev\app\src\main\jniLibs\arm64-v8a\libopencv_java4.so"/><file name="arm64-v8a/libpq_blr.so" path="D:\mpd_app_dev\app\src\main\jniLibs\arm64-v8a\libpq_blr.so"/><file name="arm64-v8a/librknnrt.so" path="D:\mpd_app_dev\app\src\main\jniLibs\arm64-v8a\librknnrt.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\mpd_app_dev\app\src\release\jniLibs"/></dataSet></merger>