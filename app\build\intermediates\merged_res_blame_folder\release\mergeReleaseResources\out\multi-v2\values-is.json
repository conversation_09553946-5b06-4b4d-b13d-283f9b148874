{"logs": [{"outputFile": "com.airdoc.mpd.app-mergeReleaseResources-69:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf04cdc715fc93d5a24d642c24f51c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5026,5132,5286,5411,5520,5661,5786,5895,6129,6283,6389,6546,6672,6814,6968,7032,7095", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "5127,5281,5406,5515,5656,5781,5890,5995,6278,6384,6541,6667,6809,6963,7027,7090,7169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37eb7cb3503359f8e3891a2adf804078\\transformed\\jetified-media3-ui-1.3.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,488,689,774,858,935,1024,1121,1190,1254,1345,1436,1499,1563,1625,1693,1817,1943,2067,2142,2223,2296,2365,2448,2530,2595,2675,2728,2789,2839,2900,2959,3029,3092,3154,3218,3278,3344,3409,3479,3531,3591,3665,3739,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52,64", "endOffsets": "283,483,684,769,853,930,1019,1116,1185,1249,1340,1431,1494,1558,1620,1688,1812,1938,2062,2137,2218,2291,2360,2443,2525,2590,2670,2723,2784,2834,2895,2954,3024,3087,3149,3213,3273,3339,3404,3474,3526,3586,3660,3734,3787,3852"}, "to": {"startLines": "2,11,15,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,583,7239,7324,7408,7485,7574,7671,7740,7804,7895,7986,8049,8113,8175,8243,8367,8493,8617,8692,8773,8846,8915,8998,9080,9145,9855,9908,9969,10019,10080,10139,10209,10272,10334,10398,10458,10524,10589,10659,10711,10771,10845,10919,10972", "endLines": "10,14,18,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,84,83,76,88,96,68,63,90,90,62,63,61,67,123,125,123,74,80,72,68,82,81,64,79,52,60,49,60,58,69,62,61,63,59,65,64,69,51,59,73,73,52,64", "endOffsets": "378,578,779,7319,7403,7480,7569,7666,7735,7799,7890,7981,8044,8108,8170,8238,8362,8488,8612,8687,8768,8841,8910,8993,9075,9140,9220,9903,9964,10014,10075,10134,10204,10267,10329,10393,10453,10519,10584,10654,10706,10766,10840,10914,10967,11032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f784686b41df3e3e9ff94a38ce261387\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6000", "endColumns": "128", "endOffsets": "6124"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\efd467293e0b9b5a51777a2be79e83eb\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,181,240,306,382,445,534,616", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "117,176,235,301,377,440,529,611,680"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9225,9292,9351,9410,9476,9552,9615,9704,9786", "endColumns": "66,58,58,65,75,62,88,81,68", "endOffsets": "9287,9346,9405,9471,9547,9610,9699,9781,9850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\09e94a311f42d674eb715371ac8d596c\\transformed\\material-1.10.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,344,416,495,577,657,754,869,951,1016,1104,1168,1229,1319,1383,1446,1508,1576,1640,1696,1819,1884,1946,2002,2073,2200,2284,2368,2504,2581,2658,2774,2861,2940,2997,3052,3118,3194,3274,3363,3439,3506,3580,3650,3716,3818,3904,3974,4065,4155,4229,4302,4391,4442,4523,4595,4676,4762,4824,4888,4951,5020,5134,5240,5348,5450,5511,5570", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "265,339,411,490,572,652,749,864,946,1011,1099,1163,1224,1314,1378,1441,1503,1571,1635,1691,1814,1879,1941,1997,2068,2195,2279,2363,2499,2576,2653,2769,2856,2935,2992,3047,3113,3189,3269,3358,3434,3501,3575,3645,3711,3813,3899,3969,4060,4150,4224,4297,4386,4437,4518,4590,4671,4757,4819,4883,4946,5015,5129,5235,5343,5445,5506,5565,5645"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3628,3702,3774,3853,3935,4732,4829,4944,7174,11037,11125,11189,11250,11340,11404,11467,11529,11597,11661,11717,11840,11905,11967,12023,12094,12221,12305,12389,12525,12602,12679,12795,12882,12961,13018,13073,13139,13215,13295,13384,13460,13527,13601,13671,13737,13839,13925,13995,14086,14176,14250,14323,14412,14463,14544,14616,14697,14783,14845,14909,14972,15041,15155,15261,15369,15471,15532,15591", "endLines": "22,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "endColumns": "12,73,71,78,81,79,96,114,81,64,87,63,60,89,63,62,61,67,63,55,122,64,61,55,70,126,83,83,135,76,76,115,86,78,56,54,65,75,79,88,75,66,73,69,65,101,85,69,90,89,73,72,88,50,80,71,80,85,61,63,62,68,113,105,107,101,60,58,79", "endOffsets": "949,3697,3769,3848,3930,4010,4824,4939,5021,7234,11120,11184,11245,11335,11399,11462,11524,11592,11656,11712,11835,11900,11962,12018,12089,12216,12300,12384,12520,12597,12674,12790,12877,12956,13013,13068,13134,13210,13290,13379,13455,13522,13596,13666,13732,13834,13920,13990,14081,14171,14245,14318,14407,14458,14539,14611,14692,14778,14840,14904,14967,15036,15150,15256,15364,15466,15527,15586,15666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c63b318755d78145d01b8b87b88f3c2\\transformed\\core-1.12.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "55,56,57,58,59,60,61,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4015,4110,4217,4314,4414,4517,4621,15752", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "4105,4212,4309,4409,4512,4616,4727,15848"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ede9995337ea73b4d0233d500609b091\\transformed\\appcompat-1.6.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,874,965,1058,1151,1245,1351,1444,1539,1634,1725,1819,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "200,297,409,494,595,709,790,869,960,1053,1146,1240,1346,1439,1534,1629,1720,1814,1895,2005,2112,2209,2318,2418,2521,2676,2774,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "954,1054,1151,1263,1348,1449,1563,1644,1723,1814,1907,2000,2094,2200,2293,2388,2483,2574,2668,2749,2859,2966,3063,3172,3272,3375,3530,15671", "endColumns": "99,96,111,84,100,113,80,78,90,92,92,93,105,92,94,94,90,93,80,109,106,96,108,99,102,154,97,80", "endOffsets": "1049,1146,1258,1343,1444,1558,1639,1718,1809,1902,1995,2089,2195,2288,2383,2478,2569,2663,2744,2854,2961,3058,3167,3267,3370,3525,3623,15747"}}]}]}