<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_startup_mode_settings" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\dialog_startup_mode_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_startup_mode_settings_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="50"/></Target><Target id="@+id/rg_scan_type" view="RadioGroup"><Expressions/><location startLine="60" startOffset="8" endLine="95" endOffset="20"/></Target><Target id="@+id/rb_h5_qr_code" view="RadioButton"><Expressions/><location startLine="70" startOffset="12" endLine="81" endOffset="56"/></Target><Target id="@+id/rb_whatsapp_qr_code" view="RadioButton"><Expressions/><location startLine="83" startOffset="12" endLine="93" endOffset="57"/></Target><Target id="@+id/space_line1" view="Space"><Expressions/><location startLine="97" startOffset="8" endLine="102" endOffset="60"/></Target><Target id="@+id/tv_select_qr_code_type_tips" view="TextView"><Expressions/><location startLine="104" startOffset="8" endLine="116" endOffset="60"/></Target><Target id="@+id/tv_cancel" view="TextView"><Expressions/><location startLine="120" startOffset="4" endLine="134" endOffset="60"/></Target><Target id="@+id/tv_ok" view="TextView"><Expressions/><location startLine="136" startOffset="4" endLine="150" endOffset="57"/></Target></Targets></Layout>