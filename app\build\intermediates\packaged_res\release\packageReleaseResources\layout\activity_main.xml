<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    android:id="@+id/cl_main_root"
    tools:background="@drawable/ic_main_bg">

    <FrameLayout
        android:id="@+id/fl_start_up_mode"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="73dp"
        android:src="@drawable/ic_main_logo"
        android:scaleType="fitStart"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginStart="5dp"/>

    <TextView
        android:id="@+id/tv_copyright"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/str_main_all_rights_reserved"
        android:layout_marginBottom="20dp"
        android:textSize="14sp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_setting"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="25dp">

        <ImageView
            android:id="@+id/iv_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ic_menu" />

        <ImageView
            android:id="@+id/iv_setting_red_dot"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/ic_red_dot"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:visibility="gone"
            tools:visibility="visible"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_config"
        android:layout_width="268dp"
        android:layout_height="25dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>



</androidx.constraintlayout.widget.ConstraintLayout>