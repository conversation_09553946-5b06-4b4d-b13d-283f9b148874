// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMoreSettingsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView btnChangeCollector;

  @NonNull
  public final TextView btnUpdateTest;

  @NonNull
  public final TextView btnUploadCache;

  @NonNull
  public final ImageView ivBack;

  @NonNull
  public final LinearLayout llCollectorNumber;

  @NonNull
  public final LinearLayout llDataCache;

  @NonNull
  public final LinearLayout llFingertipCollection;

  @NonNull
  public final LinearLayout llProactivelyGreet;

  @NonNull
  public final LinearLayout llTestVersion;

  @NonNull
  public final SwitchCompat switchFingertipCollection;

  @NonNull
  public final SwitchCompat switchProactivelyGreet;

  @NonNull
  public final TextView tvCollectorNumber;

  @NonNull
  public final TextView tvDataCacheStatus;

  @NonNull
  public final TextView tvFingertipCollection;

  @NonNull
  public final TextView tvProactivelyGreet;

  @NonNull
  public final TextView tvTestVersion;

  private ActivityMoreSettingsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView btnChangeCollector, @NonNull TextView btnUpdateTest,
      @NonNull TextView btnUploadCache, @NonNull ImageView ivBack,
      @NonNull LinearLayout llCollectorNumber, @NonNull LinearLayout llDataCache,
      @NonNull LinearLayout llFingertipCollection, @NonNull LinearLayout llProactivelyGreet,
      @NonNull LinearLayout llTestVersion, @NonNull SwitchCompat switchFingertipCollection,
      @NonNull SwitchCompat switchProactivelyGreet, @NonNull TextView tvCollectorNumber,
      @NonNull TextView tvDataCacheStatus, @NonNull TextView tvFingertipCollection,
      @NonNull TextView tvProactivelyGreet, @NonNull TextView tvTestVersion) {
    this.rootView = rootView;
    this.btnChangeCollector = btnChangeCollector;
    this.btnUpdateTest = btnUpdateTest;
    this.btnUploadCache = btnUploadCache;
    this.ivBack = ivBack;
    this.llCollectorNumber = llCollectorNumber;
    this.llDataCache = llDataCache;
    this.llFingertipCollection = llFingertipCollection;
    this.llProactivelyGreet = llProactivelyGreet;
    this.llTestVersion = llTestVersion;
    this.switchFingertipCollection = switchFingertipCollection;
    this.switchProactivelyGreet = switchProactivelyGreet;
    this.tvCollectorNumber = tvCollectorNumber;
    this.tvDataCacheStatus = tvDataCacheStatus;
    this.tvFingertipCollection = tvFingertipCollection;
    this.tvProactivelyGreet = tvProactivelyGreet;
    this.tvTestVersion = tvTestVersion;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMoreSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMoreSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_more_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMoreSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_change_collector;
      TextView btnChangeCollector = ViewBindings.findChildViewById(rootView, id);
      if (btnChangeCollector == null) {
        break missingId;
      }

      id = R.id.btn_update_test;
      TextView btnUpdateTest = ViewBindings.findChildViewById(rootView, id);
      if (btnUpdateTest == null) {
        break missingId;
      }

      id = R.id.btn_upload_cache;
      TextView btnUploadCache = ViewBindings.findChildViewById(rootView, id);
      if (btnUploadCache == null) {
        break missingId;
      }

      id = R.id.iv_back;
      ImageView ivBack = ViewBindings.findChildViewById(rootView, id);
      if (ivBack == null) {
        break missingId;
      }

      id = R.id.ll_collector_number;
      LinearLayout llCollectorNumber = ViewBindings.findChildViewById(rootView, id);
      if (llCollectorNumber == null) {
        break missingId;
      }

      id = R.id.ll_data_cache;
      LinearLayout llDataCache = ViewBindings.findChildViewById(rootView, id);
      if (llDataCache == null) {
        break missingId;
      }

      id = R.id.ll_fingertip_collection;
      LinearLayout llFingertipCollection = ViewBindings.findChildViewById(rootView, id);
      if (llFingertipCollection == null) {
        break missingId;
      }

      id = R.id.ll_proactively_greet;
      LinearLayout llProactivelyGreet = ViewBindings.findChildViewById(rootView, id);
      if (llProactivelyGreet == null) {
        break missingId;
      }

      id = R.id.ll_test_version;
      LinearLayout llTestVersion = ViewBindings.findChildViewById(rootView, id);
      if (llTestVersion == null) {
        break missingId;
      }

      id = R.id.switch_fingertip_collection;
      SwitchCompat switchFingertipCollection = ViewBindings.findChildViewById(rootView, id);
      if (switchFingertipCollection == null) {
        break missingId;
      }

      id = R.id.switch_proactively_greet;
      SwitchCompat switchProactivelyGreet = ViewBindings.findChildViewById(rootView, id);
      if (switchProactivelyGreet == null) {
        break missingId;
      }

      id = R.id.tv_collector_number;
      TextView tvCollectorNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvCollectorNumber == null) {
        break missingId;
      }

      id = R.id.tv_data_cache_status;
      TextView tvDataCacheStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvDataCacheStatus == null) {
        break missingId;
      }

      id = R.id.tv_fingertip_collection;
      TextView tvFingertipCollection = ViewBindings.findChildViewById(rootView, id);
      if (tvFingertipCollection == null) {
        break missingId;
      }

      id = R.id.tv_proactively_greet;
      TextView tvProactivelyGreet = ViewBindings.findChildViewById(rootView, id);
      if (tvProactivelyGreet == null) {
        break missingId;
      }

      id = R.id.tv_test_version;
      TextView tvTestVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvTestVersion == null) {
        break missingId;
      }

      return new ActivityMoreSettingsBinding((LinearLayout) rootView, btnChangeCollector,
          btnUpdateTest, btnUploadCache, ivBack, llCollectorNumber, llDataCache,
          llFingertipCollection, llProactivelyGreet, llTestVersion, switchFingertipCollection,
          switchProactivelyGreet, tvCollectorNumber, tvDataCacheStatus, tvFingertipCollection,
          tvProactivelyGreet, tvTestVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
