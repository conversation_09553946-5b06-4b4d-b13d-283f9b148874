<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MPD眼动追踪WebSocket测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .connection-panel {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn-connect {
            background: #4CAF50;
            color: white;
        }
        .btn-disconnect {
            background: #f44336;
            color: white;
        }
        .btn-send {
            background: #2196F3;
            color: white;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .control-panel {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .data-panel {
            margin-top: 20px;
        }
        .data-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .gaze-display {
            position: relative;
            width: 100%;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            overflow: hidden;
        }
        .gaze-point {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff4444;
            border: 2px solid #ffffff;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.6);
            z-index: 10;
        }
        .gaze-trail {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgba(255, 68, 68, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        .gaze-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .message-log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #666;
            margin-right: 10px;
        }
        .log-success {
            color: #28a745;
        }
        .log-error {
            color: #dc3545;
        }
        .log-info {
            color: #17a2b8;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-bottom: 3px;
        }
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <h1>🔗 MPD眼动追踪WebSocket测试</h1>
    
    <div class="container">
        <div class="connection-panel">
            <input type="text" id="wsUrl" placeholder="WebSocket地址" value="ws://localhost:9200">
            <button id="connectBtn" class="btn-connect">连接</button>
            <button id="disconnectBtn" class="btn-disconnect" disabled>断开</button>
            <button id="clearBtn" class="btn-send">清空日志</button>
        </div>
        
        <div id="status" class="status disconnected">状态: 未连接</div>
        
        <div class="control-panel">
            <button id="pingBtn" class="btn-send" disabled>发送Ping</button>
            <button id="statusBtn" class="btn-send" disabled>获取状态</button>
            <button id="startBtn" class="btn-send" disabled>开始追踪</button>
            <button id="stopBtn" class="btn-send" disabled>停止追踪</button>
        </div>
    </div>

    <div class="container">
        <div class="stats-panel">
            <div class="stat-item">
                <div class="stat-label">连接数</div>
                <div class="stat-value" id="connCount">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">消息数</div>
                <div class="stat-value" id="msgCount">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">眼动数据</div>
                <div class="stat-value" id="gazeCount">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">错误数</div>
                <div class="stat-value" id="errorCount">0</div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="data-panel">
            <h3>👁️ 实时眼动数据</h3>
            <div class="gaze-info">
                <div class="info-item">
                    <div class="info-label">X坐标</div>
                    <div class="info-value" id="gazeX">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Y坐标</div>
                    <div class="info-value" id="gazeY">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">距离(cm)</div>
                    <div class="info-value" id="gazeDist">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">持续时间(ms)</div>
                    <div class="info-value" id="gazeDuration">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">数据有效</div>
                    <div class="info-value" id="gazeValid">--</div>
                </div>
                <div class="info-item">
                    <div class="info-label">倾斜检测</div>
                    <div class="info-value" id="gazeSkew">--</div>
                </div>
            </div>
            
            <div class="gaze-display" id="gazeCanvas">
                <div class="gaze-point" id="gazePoint" style="display: none;"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="data-panel">
            <h3>📋 消息日志</h3>
            <div class="message-log" id="messageLog"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let messageCount = 0;
        let gazeCount = 0;
        let errorCount = 0;
        let trails = [];
        let maxTrails = 50;

        const elements = {
            wsUrl: document.getElementById('wsUrl'),
            connectBtn: document.getElementById('connectBtn'),
            disconnectBtn: document.getElementById('disconnectBtn'),
            clearBtn: document.getElementById('clearBtn'),
            pingBtn: document.getElementById('pingBtn'),
            statusBtn: document.getElementById('statusBtn'),
            startBtn: document.getElementById('startBtn'),
            stopBtn: document.getElementById('stopBtn'),
            status: document.getElementById('status'),
            messageLog: document.getElementById('messageLog'),
            gazePoint: document.getElementById('gazePoint'),
            gazeCanvas: document.getElementById('gazeCanvas'),
            gazeX: document.getElementById('gazeX'),
            gazeY: document.getElementById('gazeY'),
            gazeDist: document.getElementById('gazeDist'),
            gazeDuration: document.getElementById('gazeDuration'),
            gazeValid: document.getElementById('gazeValid'),
            gazeSkew: document.getElementById('gazeSkew'),
            connCount: document.getElementById('connCount'),
            msgCount: document.getElementById('msgCount'),
            gazeCount: document.getElementById('gazeCount'),
            errorCount: document.getElementById('errorCount')
        };

        function updateStatus(message, type = 'disconnected') {
            elements.status.textContent = `状态: ${message}`;
            elements.status.className = `status ${type}`;
        }

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span><span class="log-${type}">${message}</span>`;
            
            elements.messageLog.appendChild(logEntry);
            elements.messageLog.scrollTop = elements.messageLog.scrollHeight;
            
            messageCount++;
            elements.msgCount.textContent = messageCount;
        }

        function updateGazePoint(gazeData) {
            try {
                if (gazeData.valid && gazeData.x !== undefined && gazeData.y !== undefined) {
                    const canvas = elements.gazeCanvas;
                    const rect = canvas.getBoundingClientRect();
                    
                    // 转换坐标到画布坐标系
                    const x = gazeData.x * rect.width;
                    const y = gazeData.y * rect.height;
                    
                    // 显示当前视点
                    elements.gazePoint.style.left = x + 'px';
                    elements.gazePoint.style.top = y + 'px';
                    elements.gazePoint.style.display = 'block';
                    
                    // 添加轨迹点
                    const trail = document.createElement('div');
                    trail.className = 'gaze-trail';
                    trail.style.left = x + 'px';
                    trail.style.top = y + 'px';
                    canvas.appendChild(trail);
                    
                    trails.push(trail);
                    if (trails.length > maxTrails) {
                        const oldTrail = trails.shift();
                        if (oldTrail.parentNode) {
                            oldTrail.parentNode.removeChild(oldTrail);
                        }
                    }
                    
                    // 更新数据显示
                    elements.gazeX.textContent = gazeData.x.toFixed(3);
                    elements.gazeY.textContent = gazeData.y.toFixed(3);
                    elements.gazeDist.textContent = gazeData.dist ? gazeData.dist.toFixed(1) : '--';
                    elements.gazeDuration.textContent = gazeData.duration || '--';
                    elements.gazeValid.textContent = gazeData.valid ? '✓' : '✗';
                    elements.gazeSkew.textContent = gazeData.skew ? '⚠️' : '✓';
                    
                    gazeCount++;
                    elements.gazeCount.textContent = gazeCount;
                } else {
                    elements.gazePoint.style.display = 'none';
                    elements.gazeX.textContent = '--';
                    elements.gazeY.textContent = '--';
                    elements.gazeValid.textContent = '✗';
                }
            } catch (error) {
                console.error('更新眼动数据失败:', error);
                errorCount++;
                elements.errorCount.textContent = errorCount;
            }
        }

        function clearLog() {
            elements.messageLog.innerHTML = '';
            messageCount = 0;
            gazeCount = 0;
            errorCount = 0;
            elements.msgCount.textContent = '0';
            elements.gazeCount.textContent = '0';
            elements.errorCount.textContent = '0';
            
            // 清除轨迹
            trails.forEach(trail => {
                if (trail.parentNode) {
                    trail.parentNode.removeChild(trail);
                }
            });
            trails = [];
            elements.gazePoint.style.display = 'none';
        }

        function sendMessage(message) {
            if (ws && isConnected) {
                try {
                    ws.send(message);
                    logMessage(`发送: ${message}`, 'info');
                } catch (error) {
                    logMessage(`发送失败: ${error.message}`, 'error');
                    errorCount++;
                    elements.errorCount.textContent = errorCount;
                }
            } else {
                logMessage('WebSocket未连接', 'error');
            }
        }

        function connect() {
            if (isConnected) return;
            
            const url = elements.wsUrl.value.trim();
            if (!url) {
                logMessage('请输入WebSocket地址', 'error');
                return;
            }
            
            updateStatus('连接中...', 'connecting');
            logMessage(`尝试连接: ${url}`);
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    updateStatus('已连接', 'connected');
                    logMessage('WebSocket连接成功！', 'success');
                    
                    elements.connectBtn.disabled = true;
                    elements.disconnectBtn.disabled = false;
                    elements.pingBtn.disabled = false;
                    elements.statusBtn.disabled = false;
                    elements.startBtn.disabled = false;
                    elements.stopBtn.disabled = false;
                    
                    elements.connCount.textContent = '1';
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        
                        if (data.type === 'welcome') {
                            logMessage(`服务器欢迎: ${data.message}`, 'success');
                        } else if (data.type === 'pong') {
                            logMessage('收到Pong响应', 'success');
                        } else if (data.type === 'status') {
                            logMessage(`服务器状态 - 连接数: ${data.connections}, 广播数: ${data.broadcasts}, 消息数: ${data.messages}`, 'success');
                        } else if (data.action) {
                            // 处理校准消息
                            logMessage(`收到校准消息: ${data.action}`, 'info');
                        } else if (data.valid !== undefined) {
                            // 处理眼动数据
                            updateGazePoint(data);
                        } else {
                            logMessage(`收到未知消息: ${event.data}`, 'info');
                        }
                    } catch (error) {
                        logMessage(`解析消息失败: ${event.data}`, 'error');
                        errorCount++;
                        elements.errorCount.textContent = errorCount;
                    }
                };
                
                ws.onerror = function(error) {
                    logMessage(`WebSocket错误: ${error}`, 'error');
                    errorCount++;
                    elements.errorCount.textContent = errorCount;
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    updateStatus('已断开', 'disconnected');
                    logMessage(`连接关闭 - 代码: ${event.code}, 原因: ${event.reason}`, 'info');
                    
                    elements.connectBtn.disabled = false;
                    elements.disconnectBtn.disabled = true;
                    elements.pingBtn.disabled = true;
                    elements.statusBtn.disabled = true;
                    elements.startBtn.disabled = true;
                    elements.stopBtn.disabled = true;
                    
                    elements.connCount.textContent = '0';
                };
                
            } catch (error) {
                updateStatus('连接失败', 'disconnected');
                logMessage(`连接失败: ${error.message}`, 'error');
                errorCount++;
                elements.errorCount.textContent = errorCount;
            }
        }

        function disconnect() {
            if (ws && isConnected) {
                ws.close();
                logMessage('主动断开连接', 'info');
            }
        }

        // 事件监听
        elements.connectBtn.addEventListener('click', connect);
        elements.disconnectBtn.addEventListener('click', disconnect);
        elements.clearBtn.addEventListener('click', clearLog);
        elements.pingBtn.addEventListener('click', () => sendMessage('ping'));
        elements.statusBtn.addEventListener('click', () => sendMessage('status'));
        elements.startBtn.addEventListener('click', () => sendMessage('start_tracking'));
        elements.stopBtn.addEventListener('click', () => sendMessage('stop_tracking'));

        // 回车连接
        elements.wsUrl.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                connect();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            logMessage('WebSocket测试页面已加载');
            logMessage('请输入设备IP地址，格式：ws://[设备IP]:9200');
            logMessage('确保设备上的MPD应用正在运行并启动了眼动追踪服务');
        });

        // 页面卸载时断开连接
        window.addEventListener('beforeunload', function() {
            if (ws && isConnected) {
                ws.close();
            }
        });
    </script>
</body>
</html>
