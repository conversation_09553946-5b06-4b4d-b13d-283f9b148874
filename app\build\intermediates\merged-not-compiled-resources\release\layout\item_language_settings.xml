<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_language_root">

    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:duplicateParentState="true"
        android:src="@drawable/selector_common_radio_button"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <ImageView
        android:id="@+id/iv_language"
        android:layout_width="15dp"
        android:layout_height="15dp"
        tools:src="@drawable/ic_language_zh_cn"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_select"/>

    <TextView
        android:id="@+id/tv_language"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="简体中文"
        android:textColor="@color/color_333333"
        android:textSize="18sp"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_language"/>

</androidx.constraintlayout.widget.ConstraintLayout>