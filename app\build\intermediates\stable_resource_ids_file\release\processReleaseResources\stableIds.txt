com.airdoc.mpd:xml/network_security_config = 0x7f150004
com.airdoc.mpd:xml/log_file_paths = 0x7f150003
com.airdoc.mpd:xml/file_paths = 0x7f150002
com.airdoc.mpd:xml/data_extraction_rules = 0x7f150001
com.airdoc.mpd:xml/backup_rules = 0x7f150000
com.airdoc.mpd:styleable/include = 0x7f1400a3
com.airdoc.mpd:styleable/ViewTransition = 0x7f1400a2
com.airdoc.mpd:styleable/ViewPager2 = 0x7f1400a0
com.airdoc.mpd:styleable/View = 0x7f14009e
com.airdoc.mpd:styleable/VerticalMarqueeTextView = 0x7f14009d
com.airdoc.mpd:styleable/Variant = 0x7f14009c
com.airdoc.mpd:styleable/Transform = 0x7f14009a
com.airdoc.mpd:styleable/Tooltip = 0x7f140099
com.airdoc.mpd:styleable/Toolbar = 0x7f140098
com.airdoc.mpd:styleable/ThemeEnforcement = 0x7f140097
com.airdoc.mpd:styleable/TextInputLayout = 0x7f140096
com.airdoc.mpd:styleable/TextEffects = 0x7f140094
com.airdoc.mpd:styleable/TextAppearance = 0x7f140093
com.airdoc.mpd:styleable/TabLayout = 0x7f140092
com.airdoc.mpd:styleable/SwitchCompat = 0x7f14008f
com.airdoc.mpd:styleable/StateSet = 0x7f14008e
com.airdoc.mpd:styleable/StateListDrawableItem = 0x7f14008d
com.airdoc.mpd:styleable/Spinner = 0x7f14008a
com.airdoc.mpd:styleable/SnackbarLayout = 0x7f140089
com.airdoc.mpd:styleable/ShapeableImageView = 0x7f140084
com.airdoc.mpd:styleable/ShapeAppearance = 0x7f140083
com.airdoc.mpd:styleable/SearchBar = 0x7f140081
com.airdoc.mpd:styleable/ScrimInsetsFrameLayout = 0x7f14007f
com.airdoc.mpd:styleable/PlayerView = 0x7f140076
com.airdoc.mpd:styleable/PlayerControlView = 0x7f140075
com.airdoc.mpd:styleable/NavigationRailView = 0x7f140071
com.airdoc.mpd:styleable/MotionTelltales = 0x7f14006e
com.airdoc.mpd:styleable/MotionScene = 0x7f14006d
com.airdoc.mpd:styleable/MotionLayout = 0x7f14006c
com.airdoc.mpd:styleable/MotionEffect = 0x7f140069
com.airdoc.mpd:styleable/MockView = 0x7f140067
com.airdoc.mpd:styleable/MenuView = 0x7f140066
com.airdoc.mpd:styleable/MenuGroup = 0x7f140064
com.airdoc.mpd:styleable/MaterialToolbar = 0x7f140063
com.airdoc.mpd:styleable/MaterialTimePicker = 0x7f140062
com.airdoc.mpd:styleable/MaterialTextView = 0x7f140061
com.airdoc.mpd:styleable/MaterialTextAppearance = 0x7f140060
com.airdoc.mpd:styleable/MaterialShape = 0x7f14005e
com.airdoc.mpd:styleable/MaterialRadioButton = 0x7f14005d
com.airdoc.mpd:styleable/MaterialCardView = 0x7f140059
com.airdoc.mpd:styleable/MaterialCalendarItem = 0x7f140058
com.airdoc.mpd:styleable/MaterialButtonToggleGroup = 0x7f140056
com.airdoc.mpd:styleable/MaterialAlertDialogTheme = 0x7f140053
com.airdoc.mpd:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f13004a
com.airdoc.mpd:styleable/LottieAnimationView = 0x7f140051
com.airdoc.mpd:drawable/abc_ic_search_api_material = 0x7f080049
com.airdoc.mpd:styleable/ListPopupWindow = 0x7f14004f
com.airdoc.mpd:styleable/LinearLayoutCompat_Layout = 0x7f14004d
com.airdoc.mpd:styleable/Layout = 0x7f14004a
com.airdoc.mpd:string/common_google_play_services_install_text = 0x7f120033
com.airdoc.mpd:styleable/KeyTimeCycle = 0x7f140048
com.airdoc.mpd:styleable/KeyPosition = 0x7f140047
com.airdoc.mpd:styleable/KeyFramesVelocity = 0x7f140046
com.airdoc.mpd:color/material_dynamic_neutral10 = 0x7f06022c
com.airdoc.mpd:styleable/KeyFramesAcceleration = 0x7f140045
com.airdoc.mpd:styleable/KeyFrame = 0x7f140044
com.airdoc.mpd:styleable/KeyCycle = 0x7f140043
com.airdoc.mpd:styleable/FlowLayout = 0x7f140038
com.airdoc.mpd:styleable/FloatingActionButton_Behavior_Layout = 0x7f140037
com.airdoc.mpd:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f140035
com.airdoc.mpd:styleable/ExtendedFloatingActionButton = 0x7f140034
com.airdoc.mpd:styleable/DrawerLayout = 0x7f140033
com.airdoc.mpd:styleable/DrawerArrowToggle = 0x7f140032
com.airdoc.mpd:styleable/CustomAttribute = 0x7f140030
com.airdoc.mpd:attr/behavior_significantVelocityThreshold = 0x7f040079
com.airdoc.mpd:styleable/CoordinatorLayout_Layout = 0x7f14002f
com.airdoc.mpd:styleable/CoordinatorLayout = 0x7f14002e
com.airdoc.mpd:styleable/ConstraintLayout_ReactiveGuide = 0x7f14002a
com.airdoc.mpd:attr/circleRadius = 0x7f0400dc
com.airdoc.mpd:styleable/ColorStateListItem = 0x7f140026
com.airdoc.mpd:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f130113
com.airdoc.mpd:macro/m3_comp_slider_inactive_track_color = 0x7f0e0111
com.airdoc.mpd:styleable/CollapsingToolbarLayout_Layout = 0x7f140025
com.airdoc.mpd:styleable/CircularProgressIndicator = 0x7f140021
com.airdoc.mpd:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f080048
com.airdoc.mpd:styleable/ChipGroup = 0x7f14001f
com.airdoc.mpd:styleable/Capability = 0x7f14001a
com.airdoc.mpd:styleable/ButtonBarLayout = 0x7f140019
com.airdoc.mpd:styleable/AppCompatImageView = 0x7f14000e
com.airdoc.mpd:styleable/AppBarLayout_Layout = 0x7f14000c
com.airdoc.mpd:drawable/exo_icon_repeat_off = 0x7f0800d8
com.airdoc.mpd:styleable/AppBarLayoutStates = 0x7f14000b
com.airdoc.mpd:styleable/AlertDialog = 0x7f140006
com.airdoc.mpd:styleable/ActivityChooserView = 0x7f140005
com.airdoc.mpd:id/content_container = 0x7f0a0096
com.airdoc.mpd:styleable/ActionMenuView = 0x7f140003
com.airdoc.mpd:styleable/ActionMenuItemView = 0x7f140002
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f1301ac
com.airdoc.mpd:styleable/ActionBarLayout = 0x7f140001
com.airdoc.mpd:attr/sideSheetModalStyle = 0x7f04040a
com.airdoc.mpd:styleable/ActionBar = 0x7f140000
com.airdoc.mpd:style/shape_image_top_round_12dp = 0x7f13048b
com.airdoc.mpd:style/shape_image_round_5dp = 0x7f13048a
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f13047e
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f13047d
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f13047b
com.airdoc.mpd:layout/abc_search_dropdown_item_icons_2line = 0x7f0d0018
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Display = 0x7f130479
com.airdoc.mpd:id/exo_progress = 0x7f0a00f3
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f130190
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker = 0x7f130476
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130474
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f130472
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f130471
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f13046e
com.airdoc.mpd:attr/errorIconTintMode = 0x7f0401c0
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f13046a
com.airdoc.mpd:style/Widget.MaterialComponents.Slider = 0x7f130462
com.airdoc.mpd:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f130047
com.airdoc.mpd:style/Widget.MaterialComponents.ShapeableImageView = 0x7f130461
com.airdoc.mpd:style/Widget.MaterialComponents.PopupMenu = 0x7f13045c
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationRailView = 0x7f130456
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f130453
com.airdoc.mpd:string/str_please_enter_detection_code = 0x7f12011d
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f130452
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f13044b
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f13044a
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f130449
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f130446
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130444
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f130442
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f13043f
com.airdoc.mpd:attr/tabMinWidth = 0x7f04045b
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f13043e
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar = 0x7f13043d
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f13043c
com.airdoc.mpd:style/Widget.MaterialComponents.FloatingActionButton = 0x7f130439
com.airdoc.mpd:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f130438
com.airdoc.mpd:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f130437
com.airdoc.mpd:id/decelerateAndComplete = 0x7f0a00a4
com.airdoc.mpd:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f130436
com.airdoc.mpd:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f130433
com.airdoc.mpd:dimen/m3_alert_dialog_action_top_padding = 0x7f0700c1
com.airdoc.mpd:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f130432
com.airdoc.mpd:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f13042f
com.airdoc.mpd:style/Widget.MaterialComponents.Chip.Choice = 0x7f13042b
com.airdoc.mpd:style/Base.Widget.MaterialComponents.Snackbar = 0x7f13011c
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f130424
com.airdoc.mpd:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f13041b
com.airdoc.mpd:style/Widget.MaterialComponents.BottomNavigationView = 0x7f130417
com.airdoc.mpd:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f130412
com.airdoc.mpd:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f13040f
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f13019d
com.airdoc.mpd:color/m3_ref_palette_secondary90 = 0x7f06015a
com.airdoc.mpd:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f13040d
com.airdoc.mpd:style/Widget.MaterialComponents.ActionMode = 0x7f13040b
com.airdoc.mpd:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f13040a
com.airdoc.mpd:dimen/m3_searchview_height = 0x7f0701ff
com.airdoc.mpd:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0e014c
com.airdoc.mpd:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f130409
com.airdoc.mpd:style/Widget.Material3.Toolbar.Surface = 0x7f130405
com.airdoc.mpd:style/Widget.MaterialComponents.CheckedTextView = 0x7f130429
com.airdoc.mpd:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1300a0
com.airdoc.mpd:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0e0134
com.airdoc.mpd:style/Widget.Material3.Toolbar.OnSurface = 0x7f130404
com.airdoc.mpd:drawable/tooltip_frame_light = 0x7f0801c8
com.airdoc.mpd:style/Widget.Material3.Toolbar = 0x7f130403
com.airdoc.mpd:string/exo_controls_seek_bar_description = 0x7f120055
com.airdoc.mpd:layout/abc_alert_dialog_material = 0x7f0d0009
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f130402
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0601b2
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130401
com.airdoc.mpd:animator/mtrl_chip_state_list_anim = 0x7f020018
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1303fd
com.airdoc.mpd:attr/hintEnabled = 0x7f04022c
com.airdoc.mpd:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1303f9
com.airdoc.mpd:id/mtrl_picker_header_title_and_selection = 0x7f0a01af
com.airdoc.mpd:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1303f8
com.airdoc.mpd:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1303f7
com.airdoc.mpd:style/Widget.Material3.TabLayout = 0x7f1303f4
com.airdoc.mpd:style/Widget.Material3.Snackbar.TextView = 0x7f1303f3
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f13044f
com.airdoc.mpd:style/Widget.Material3.Snackbar = 0x7f1303f1
com.airdoc.mpd:style/Widget.Material3.Slider.Label = 0x7f1303f0
com.airdoc.mpd:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0601da
com.airdoc.mpd:style/Widget.Material3.Slider = 0x7f1303ef
com.airdoc.mpd:drawable/mtrl_switch_thumb_checked = 0x7f080198
com.airdoc.mpd:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1303ee
com.airdoc.mpd:styleable/BaseProgressIndicator = 0x7f140015
com.airdoc.mpd:style/Widget.Material3.SideSheet.Detached = 0x7f1303ec
com.airdoc.mpd:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f0700bd
com.airdoc.mpd:style/Widget.Material3.SideSheet = 0x7f1303eb
com.airdoc.mpd:style/Widget.Material3.SearchBar = 0x7f1303e6
com.airdoc.mpd:id/search_src_text = 0x7f0a021d
com.airdoc.mpd:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1303e2
com.airdoc.mpd:style/Widget.Material3.NavigationView = 0x7f1303df
com.airdoc.mpd:string/str_phone_last_number_ = 0x7f12011b
com.airdoc.mpd:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f070145
com.airdoc.mpd:style/Widget.Material3.NavigationRailView.Badge = 0x7f1303de
com.airdoc.mpd:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0e0122
com.airdoc.mpd:style/Widget.Material3.NavigationRailView = 0x7f1303dc
com.airdoc.mpd:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0e000c
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1303d8
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1303d7
com.airdoc.mpd:id/exo_progress_placeholder = 0x7f0a00f4
com.airdoc.mpd:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1303d2
com.airdoc.mpd:styleable/Insets = 0x7f140041
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1303ce
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Year = 0x7f1303cd
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1303c9
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f13046d
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1303c6
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1303c1
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f13044d
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1303c0
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1303d6
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1303bd
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1303bc
com.airdoc.mpd:anim/design_snackbar_out = 0x7f01001c
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar = 0x7f1303ba
com.airdoc.mpd:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1303b9
com.airdoc.mpd:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1303b7
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1303b5
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary60 = 0x7f06010b
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1303b4
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1303b2
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1303b1
com.airdoc.mpd:layout/activity_hrv = 0x7f0d0021
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1303af
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1303ae
com.airdoc.mpd:color/abc_color_highlight_material = 0x7f060004
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1303ab
com.airdoc.mpd:attr/itemTextAppearanceInactive = 0x7f04026f
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1303aa
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1303a9
com.airdoc.mpd:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f130344
com.airdoc.mpd:style/Widget.Material3.CompoundButton.Switch = 0x7f1303a1
com.airdoc.mpd:style/Widget.Material3.CompoundButton.CheckBox = 0x7f13039e
com.airdoc.mpd:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f13039d
com.airdoc.mpd:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f13039a
com.airdoc.mpd:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f130395
com.airdoc.mpd:styleable/KeyTrigger = 0x7f140049
com.airdoc.mpd:drawable/abc_cab_background_top_material = 0x7f080039
com.airdoc.mpd:style/Widget.Material3.Chip.Input = 0x7f130390
com.airdoc.mpd:style/Widget.Material3.CardView.Elevated = 0x7f130388
com.airdoc.mpd:style/Widget.Material3.Button.TonalButton.Icon = 0x7f130386
com.airdoc.mpd:id/flip = 0x7f0a0118
com.airdoc.mpd:style/Widget.Material3.Button.TonalButton = 0x7f130385
com.airdoc.mpd:style/Widget.Material3.Button.TextButton.Icon = 0x7f130383
com.airdoc.mpd:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f130382
com.airdoc.mpd:style/Widget.Material3.Button.TextButton = 0x7f13037f
com.airdoc.mpd:style/Widget.Material3.Button.IconButton.Outlined = 0x7f13037c
com.airdoc.mpd:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f13037b
com.airdoc.mpd:id/clip_horizontal = 0x7f0a0089
com.airdoc.mpd:style/Widget.Material3.Button.IconButton = 0x7f130379
com.airdoc.mpd:style/Widget.Material3.Button = 0x7f130375
com.airdoc.mpd:style/Widget.Material3.BottomNavigationView = 0x7f130370
com.airdoc.mpd:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f07012a
com.airdoc.mpd:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f13036e
com.airdoc.mpd:style/Widget.Material3.Badge = 0x7f13036b
com.airdoc.mpd:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f130369
com.airdoc.mpd:attr/materialCalendarHeaderConfirmButton = 0x7f040306
com.airdoc.mpd:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f130368
com.airdoc.mpd:style/Widget.Material3.ActionMode = 0x7f130365
com.airdoc.mpd:attr/onShow = 0x7f040382
com.airdoc.mpd:style/Widget.Material3.ActionBar.Solid = 0x7f130364
com.airdoc.mpd:drawable/icon_back_white_coarse = 0x7f080150
com.airdoc.mpd:style/Widget.Design.Snackbar = 0x7f130360
com.airdoc.mpd:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f13035f
com.airdoc.mpd:style/Widget.Design.CollapsingToolbar = 0x7f13035c
com.airdoc.mpd:style/Widget.Design.AppBarLayout = 0x7f130359
com.airdoc.mpd:id/selection_type = 0x7f0a0221
com.airdoc.mpd:style/Widget.Compat.NotificationActionContainer = 0x7f130357
com.airdoc.mpd:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f130354
com.airdoc.mpd:style/Widget.AppCompat.Spinner.DropDown = 0x7f130350
com.airdoc.mpd:style/Widget.AppCompat.Spinner = 0x7f13034f
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1303b0
com.airdoc.mpd:style/Widget.AppCompat.SeekBar.Discrete = 0x7f13034e
com.airdoc.mpd:style/Widget.AppCompat.SearchView.ActionBar = 0x7f13034c
com.airdoc.mpd:style/Widget.AppCompat.RatingBar.Small = 0x7f13034a
com.airdoc.mpd:id/honorRequest = 0x7f0a012d
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f1301b1
com.airdoc.mpd:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f130347
com.airdoc.mpd:style/Widget.AppCompat.ProgressBar = 0x7f130346
com.airdoc.mpd:dimen/mtrl_progress_circular_size_small = 0x7f0702f8
com.airdoc.mpd:style/Widget.AppCompat.ListPopupWindow = 0x7f13033f
com.airdoc.mpd:style/Widget.AppCompat.ListMenuView = 0x7f13033e
com.airdoc.mpd:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f13033d
com.airdoc.mpd:id/uniform = 0x7f0a02ab
com.airdoc.mpd:style/Widget.AppCompat.Light.SearchView = 0x7f13033c
com.airdoc.mpd:styleable/ClockHandView = 0x7f140023
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f070182
com.airdoc.mpd:style/Widget.AppCompat.Light.PopupMenu = 0x7f13033a
com.airdoc.mpd:id/postLayout = 0x7f0a01ee
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f130334
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f130332
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f13032f
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f13032c
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f13032b
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f130329
com.airdoc.mpd:attr/customReference = 0x7f040176
com.airdoc.mpd:style/Widget.AppCompat.EditText = 0x7f130326
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f13032a
com.airdoc.mpd:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f130325
com.airdoc.mpd:attr/contentPaddingLeft = 0x7f04014e
com.airdoc.mpd:style/Widget.AppCompat.DrawerArrowToggle = 0x7f130324
com.airdoc.mpd:style/Widget.AppCompat.CompoundButton.Switch = 0x7f130323
com.airdoc.mpd:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f130322
com.airdoc.mpd:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f130320
com.airdoc.mpd:styleable/LinearLayoutCompat = 0x7f14004c
com.airdoc.mpd:layout/mtrl_calendar_month_navigation = 0x7f0d0071
com.airdoc.mpd:style/Widget.Material3.SearchView = 0x7f1303e8
com.airdoc.mpd:style/Widget.AppCompat.ButtonBar = 0x7f13031f
com.airdoc.mpd:style/Widget.AppCompat.Button.Small = 0x7f13031e
com.airdoc.mpd:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f13031c
com.airdoc.mpd:style/Widget.AppCompat.ActionMode = 0x7f130316
com.airdoc.mpd:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.airdoc.mpd:color/material_grey_100 = 0x7f06026c
com.airdoc.mpd:macro/m3_comp_fab_secondary_icon_color = 0x7f0e003d
com.airdoc.mpd:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f130314
com.airdoc.mpd:style/Widget.AppCompat.ActionBar.TabBar = 0x7f130310
com.airdoc.mpd:style/Widget.AppCompat.ActionBar = 0x7f13030e
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f13030c
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13030a
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f130307
com.airdoc.mpd:style/Widget.Material3.BottomAppBar = 0x7f13036d
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1302e7
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f130306
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f130304
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f130303
com.airdoc.mpd:attr/textEndPadding = 0x7f04049e
com.airdoc.mpd:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1300ac
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f130302
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1302ff
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f130441
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1302fd
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1302fc
com.airdoc.mpd:dimen/m3_card_elevation = 0x7f07010d
com.airdoc.mpd:color/m3_sys_color_dark_on_secondary_container = 0x7f06017f
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1302fb
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1302f9
com.airdoc.mpd:attr/tooltipStyle = 0x7f0404e6
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1302f7
com.airdoc.mpd:style/Widget.AppCompat.ActionBar.TabText = 0x7f130311
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1302f6
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1302f5
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1302f4
com.airdoc.mpd:layout/abc_screen_content_include = 0x7f0d0014
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1302f3
com.airdoc.mpd:string/material_hour_suffix = 0x7f120093
com.airdoc.mpd:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f13041f
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1302f1
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1302f0
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1302ec
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1302ea
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1302e8
com.airdoc.mpd:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f0702a7
com.airdoc.mpd:color/material_dynamic_primary80 = 0x7f06024e
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents = 0x7f1302e6
com.airdoc.mpd:attr/actionModeSplitBackground = 0x7f04001d
com.airdoc.mpd:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1302e5
com.airdoc.mpd:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1302e2
com.airdoc.mpd:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1302e0
com.airdoc.mpd:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000f
com.airdoc.mpd:id/ll_test_version = 0x7f0a0174
com.airdoc.mpd:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1302dc
com.airdoc.mpd:style/ThemeOverlay.Material3.NavigationView = 0x7f1302d9
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1302d7
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary100 = 0x7f0600ec
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1302d5
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1302d3
com.airdoc.mpd:id/open_search_view_root = 0x7f0a01d6
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1302d1
com.airdoc.mpd:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1302d0
com.airdoc.mpd:style/ThemeOverlay.Material3.Light = 0x7f1302cf
com.airdoc.mpd:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1303e1
com.airdoc.mpd:attr/maxWidth = 0x7f040332
com.airdoc.mpd:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1302cd
com.airdoc.mpd:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1302cb
com.airdoc.mpd:styleable/AppCompatSeekBar = 0x7f14000f
com.airdoc.mpd:plurals/exo_controls_rewind_by_amount_description = 0x7f100001
com.airdoc.mpd:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1302c7
com.airdoc.mpd:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1302c5
com.airdoc.mpd:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1302c4
com.airdoc.mpd:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1302c1
com.airdoc.mpd:style/ThemeOverlay.Material3.Dialog = 0x7f1302bf
com.airdoc.mpd:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1302be
com.airdoc.mpd:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1302bd
com.airdoc.mpd:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1302bc
com.airdoc.mpd:style/ThemeOverlay.Material3.Chip = 0x7f1302b9
com.airdoc.mpd:attr/cornerFamily = 0x7f040158
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1302b8
com.airdoc.mpd:attr/colorPrimaryInverse = 0x7f040122
com.airdoc.mpd:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0602f6
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1302b3
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1302b2
com.airdoc.mpd:style/ThemeOverlay.Material3.Button = 0x7f1302b1
com.airdoc.mpd:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1302b0
com.airdoc.mpd:id/withinBounds = 0x7f0a02c3
com.airdoc.mpd:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1302af
com.airdoc.mpd:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1302ae
com.airdoc.mpd:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1302ad
com.airdoc.mpd:attr/trackColorInactive = 0x7f0404f0
com.airdoc.mpd:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1302ab
com.airdoc.mpd:id/center = 0x7f0a0070
com.airdoc.mpd:color/m3_ref_palette_secondary80 = 0x7f060159
com.airdoc.mpd:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1302aa
com.airdoc.mpd:style/ThemeOverlay.Material3.ActionBar = 0x7f1302a7
com.airdoc.mpd:style/ThemeOverlay.Material3 = 0x7f1302a6
com.airdoc.mpd:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1302a3
com.airdoc.mpd:style/ThemeOverlay.AppCompat.Dialog = 0x7f1302a2
com.airdoc.mpd:style/Theme.Mpd = 0x7f13029b
com.airdoc.mpd:style/Theme.MaterialComponents.NoActionBar = 0x7f130299
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f130294
com.airdoc.mpd:attr/motion_postLayoutCollision = 0x7f04036e
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f130292
com.airdoc.mpd:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f13028c
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Bridge = 0x7f13028b
com.airdoc.mpd:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f13028a
com.airdoc.mpd:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f130288
com.airdoc.mpd:attr/animateNavigationIcon = 0x7f040035
com.airdoc.mpd:color/m3_ref_palette_neutral17 = 0x7f060122
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f130287
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f130286
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.PlaybackSpeed = 0x7f130133
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f130285
com.airdoc.mpd:color/m3_text_button_foreground_color_selector = 0x7f060212
com.airdoc.mpd:raw/please_enter_detection_code = 0x7f110004
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f130283
com.airdoc.mpd:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0401d2
com.airdoc.mpd:style/ThemeOverlay.Material3.Dark = 0x7f1302bb
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f13027a
com.airdoc.mpd:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0e0138
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f130275
com.airdoc.mpd:dimen/design_bottom_navigation_active_item_max_width = 0x7f07005f
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f130274
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f130271
com.airdoc.mpd:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f13026d
com.airdoc.mpd:style/Theme.MaterialComponents = 0x7f13026c
com.airdoc.mpd:style/AppTheme = 0x7f13000b
com.airdoc.mpd:style/Theme.Material3.Light.DialogWhenLarge = 0x7f130269
com.airdoc.mpd:style/Theme.Material3.Light.Dialog.Alert = 0x7f130267
com.airdoc.mpd:style/Theme.Material3.DynamicColors.DayNight = 0x7f130262
com.airdoc.mpd:style/Theme.Material3.DayNight.NoActionBar = 0x7f13025f
com.airdoc.mpd:style/Theme.MaterialComponents.Bridge = 0x7f13026e
com.airdoc.mpd:anim/mtrl_card_lowers_interpolator = 0x7f01002c
com.airdoc.mpd:style/Theme.Material3.Dark.SideSheetDialog = 0x7f130258
com.airdoc.mpd:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.airdoc.mpd:drawable/exo_icon_stop = 0x7f0800dd
com.airdoc.mpd:style/Theme.Material3.Dark.NoActionBar = 0x7f130257
com.airdoc.mpd:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f13025e
com.airdoc.mpd:style/Theme.Material3.Dark.Dialog.Alert = 0x7f130254
com.airdoc.mpd:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f130252
com.airdoc.mpd:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f13031b
com.airdoc.mpd:style/Theme.Design = 0x7f13024b
com.airdoc.mpd:dimen/abc_floating_window_z = 0x7f07002f
com.airdoc.mpd:style/Theme.AppStartLoad = 0x7f13024a
com.airdoc.mpd:style/Widget.Material3.Chip.Suggestion = 0x7f130394
com.airdoc.mpd:id/shortcut = 0x7f0a0225
com.airdoc.mpd:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f130247
com.airdoc.mpd:raw/speech_3 = 0x7f11000c
com.airdoc.mpd:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130246
com.airdoc.mpd:macro/m3_comp_slider_handle_color = 0x7f0e0110
com.airdoc.mpd:style/Theme.AppCompat.Empty = 0x7f130241
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1303c7
com.airdoc.mpd:color/dim_foreground_material_light = 0x7f060078
com.airdoc.mpd:style/Theme.AppCompat.DialogWhenLarge = 0x7f130240
com.airdoc.mpd:style/Theme.AppCompat.Dialog.MinWidth = 0x7f13023f
com.airdoc.mpd:attr/layout_marginBaseline = 0x7f0402c0
com.airdoc.mpd:style/Theme.AppCompat.Dialog = 0x7f13023d
com.airdoc.mpd:style/Theme.AppCompat.DayNight.Dialog = 0x7f130238
com.airdoc.mpd:id/auto = 0x7f0a0056
com.airdoc.mpd:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f130232
com.airdoc.mpd:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f13022f
com.airdoc.mpd:dimen/m3_sys_elevation_level2 = 0x7f07020e
com.airdoc.mpd:macro/m3_comp_search_view_header_input_text_color = 0x7f0e00f6
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f13022d
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline2 = 0x7f130227
com.airdoc.mpd:attr/rotationCenterId = 0x7f0403cd
com.airdoc.mpd:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0e00db
com.airdoc.mpd:styleable/LegacyPlayerControlView = 0x7f14004b
com.airdoc.mpd:style/Widget.MaterialComponents.Chip.Entry = 0x7f13042c
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Caption = 0x7f130224
com.airdoc.mpd:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f0b0041
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Button = 0x7f130223
com.airdoc.mpd:attr/colorOnTertiaryFixed = 0x7f040119
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Body2 = 0x7f130222
com.airdoc.mpd:style/Theme.Material3.Light.NoActionBar = 0x7f13026a
com.airdoc.mpd:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0403c0
com.airdoc.mpd:style/TextAppearance.Material3.SearchView.Prefix = 0x7f13021c
com.airdoc.mpd:styleable/Badge = 0x7f140014
com.airdoc.mpd:color/design_default_color_primary_dark = 0x7f060066
com.airdoc.mpd:style/TextAppearance.Material3.SearchView = 0x7f13021b
com.airdoc.mpd:style/TextAppearance.Material3.SearchBar = 0x7f13021a
com.airdoc.mpd:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f130219
com.airdoc.mpd:attr/layout_constraintHeight_default = 0x7f040299
com.airdoc.mpd:style/TextAppearance.Material3.LabelMedium = 0x7f130217
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e00d2
com.airdoc.mpd:style/TextAppearance.Material3.LabelLarge = 0x7f130216
com.airdoc.mpd:style/TextAppearance.Material3.HeadlineLarge = 0x7f130213
com.airdoc.mpd:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f070228
com.airdoc.mpd:style/Widget.Material3.MaterialDivider = 0x7f1303d1
com.airdoc.mpd:style/TextAppearance.Material3.DisplaySmall = 0x7f130212
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f130209
com.airdoc.mpd:id/exo_repeat_toggle = 0x7f0a00f5
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f130204
com.airdoc.mpd:style/Theme.AppCompat.Light.NoActionBar = 0x7f130248
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f130203
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1301fc
com.airdoc.mpd:style/TextAppearance.Design.Tab = 0x7f1301fb
com.airdoc.mpd:style/TextAppearance.Design.HelperText = 0x7f1301f5
com.airdoc.mpd:style/TextAppearance.Design.Error = 0x7f1301f4
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1301f0
com.airdoc.mpd:string/str_ok = 0x7f120119
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Time = 0x7f1301ed
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Media = 0x7f1301ec
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Info = 0x7f1301e8
com.airdoc.mpd:layout/dialog_startup_mode_settings = 0x7f0d003c
com.airdoc.mpd:style/TextAppearance.Compat.Notification = 0x7f1301e7
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1301e5
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1301e4
com.airdoc.mpd:attr/materialIconButtonStyle = 0x7f04031d
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0e00cb
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1301df
com.airdoc.mpd:color/m3_sys_color_light_background = 0x7f0601dd
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1301dc
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1301da
com.airdoc.mpd:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1302c3
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1301d9
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1301d8
com.airdoc.mpd:style/TextAppearance.AppCompat.Title = 0x7f1301d1
com.airdoc.mpd:color/m3_button_ripple_color_selector = 0x7f06008f
com.airdoc.mpd:drawable/ic_m3_chip_check = 0x7f08012f
com.airdoc.mpd:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1301d0
com.airdoc.mpd:style/Widget.Material3.Chip.Input.Elevated = 0x7f130391
com.airdoc.mpd:style/TextAppearance.AppCompat.Subhead = 0x7f1301cf
com.airdoc.mpd:string/mtrl_exceed_max_badge_number_content_description = 0x7f1200b2
com.airdoc.mpd:style/TextAppearance.AppCompat.Small = 0x7f1301cd
com.airdoc.mpd:style/TextAppearance.AppCompat.Display1 = 0x7f1301bc
com.airdoc.mpd:style/TextAppearance.AppCompat.Body1 = 0x7f1301b8
com.airdoc.mpd:style/TextAppearance.AppCompat = 0x7f1301b7
com.airdoc.mpd:style/ShapeableImage_Rounded_40 = 0x7f1301b4
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1301b3
com.airdoc.mpd:color/abc_search_url_text_pressed = 0x7f06000f
com.airdoc.mpd:dimen/m3_badge_offset = 0x7f0700d7
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f1301b0
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f130309
com.airdoc.mpd:layout/design_navigation_item_subheader = 0x7f0d0032
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f1301ae
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f1301a8
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f1301a7
com.airdoc.mpd:attr/maxImageSize = 0x7f04032e
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f1301a5
com.airdoc.mpd:attr/textAppearanceLabelMedium = 0x7f040485
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f1301a4
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Button = 0x7f1301a1
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f13019f
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f070164
com.airdoc.mpd:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f130198
com.airdoc.mpd:style/ShapeAppearance.Material3.MediumComponent = 0x7f130197
com.airdoc.mpd:id/mtrl_calendar_selection_frame = 0x7f0a01a5
com.airdoc.mpd:style/ShapeAppearance.Material3.LargeComponent = 0x7f130196
com.airdoc.mpd:drawable/common_hollow_button_bg = 0x7f0800a6
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.Small = 0x7f130195
com.airdoc.mpd:integer/bottom_sheet_slide_duration = 0x7f0b0003
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.None = 0x7f130194
com.airdoc.mpd:styleable/KeyAttribute = 0x7f140042
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Inverse = 0x7f130021
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.Medium = 0x7f130193
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.Full = 0x7f130191
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f13018f
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f13018b
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f130189
com.airdoc.mpd:dimen/mtrl_switch_thumb_size = 0x7f070314
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f130187
com.airdoc.mpd:drawable/icon_back_black_coarse = 0x7f08014d
com.airdoc.mpd:attr/tickColor = 0x7f0404c3
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f130185
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f130181
com.airdoc.mpd:id/ghost_view = 0x7f0a011e
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f130180
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f13017f
com.airdoc.mpd:string/m3_sys_motion_easing_standard_decelerate = 0x7f12008e
com.airdoc.mpd:attr/icon = 0x7f040234
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f13017d
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f130178
com.airdoc.mpd:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f130174
com.airdoc.mpd:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0701c9
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f130172
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f13016d
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f13016c
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f130167
com.airdoc.mpd:style/Platform.V21.AppCompat.Light = 0x7f130161
com.airdoc.mpd:dimen/m3_comp_switch_track_width = 0x7f0701b5
com.airdoc.mpd:style/Platform.V21.AppCompat = 0x7f130160
com.airdoc.mpd:style/Platform.MaterialComponents.Dialog = 0x7f13015a
com.airdoc.mpd:attr/colorOnPrimaryContainer = 0x7f04010c
com.airdoc.mpd:string/side_sheet_behavior = 0x7f1200ed
com.airdoc.mpd:style/Platform.MaterialComponents = 0x7f130159
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130155
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f130282
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f130154
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1303fb
com.airdoc.mpd:anim/m3_motion_fade_exit = 0x7f010025
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f130152
com.airdoc.mpd:layout/abc_list_menu_item_radio = 0x7f0d0011
com.airdoc.mpd:style/TextAppearance.AppCompat.Medium = 0x7f1301c8
com.airdoc.mpd:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1302c9
com.airdoc.mpd:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1300fa
com.airdoc.mpd:style/Theme.Design.Light = 0x7f13024d
com.airdoc.mpd:attr/layout_goneMarginBottom = 0x7f0402b8
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130151
com.airdoc.mpd:color/m3_sys_color_dark_primary_container = 0x7f060187
com.airdoc.mpd:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0e0032
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f130150
com.airdoc.mpd:raw/mobile_browser = 0x7f110002
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f13014f
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f13014e
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f130149
com.airdoc.mpd:style/ExoStyledControls.TimeText.Separator = 0x7f130142
com.airdoc.mpd:style/ExoStyledControls.TimeText.Position = 0x7f130141
com.airdoc.mpd:style/Widget.Material3.CollapsingToolbar = 0x7f13039b
com.airdoc.mpd:style/ExoStyledControls.TimeText.Duration = 0x7f130140
com.airdoc.mpd:style/ExoStyledControls.TimeBar = 0x7f13013e
com.airdoc.mpd:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f0b003b
com.airdoc.mpd:style/ExoStyledControls.Button.Center.RewWithAmount = 0x7f13013d
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f130146
com.airdoc.mpd:attr/drawerArrowStyle = 0x7f04019e
com.airdoc.mpd:style/ExoStyledControls.Button.Center.Previous = 0x7f13013c
com.airdoc.mpd:style/ExoStyledControls.Button.Center.PlayPause = 0x7f13013b
com.airdoc.mpd:style/ExoStyledControls.Button.Center.Next = 0x7f13013a
com.airdoc.mpd:style/ExoStyledControls.Button.Center.FfwdWithAmount = 0x7f130139
com.airdoc.mpd:style/ExoStyledControls.Button.Center = 0x7f130138
com.airdoc.mpd:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f130418
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.VR = 0x7f130137
com.airdoc.mpd:integer/m3_sys_shape_corner_small_corner_family = 0x7f0b0028
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.Settings = 0x7f130135
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.OverflowHide = 0x7f130131
com.airdoc.mpd:attr/contentInsetEndWithActions = 0x7f040146
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.CC = 0x7f13012f
com.airdoc.mpd:attr/trackDecorationTint = 0x7f0404f3
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom = 0x7f13012d
com.airdoc.mpd:style/ExoMediaButton.Previous = 0x7f130128
com.airdoc.mpd:style/ExoMediaButton.Play = 0x7f130127
com.airdoc.mpd:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0e0171
com.airdoc.mpd:style/CardView.Dark = 0x7f130121
com.airdoc.mpd:dimen/mtrl_tooltip_cornerSize = 0x7f070322
com.airdoc.mpd:style/CardView = 0x7f130120
com.airdoc.mpd:drawable/exo_ic_chevron_right = 0x7f0800c1
com.airdoc.mpd:style/Base.Widget.MaterialComponents.Slider = 0x7f13011b
com.airdoc.mpd:style/Base.Widget.Material3.TabLayout = 0x7f13010f
com.airdoc.mpd:color/m3_sys_color_primary_fixed_dim = 0x7f060206
com.airdoc.mpd:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f13010c
com.airdoc.mpd:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f13010b
com.airdoc.mpd:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f13010a
com.airdoc.mpd:string/common_google_play_services_install_title = 0x7f120034
com.airdoc.mpd:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f130108
com.airdoc.mpd:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f130107
com.airdoc.mpd:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f130321
com.airdoc.mpd:style/Base.Widget.Material3.CollapsingToolbar = 0x7f130103
com.airdoc.mpd:style/Base.Widget.Material3.BottomNavigationView = 0x7f130100
com.airdoc.mpd:style/Base.Widget.Design.TabLayout = 0x7f1300fd
com.airdoc.mpd:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1300fc
com.airdoc.mpd:style/Base.Widget.AppCompat.TextView = 0x7f1300f9
com.airdoc.mpd:style/Base.Widget.AppCompat.SeekBar = 0x7f1300f5
com.airdoc.mpd:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1300f2
com.airdoc.mpd:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1300f1
com.airdoc.mpd:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f130464
com.airdoc.mpd:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1300e9
com.airdoc.mpd:style/Base.Widget.AppCompat.ListView = 0x7f1300e8
com.airdoc.mpd:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1300e7
com.airdoc.mpd:style/Base.Widget.AppCompat.ListMenuView = 0x7f1300e6
com.airdoc.mpd:attr/titleTextColor = 0x7f0404dc
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1300e1
com.airdoc.mpd:color/material_personalized__highlighted_text = 0x7f060281
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1300de
com.airdoc.mpd:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1300d7
com.airdoc.mpd:style/Base.Widget.AppCompat.ButtonBar = 0x7f1300d4
com.airdoc.mpd:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1300d0
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f13007b
com.airdoc.mpd:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1300cd
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionMode = 0x7f1300cb
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f130330
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1300ca
com.airdoc.mpd:attr/boxStrokeWidth = 0x7f04008f
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1300c7
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1300c6
com.airdoc.mpd:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0e0031
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1300c5
com.airdoc.mpd:drawable/$avd_show_password__2 = 0x7f080005
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1300c4
com.airdoc.mpd:attr/textAppearanceSearchResultTitle = 0x7f04048f
com.airdoc.mpd:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1300c2
com.airdoc.mpd:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1300c0
com.airdoc.mpd:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1300be
com.airdoc.mpd:layout/mtrl_layout_snackbar = 0x7f0d0075
com.airdoc.mpd:style/Base.V7.Theme.AppCompat.Light = 0x7f1300bd
com.airdoc.mpd:style/Base.V7.Theme.AppCompat = 0x7f1300bb
com.airdoc.mpd:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f13011d
com.airdoc.mpd:style/Base.V28.Theme.AppCompat.Light = 0x7f1300ba
com.airdoc.mpd:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1300b8
com.airdoc.mpd:style/Base.V26.Theme.AppCompat = 0x7f1300b6
com.airdoc.mpd:style/Base.V24.Theme.Material3.Light = 0x7f1300b4
com.airdoc.mpd:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1300aa
com.airdoc.mpd:styleable/PreviewView = 0x7f140079
com.airdoc.mpd:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1300a7
com.airdoc.mpd:attr/drawableTopCompat = 0x7f04019d
com.airdoc.mpd:style/Base.V21.Theme.MaterialComponents = 0x7f1300a6
com.airdoc.mpd:style/Base.V21.Theme.AppCompat.Light = 0x7f1300a4
com.airdoc.mpd:style/Base.V21.Theme.AppCompat = 0x7f1300a2
com.airdoc.mpd:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f13008c
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130098
com.airdoc.mpd:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f130097
com.airdoc.mpd:id/dragStart = 0x7f0a00bc
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Light = 0x7f130096
com.airdoc.mpd:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f13039f
com.airdoc.mpd:color/material_dynamic_neutral_variant95 = 0x7f060243
com.airdoc.mpd:attr/state_error = 0x7f04042f
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f130095
com.airdoc.mpd:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f130090
com.airdoc.mpd:attr/dropDownBackgroundTint = 0x7f0401a1
com.airdoc.mpd:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f13008f
com.airdoc.mpd:style/Base.V14.Theme.Material3.Light = 0x7f13008e
com.airdoc.mpd:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0602ae
com.airdoc.mpd:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f13008d
com.airdoc.mpd:string/abc_menu_function_shortcut_label = 0x7f12000c
com.airdoc.mpd:style/Base.V14.Theme.Material3.Dark = 0x7f13008a
com.airdoc.mpd:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f130089
com.airdoc.mpd:attr/thumbTextPadding = 0x7f0404c0
com.airdoc.mpd:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f0700be
com.airdoc.mpd:dimen/m3_comp_navigation_rail_icon_size = 0x7f070170
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f13047a
com.airdoc.mpd:dimen/notification_action_icon_size = 0x7f070327
com.airdoc.mpd:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f130087
com.airdoc.mpd:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f130086
com.airdoc.mpd:string/material_hour_selection = 0x7f120092
com.airdoc.mpd:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f130085
com.airdoc.mpd:color/m3_dynamic_highlighted_text = 0x7f0600aa
com.airdoc.mpd:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f130084
com.airdoc.mpd:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f130083
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.Light = 0x7f13007f
com.airdoc.mpd:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f080037
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f13007e
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f130078
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f130076
com.airdoc.mpd:macro/m3_comp_search_bar_container_color = 0x7f0e00e6
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f1301a9
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f130074
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f130072
com.airdoc.mpd:attr/layout_insetEdge = 0x7f0402be
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f130070
com.airdoc.mpd:drawable/read_count_down_bg = 0x7f0801b3
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f13006d
com.airdoc.mpd:id/accessibility_custom_action_19 = 0x7f0a001b
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f13006c
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f13006a
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Bridge = 0x7f130067
com.airdoc.mpd:dimen/m3_badge_size = 0x7f0700d8
com.airdoc.mpd:style/TextAppearance.Material3.DisplayMedium = 0x7f130211
com.airdoc.mpd:style/Base.Theme.MaterialComponents = 0x7f130066
com.airdoc.mpd:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f130064
com.airdoc.mpd:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f130063
com.airdoc.mpd:style/Base.Theme.Material3.Light.Dialog = 0x7f130062
com.airdoc.mpd:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f13005e
com.airdoc.mpd:style/Base.Theme.Material3.Dark.Dialog = 0x7f13005c
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f13027c
com.airdoc.mpd:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f13005b
com.airdoc.mpd:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f130415
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f130059
com.airdoc.mpd:style/TextAppearance.Material3.HeadlineSmall = 0x7f130215
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f130058
com.airdoc.mpd:drawable/abc_text_select_handle_middle_mtrl = 0x7f080070
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f130057
com.airdoc.mpd:styleable/PopupWindow = 0x7f140077
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f130295
com.airdoc.mpd:attr/textAppearanceHeadlineMedium = 0x7f040482
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f130056
com.airdoc.mpd:attr/minTouchTargetSize = 0x7f04033b
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.Dialog = 0x7f130055
com.airdoc.mpd:attr/searchIcon = 0x7f0403e0
com.airdoc.mpd:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f130052
com.airdoc.mpd:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f130337
com.airdoc.mpd:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f130050
com.airdoc.mpd:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f13004f
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1301e1
com.airdoc.mpd:style/Base.Theme.AppCompat.Dialog = 0x7f13004e
com.airdoc.mpd:style/Base.Theme.AppCompat.CompactMenu = 0x7f13004d
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f130171
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1303b6
com.airdoc.mpd:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130049
com.airdoc.mpd:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f130048
com.airdoc.mpd:drawable/ic_mtrl_chip_checked_black = 0x7f080139
com.airdoc.mpd:style/Base.TextAppearance.MaterialComponents.Button = 0x7f130046
com.airdoc.mpd:style/Widget.Design.TextInputEditText = 0x7f130362
com.airdoc.mpd:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f130045
com.airdoc.mpd:dimen/mtrl_switch_thumb_icon_size = 0x7f070313
com.airdoc.mpd:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f070199
com.airdoc.mpd:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1200cf
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f13046f
com.airdoc.mpd:style/Base.TextAppearance.Material3.Search = 0x7f130044
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f07021a
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f13003f
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f13018a
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f13003d
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f13003a
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f080019
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f130039
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f130038
com.airdoc.mpd:attr/dividerPadding = 0x7f04018e
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f130036
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f130034
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f130033
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f130147
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Title = 0x7f130030
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Small = 0x7f13002c
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f13002a
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f130029
com.airdoc.mpd:attr/itemSpacing = 0x7f040269
com.airdoc.mpd:string/material_slider_range_end = 0x7f12009b
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f130027
com.airdoc.mpd:styleable/SideSheetBehavior_Layout = 0x7f140085
com.airdoc.mpd:drawable/abc_seekbar_track_material = 0x7f080065
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Display2 = 0x7f13001d
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Button = 0x7f13001a
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Body2 = 0x7f130019
com.airdoc.mpd:bool/abc_action_bar_embed_tabs = 0x7f050000
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Body1 = 0x7f130018
com.airdoc.mpd:attr/expandedTitleMarginBottom = 0x7f0401c9
com.airdoc.mpd:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f13043a
com.airdoc.mpd:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f130016
com.airdoc.mpd:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130015
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0e00e1
com.airdoc.mpd:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f130014
com.airdoc.mpd:attr/use_artwork = 0x7f040509
com.airdoc.mpd:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f130013
com.airdoc.mpd:style/Base.DialogWindowTitle.AppCompat = 0x7f130012
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0e0084
com.airdoc.mpd:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f13009d
com.airdoc.mpd:style/Base.Animation.AppCompat.DropDownUp = 0x7f13000f
com.airdoc.mpd:style/Base.Animation.AppCompat.Dialog = 0x7f13000e
com.airdoc.mpd:id/action_bar_spinner = 0x7f0a0038
com.airdoc.mpd:style/Base.AlertDialog.AppCompat.Light = 0x7f13000d
com.airdoc.mpd:color/material_dynamic_neutral40 = 0x7f060230
com.airdoc.mpd:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f13000a
com.airdoc.mpd:style/Animation.Material3.SideSheetDialog.Left = 0x7f130008
com.airdoc.mpd:style/Animation.Material3.BottomSheetDialog = 0x7f130006
com.airdoc.mpd:id/TOP_END = 0x7f0a000c
com.airdoc.mpd:style/Animation.AppCompat.Tooltip = 0x7f130004
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f13018d
com.airdoc.mpd:style/Animation.AppCompat.DropDownUp = 0x7f130003
com.airdoc.mpd:style/Animation.AppCompat.Dialog = 0x7f130002
com.airdoc.mpd:string/str_whatsapp_qr_code_tips = 0x7f120133
com.airdoc.mpd:dimen/material_helper_text_default_padding_top = 0x7f070258
com.airdoc.mpd:string/str_valid_until = 0x7f12012e
com.airdoc.mpd:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f130091
com.airdoc.mpd:string/str_upload = 0x7f12012d
com.airdoc.mpd:string/str_upgrade_process_may_take_a_few_minutes = 0x7f12012c
com.airdoc.mpd:string/str_update = 0x7f12012b
com.airdoc.mpd:styleable/LinearProgressIndicator = 0x7f14004e
com.airdoc.mpd:string/str_start_detection = 0x7f120127
com.airdoc.mpd:styleable/MaterialCheckBox = 0x7f14005a
com.airdoc.mpd:drawable/ic_clear_black_24 = 0x7f080117
com.airdoc.mpd:attr/carousel_emptyViewsBehavior = 0x7f0400ad
com.airdoc.mpd:string/str_select_scan_method = 0x7f120124
com.airdoc.mpd:attr/textAppearanceHeadline5 = 0x7f04047f
com.airdoc.mpd:id/open_search_view_edit_text = 0x7f0a01d4
com.airdoc.mpd:style/Base.Theme.Material3.Light = 0x7f130060
com.airdoc.mpd:id/ll_data_cache = 0x7f0a016a
com.airdoc.mpd:string/str_scanner_settings = 0x7f120123
com.airdoc.mpd:string/str_proactively_greet = 0x7f120120
com.airdoc.mpd:layout/abc_popup_menu_header_item_layout = 0x7f0d0012
com.airdoc.mpd:string/str_opening_date = 0x7f12011a
com.airdoc.mpd:string/str_none = 0x7f120117
com.airdoc.mpd:string/str_network_exception = 0x7f120116
com.airdoc.mpd:string/str_main_all_rights_reserved = 0x7f120113
com.airdoc.mpd:style/Theme.AppCompat.CompactMenu = 0x7f130235
com.airdoc.mpd:dimen/mtrl_progress_circular_size_medium = 0x7f0702f7
com.airdoc.mpd:string/str_language_settings = 0x7f120112
com.airdoc.mpd:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0701bc
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f130153
com.airdoc.mpd:string/str_id_s = 0x7f120110
com.airdoc.mpd:styleable/LoadingImageView = 0x7f140050
com.airdoc.mpd:styleable/AppCompatTheme = 0x7f140012
com.airdoc.mpd:style/Widget.AppCompat.ActionButton = 0x7f130313
com.airdoc.mpd:id/exo_duration = 0x7f0a00de
com.airdoc.mpd:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f130467
com.airdoc.mpd:string/str_h5_qr_code_tips = 0x7f12010c
com.airdoc.mpd:string/str_gender_male = 0x7f12010a
com.airdoc.mpd:id/direct = 0x7f0a00b0
com.airdoc.mpd:string/str_gender_female = 0x7f120109
com.airdoc.mpd:string/str_gender_ = 0x7f120108
com.airdoc.mpd:attr/lottie_imageAssetsFolder = 0x7f0402ea
com.airdoc.mpd:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f040357
com.airdoc.mpd:style/Widget.AppCompat.Button = 0x7f130319
com.airdoc.mpd:string/str_experience_now = 0x7f120106
com.airdoc.mpd:string/str_environment_configuration = 0x7f120104
com.airdoc.mpd:string/str_display_viewpoint = 0x7f120103
com.airdoc.mpd:style/ThemeOverlay.Material3.Snackbar = 0x7f1302dd
com.airdoc.mpd:string/str_discovering_new_versions = 0x7f120102
com.airdoc.mpd:id/rightToLeft = 0x7f0a0203
com.airdoc.mpd:string/str_detection_not_available = 0x7f1200ff
com.airdoc.mpd:string/str_details = 0x7f1200fc
com.airdoc.mpd:string/str_configuration_exception = 0x7f1200f9
com.airdoc.mpd:string/str_confidential = 0x7f1200f8
com.airdoc.mpd:dimen/m3_card_elevated_hovered_z = 0x7f07010c
com.airdoc.mpd:string/str_change = 0x7f1200f6
com.airdoc.mpd:string/str_camera_is_here = 0x7f1200f3
com.airdoc.mpd:string/side_sheet_accessibility_pane_title = 0x7f1200ec
com.airdoc.mpd:string/searchview_navigation_content_description = 0x7f1200eb
com.airdoc.mpd:string/searchview_clear_text_content_description = 0x7f1200ea
com.airdoc.mpd:string/searchbar_scrolling_view_behavior = 0x7f1200e9
com.airdoc.mpd:string/path_password_strike_through = 0x7f1200e7
com.airdoc.mpd:string/password_toggle_content_description = 0x7f1200e3
com.airdoc.mpd:string/mtrl_timepicker_confirm = 0x7f1200e2
com.airdoc.mpd:string/mtrl_timepicker_cancel = 0x7f1200e1
com.airdoc.mpd:color/material_dynamic_tertiary10 = 0x7f060260
com.airdoc.mpd:string/mtrl_switch_track_decoration_path = 0x7f1200df
com.airdoc.mpd:string/mtrl_switch_thumb_path_unchecked = 0x7f1200de
com.airdoc.mpd:string/mtrl_switch_thumb_path_pressed = 0x7f1200dd
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Medium = 0x7f130026
com.airdoc.mpd:string/mtrl_switch_thumb_path_morphing = 0x7f1200db
com.airdoc.mpd:attr/startIconScaleType = 0x7f040428
com.airdoc.mpd:dimen/abc_text_size_body_1_material = 0x7f07003f
com.airdoc.mpd:string/mtrl_picker_toggle_to_year_selection = 0x7f1200d8
com.airdoc.mpd:attr/trackTintMode = 0x7f0404f8
com.airdoc.mpd:color/design_default_color_secondary_variant = 0x7f060069
com.airdoc.mpd:string/mtrl_picker_today_description = 0x7f1200d4
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f130470
com.airdoc.mpd:string/mtrl_picker_text_input_year_abbr = 0x7f1200d3
com.airdoc.mpd:drawable/exo_icon_vr = 0x7f0800de
com.airdoc.mpd:string/mtrl_picker_text_input_day_abbr = 0x7f1200d1
com.airdoc.mpd:string/mtrl_picker_start_date_description = 0x7f1200cd
com.airdoc.mpd:style/Base.Widget.AppCompat.EditText = 0x7f1300dc
com.airdoc.mpd:string/mtrl_picker_range_header_title = 0x7f1200ca
com.airdoc.mpd:attr/actionBarTabBarStyle = 0x7f040008
com.airdoc.mpd:string/mtrl_picker_out_of_range = 0x7f1200c6
com.airdoc.mpd:attr/colorPrimarySurface = 0x7f040123
com.airdoc.mpd:integer/m3_sys_motion_duration_extra_long2 = 0x7f0b0013
com.airdoc.mpd:string/mtrl_picker_navigate_to_year_description = 0x7f1200c5
com.airdoc.mpd:string/mtrl_picker_invalid_format_use = 0x7f1200c2
com.airdoc.mpd:color/material_dynamic_neutral_variant60 = 0x7f06023f
com.airdoc.mpd:string/mtrl_picker_invalid_format_example = 0x7f1200c1
com.airdoc.mpd:drawable/exo_styled_controls_next = 0x7f0800fb
com.airdoc.mpd:string/mtrl_picker_invalid_format = 0x7f1200c0
com.airdoc.mpd:style/Base.CardView = 0x7f130011
com.airdoc.mpd:string/mtrl_picker_day_of_week_column_header = 0x7f1200be
com.airdoc.mpd:attr/scrimBackground = 0x7f0403d8
com.airdoc.mpd:string/mtrl_picker_date_header_unselected = 0x7f1200bd
com.airdoc.mpd:string/mtrl_picker_confirm = 0x7f1200ba
com.airdoc.mpd:dimen/abc_text_size_button_material = 0x7f070041
com.airdoc.mpd:string/mtrl_picker_announce_current_selection_none = 0x7f1200b8
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Tooltip = 0x7f130230
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1301e0
com.airdoc.mpd:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f0701ac
com.airdoc.mpd:string/mtrl_picker_announce_current_selection = 0x7f1200b7
com.airdoc.mpd:string/mtrl_picker_announce_current_range_selection = 0x7f1200b6
com.airdoc.mpd:string/mtrl_picker_a11y_prev_month = 0x7f1200b5
com.airdoc.mpd:style/Widget.Material3.DrawerLayout = 0x7f1303a2
com.airdoc.mpd:string/mtrl_exceed_max_badge_number_suffix = 0x7f1200b3
com.airdoc.mpd:string/mtrl_checkbox_state_description_unchecked = 0x7f1200b0
com.airdoc.mpd:attr/badgeText = 0x7f04005e
com.airdoc.mpd:string/mtrl_checkbox_button_path_checked = 0x7f1200aa
com.airdoc.mpd:color/m3_sys_color_light_inverse_primary = 0x7f0601e1
com.airdoc.mpd:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f08000f
com.airdoc.mpd:string/mtrl_checkbox_button_icon_path_name = 0x7f1200a9
com.airdoc.mpd:string/mtrl_checkbox_button_icon_path_checked = 0x7f1200a6
com.airdoc.mpd:string/material_timepicker_text_input_mode_description = 0x7f1200a4
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1303a6
com.airdoc.mpd:integer/m3_sys_motion_duration_long3 = 0x7f0b0018
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0e00e4
com.airdoc.mpd:string/material_timepicker_select_time = 0x7f1200a3
com.airdoc.mpd:string/material_timepicker_am = 0x7f12009e
com.airdoc.mpd:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1300cc
com.airdoc.mpd:string/abc_toolbar_collapse_description = 0x7f12001a
com.airdoc.mpd:string/material_slider_range_start = 0x7f12009c
com.airdoc.mpd:string/material_motion_easing_linear = 0x7f120099
com.airdoc.mpd:string/material_minute_suffix = 0x7f120095
com.airdoc.mpd:id/accessibility_custom_action_18 = 0x7f0a001a
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1303d9
com.airdoc.mpd:attr/showText = 0x7f0403fc
com.airdoc.mpd:string/material_minute_selection = 0x7f120094
com.airdoc.mpd:string/material_clock_display_divider = 0x7f12008f
com.airdoc.mpd:macro/m3_comp_menu_container_color = 0x7f0e0060
com.airdoc.mpd:attr/searchPrefixText = 0x7f0403e1
com.airdoc.mpd:string/m3_sys_motion_easing_standard_accelerate = 0x7f12008d
com.airdoc.mpd:attr/backgroundInsetBottom = 0x7f04004f
com.airdoc.mpd:string/m3_sys_motion_easing_linear = 0x7f12008b
com.airdoc.mpd:string/m3_sys_motion_easing_legacy_decelerate = 0x7f12008a
com.airdoc.mpd:string/m3_sys_motion_easing_emphasized_path_data = 0x7f120087
com.airdoc.mpd:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f120085
com.airdoc.mpd:string/m3_sys_motion_easing_emphasized = 0x7f120084
com.airdoc.mpd:string/m3_ref_typeface_plain_medium = 0x7f120082
com.airdoc.mpd:color/button_material_light = 0x7f060034
com.airdoc.mpd:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1300a3
com.airdoc.mpd:string/str_residual_degree = 0x7f120122
com.airdoc.mpd:string/m3_ref_typeface_brand_regular = 0x7f120081
com.airdoc.mpd:attr/scrimAnimationDuration = 0x7f0403d7
com.airdoc.mpd:style/Widget.AppCompat.ListView.DropDown = 0x7f130341
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1301d7
com.airdoc.mpd:string/m3_ref_typeface_brand_medium = 0x7f120080
com.airdoc.mpd:string/item_view_role_description = 0x7f12007e
com.airdoc.mpd:string/icon_content_description = 0x7f12007d
com.airdoc.mpd:string/exposed_dropdown_menu_content_description = 0x7f120079
com.airdoc.mpd:string/exo_track_unknown = 0x7f120077
com.airdoc.mpd:attr/resize_mode = 0x7f0403ca
com.airdoc.mpd:dimen/mtrl_navigation_rail_margin = 0x7f0702ed
com.airdoc.mpd:string/exo_track_surround_5_point_1 = 0x7f120075
com.airdoc.mpd:string/exo_track_selection_title_video = 0x7f120072
com.airdoc.mpd:color/material_personalized_primary_inverse_text_disable_only = 0x7f0602b3
com.airdoc.mpd:string/exo_track_selection_title_text = 0x7f120071
com.airdoc.mpd:string/exo_track_role_closed_captions = 0x7f12006b
com.airdoc.mpd:string/exo_track_role_alternate = 0x7f12006a
com.airdoc.mpd:style/TextAppearance.Material3.TitleLarge = 0x7f13021d
com.airdoc.mpd:string/exo_track_resolution = 0x7f120069
com.airdoc.mpd:string/exo_track_mono = 0x7f120068
com.airdoc.mpd:string/exo_item_list = 0x7f120066
com.airdoc.mpd:id/tv_select_qr_code_type_tips = 0x7f0a029e
com.airdoc.mpd:string/exo_download_removing = 0x7f120065
com.airdoc.mpd:string/exo_download_paused_for_wifi = 0x7f120064
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f130458
com.airdoc.mpd:attr/ad_marker_width = 0x7f040029
com.airdoc.mpd:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0e011e
com.airdoc.mpd:string/exo_download_paused_for_network = 0x7f120063
com.airdoc.mpd:string/exo_download_paused = 0x7f120062
com.airdoc.mpd:integer/mtrl_card_anim_duration_ms = 0x7f0b0037
com.airdoc.mpd:string/exo_download_notification_channel_name = 0x7f120061
com.airdoc.mpd:string/exo_download_description = 0x7f12005e
com.airdoc.mpd:string/exo_download_completed = 0x7f12005d
com.airdoc.mpd:string/exo_controls_vr_description = 0x7f12005c
com.airdoc.mpd:string/exo_controls_stop_description = 0x7f12005a
com.airdoc.mpd:string/exo_controls_shuffle_off_description = 0x7f120058
com.airdoc.mpd:string/exo_controls_repeat_one_description = 0x7f120053
com.airdoc.mpd:attr/bottomSheetStyle = 0x7f040085
com.airdoc.mpd:string/exo_controls_repeat_all_description = 0x7f120051
com.airdoc.mpd:style/Widget.Material3.Tooltip = 0x7f130406
com.airdoc.mpd:string/exo_controls_playback_speed = 0x7f12004f
com.airdoc.mpd:string/exo_controls_pause_description = 0x7f12004d
com.airdoc.mpd:string/exo_controls_overflow_show_description = 0x7f12004c
com.airdoc.mpd:string/exo_controls_fullscreen_enter_description = 0x7f120047
com.airdoc.mpd:drawable/exo_icon_shuffle_on = 0x7f0800dc
com.airdoc.mpd:style/Base.Widget.AppCompat.PopupWindow = 0x7f1300ed
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Text = 0x7f13014b
com.airdoc.mpd:string/exo_controls_fastforward_description = 0x7f120046
com.airdoc.mpd:string/exo_controls_cc_disabled_description = 0x7f120043
com.airdoc.mpd:string/error_a11y_label = 0x7f120041
com.airdoc.mpd:string/common_signin_button_text_long = 0x7f120040
com.airdoc.mpd:id/material_timepicker_mode_button = 0x7f0a018d
com.airdoc.mpd:string/common_signin_button_text = 0x7f12003f
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f13002d
com.airdoc.mpd:id/accessibility_custom_action_20 = 0x7f0a001d
com.airdoc.mpd:string/common_google_play_services_updating_text = 0x7f12003c
com.airdoc.mpd:string/common_google_play_services_update_title = 0x7f12003b
com.airdoc.mpd:dimen/m3_appbar_size_compact = 0x7f0700cc
com.airdoc.mpd:string/common_google_play_services_update_text = 0x7f12003a
com.airdoc.mpd:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f13009f
com.airdoc.mpd:string/common_google_play_services_update_button = 0x7f120039
com.airdoc.mpd:string/common_google_play_services_unknown_issue = 0x7f120037
com.airdoc.mpd:string/common_google_play_services_notification_channel_name = 0x7f120035
com.airdoc.mpd:string/common_google_play_services_install_button = 0x7f120032
com.airdoc.mpd:string/common_google_play_services_enable_text = 0x7f120030
com.airdoc.mpd:string/common_google_play_services_enable_button = 0x7f12002f
com.airdoc.mpd:string/clear_text_end_icon_content_description = 0x7f12002e
com.airdoc.mpd:string/character_counter_pattern = 0x7f12002d
com.airdoc.mpd:attr/textOutlineColor = 0x7f0404a9
com.airdoc.mpd:string/character_counter_overflowed_content_description = 0x7f12002c
com.airdoc.mpd:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0e0128
com.airdoc.mpd:string/call_notification_screening_text = 0x7f12002a
com.airdoc.mpd:dimen/design_navigation_padding_bottom = 0x7f07007c
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker = 0x7f1303d3
com.airdoc.mpd:string/call_notification_incoming_text = 0x7f120028
com.airdoc.mpd:string/call_notification_decline_action = 0x7f120026
com.airdoc.mpd:string/bottomsheet_action_collapse = 0x7f12001f
com.airdoc.mpd:string/appbar_scrolling_view_behavior = 0x7f12001d
com.airdoc.mpd:attr/colorPrimaryFixedDim = 0x7f040121
com.airdoc.mpd:string/abc_shareactionprovider_share_with_application = 0x7f120019
com.airdoc.mpd:dimen/mtrl_calendar_year_width = 0x7f0702b6
com.airdoc.mpd:string/abc_shareactionprovider_share_with = 0x7f120018
com.airdoc.mpd:string/abc_searchview_description_submit = 0x7f120016
com.airdoc.mpd:style/Base.Widget.AppCompat.Button.Small = 0x7f1300d3
com.airdoc.mpd:string/abc_searchview_description_query = 0x7f120014
com.airdoc.mpd:string/abc_menu_space_shortcut_label = 0x7f12000f
com.airdoc.mpd:string/abc_menu_meta_shortcut_label = 0x7f12000d
com.airdoc.mpd:style/TextAppearance.Material3.TitleMedium = 0x7f13021e
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f13016b
com.airdoc.mpd:string/abc_menu_delete_shortcut_label = 0x7f12000a
com.airdoc.mpd:id/scrollView = 0x7f0a0213
com.airdoc.mpd:string/abc_menu_alt_shortcut_label = 0x7f120008
com.airdoc.mpd:raw/welcome_use_stress_tolerance_assessment = 0x7f110015
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f13032e
com.airdoc.mpd:style/Theme.Design.Light.BottomSheetDialog = 0x7f13024e
com.airdoc.mpd:raw/speech_9 = 0x7f110012
com.airdoc.mpd:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1301c5
com.airdoc.mpd:raw/speech_4 = 0x7f11000d
com.airdoc.mpd:macro/m3_comp_switch_selected_handle_color = 0x7f0e0125
com.airdoc.mpd:string/str_cancel = 0x7f1200f4
com.airdoc.mpd:styleable/BottomNavigationView = 0x7f140017
com.airdoc.mpd:raw/speech_1 = 0x7f11000a
com.airdoc.mpd:raw/profile_sample = 0x7f110007
com.airdoc.mpd:id/action_bar = 0x7f0a0034
com.airdoc.mpd:raw/please_use = 0x7f110005
com.airdoc.mpd:color/m3_sys_color_light_secondary = 0x7f0601f2
com.airdoc.mpd:style/ExoMediaButton.Next = 0x7f130125
com.airdoc.mpd:attr/counterTextAppearance = 0x7f040167
com.airdoc.mpd:raw/phone_last_number = 0x7f110003
com.airdoc.mpd:attr/statusBarForeground = 0x7f040435
com.airdoc.mpd:dimen/m3_comp_divider_thickness = 0x7f07012b
com.airdoc.mpd:raw/detection_code_last_number = 0x7f110001
com.airdoc.mpd:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1300bc
com.airdoc.mpd:id/accessibility_custom_action_13 = 0x7f0a0015
com.airdoc.mpd:plurals/mtrl_badge_content_description = 0x7f100002
com.airdoc.mpd:macro/m3_comp_filled_card_container_color = 0x7f0e0047
com.airdoc.mpd:mipmap/ic_launcher = 0x7f0f0000
com.airdoc.mpd:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f130051
com.airdoc.mpd:macro/m3_sys_color_light_surface_tint = 0x7f0e0176
com.airdoc.mpd:macro/m3_sys_color_dark_surface_tint = 0x7f0e0175
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral92 = 0x7f0600d7
com.airdoc.mpd:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f130237
com.airdoc.mpd:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0e0174
com.airdoc.mpd:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0e0173
com.airdoc.mpd:macro/m3_comp_top_app_bar_small_container_color = 0x7f0e0170
com.airdoc.mpd:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f13029f
com.airdoc.mpd:string/exo_controls_next_description = 0x7f12004a
com.airdoc.mpd:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0e016e
com.airdoc.mpd:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0e00ba
com.airdoc.mpd:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0e016d
com.airdoc.mpd:dimen/m3_btn_text_btn_icon_padding_right = 0x7f070102
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0e016a
com.airdoc.mpd:style/Theme.Material3.DynamicColors.Light = 0x7f130263
com.airdoc.mpd:attr/motionDurationExtraLong3 = 0x7f040346
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0e0168
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0e0165
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0e0163
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0e0162
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0e0160
com.airdoc.mpd:attr/isLightTheme = 0x7f040250
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0e015f
com.airdoc.mpd:id/jumpToStart = 0x7f0a015c
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f13020a
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0e015c
com.airdoc.mpd:raw/speech_0 = 0x7f110009
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0e015b
com.airdoc.mpd:attr/layout_goneMarginEnd = 0x7f0402b9
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0e0157
com.airdoc.mpd:style/Platform.MaterialComponents.Light.Dialog = 0x7f13015c
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0e0154
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0e0153
com.airdoc.mpd:attr/springBoundary = 0x7f04041c
com.airdoc.mpd:macro/m3_comp_time_picker_headline_type = 0x7f0e0152
com.airdoc.mpd:macro/m3_comp_time_picker_headline_color = 0x7f0e0151
com.airdoc.mpd:macro/m3_comp_time_picker_container_color = 0x7f0e014f
com.airdoc.mpd:string/abc_capital_on = 0x7f120007
com.airdoc.mpd:id/gone = 0x7f0a0121
com.airdoc.mpd:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0e014e
com.airdoc.mpd:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0e0149
com.airdoc.mpd:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0e0148
com.airdoc.mpd:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0e0147
com.airdoc.mpd:macro/m3_comp_text_button_label_text_color = 0x7f0e0145
com.airdoc.mpd:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0e0144
com.airdoc.mpd:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1303dd
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f130179
com.airdoc.mpd:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0e013d
com.airdoc.mpd:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f060220
com.airdoc.mpd:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0e013c
com.airdoc.mpd:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f070157
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f13045a
com.airdoc.mpd:style/TextAppearance.Material3.BodyLarge = 0x7f13020d
com.airdoc.mpd:attr/sizePercent = 0x7f040412
com.airdoc.mpd:macro/m3_comp_switch_unselected_icon_color = 0x7f0e013b
com.airdoc.mpd:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0c0005
com.airdoc.mpd:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0e013a
com.airdoc.mpd:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0e0137
com.airdoc.mpd:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0e0136
com.airdoc.mpd:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0e0127
com.airdoc.mpd:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f130110
com.airdoc.mpd:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0e0132
com.airdoc.mpd:string/app_name = 0x7f12001c
com.airdoc.mpd:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0e0130
com.airdoc.mpd:attr/listDividerAlertDialog = 0x7f0402d0
com.airdoc.mpd:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0e012d
com.airdoc.mpd:macro/m3_comp_time_picker_container_shape = 0x7f0e0150
com.airdoc.mpd:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0e012c
com.airdoc.mpd:macro/m3_comp_switch_selected_hover_track_color = 0x7f0e0129
com.airdoc.mpd:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0e0123
com.airdoc.mpd:string/exo_download_failed = 0x7f120060
com.airdoc.mpd:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0e0121
com.airdoc.mpd:attr/spinnerDropDownItemStyle = 0x7f040419
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f1301ab
com.airdoc.mpd:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0e0120
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1303db
com.airdoc.mpd:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0e011a
com.airdoc.mpd:macro/m3_comp_snackbar_container_color = 0x7f0e0114
com.airdoc.mpd:styleable/ImageFilterView = 0x7f140040
com.airdoc.mpd:macro/m3_comp_slider_label_label_text_color = 0x7f0e0113
com.airdoc.mpd:id/accessibility_custom_action_14 = 0x7f0a0016
com.airdoc.mpd:macro/m3_comp_slider_label_container_color = 0x7f0e0112
com.airdoc.mpd:style/Widget.AppCompat.PopupWindow = 0x7f130345
com.airdoc.mpd:macro/m3_comp_slider_active_track_color = 0x7f0e010c
com.airdoc.mpd:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0e010b
com.airdoc.mpd:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0e0108
com.airdoc.mpd:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0e0107
com.airdoc.mpd:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1301c6
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationView = 0x7f13045b
com.airdoc.mpd:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0e0106
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0e0105
com.airdoc.mpd:attr/itemShapeInsetBottom = 0x7f040265
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0e0104
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0e0103
com.airdoc.mpd:string/mtrl_picker_end_date_description = 0x7f1200bf
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0e0102
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1303c4
com.airdoc.mpd:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0702c9
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0e0101
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0e00ff
com.airdoc.mpd:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0e00fb
com.airdoc.mpd:dimen/mtrl_chip_text_size = 0x7f0702be
com.airdoc.mpd:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0e00fa
com.airdoc.mpd:dimen/exo_error_message_height = 0x7f070090
com.airdoc.mpd:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0e00f9
com.airdoc.mpd:macro/m3_comp_search_view_container_surface_tint_layer_color = 0x7f0e00f3
com.airdoc.mpd:macro/m3_comp_search_view_container_color = 0x7f0e00f2
com.airdoc.mpd:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0e00f1
com.airdoc.mpd:macro/m3_comp_search_bar_supporting_text_color = 0x7f0e00ef
com.airdoc.mpd:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0e00ee
com.airdoc.mpd:id/SHOW_PATH = 0x7f0a0009
com.airdoc.mpd:macro/m3_comp_search_bar_input_text_type = 0x7f0e00eb
com.airdoc.mpd:attr/iconPadding = 0x7f040237
com.airdoc.mpd:style/Widget.Design.TextInputLayout = 0x7f130363
com.airdoc.mpd:macro/m3_comp_search_bar_input_text_color = 0x7f0e00ea
com.airdoc.mpd:id/month_navigation_fragment_toggle = 0x7f0a019a
com.airdoc.mpd:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0e00e8
com.airdoc.mpd:macro/m3_comp_search_bar_container_surface_tint_layer_color = 0x7f0e00e7
com.airdoc.mpd:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0e00f8
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0e00e0
com.airdoc.mpd:id/snackbar_action = 0x7f0a022d
com.airdoc.mpd:drawable/abc_star_black_48dp = 0x7f080068
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Caption = 0x7f13001b
com.airdoc.mpd:attr/state_dragged = 0x7f04042e
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0e00df
com.airdoc.mpd:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0e00de
com.airdoc.mpd:style/Widget.AppCompat.RatingBar = 0x7f130348
com.airdoc.mpd:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0e00d9
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0e00d4
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0e00d3
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon = 0x7f080182
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0e00d1
com.airdoc.mpd:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f13015f
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0e00d0
com.airdoc.mpd:drawable/exo_icon_rewind = 0x7f0800da
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0e00cf
com.airdoc.mpd:id/rb_confidentiality = 0x7f0a01f6
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0e00cd
com.airdoc.mpd:style/ShapeAppearance.Material3.Tooltip = 0x7f13019a
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0e00cc
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0e00ca
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f1301af
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0e0070
com.airdoc.mpd:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0e00c8
com.airdoc.mpd:attr/show_rewind_button = 0x7f040402
com.airdoc.mpd:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1301c4
com.airdoc.mpd:attr/splitTrack = 0x7f04041b
com.airdoc.mpd:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f07026f
com.airdoc.mpd:macro/m3_comp_outlined_text_field_outline_color = 0x7f0e00c5
com.airdoc.mpd:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0e00c4
com.airdoc.mpd:id/tv_code_loading = 0x7f0a0286
com.airdoc.mpd:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0e00c3
com.airdoc.mpd:dimen/material_clock_display_height = 0x7f07023e
com.airdoc.mpd:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0e00c2
com.airdoc.mpd:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0e00c0
com.airdoc.mpd:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0e00be
com.airdoc.mpd:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0e00bd
com.airdoc.mpd:macro/m3_comp_outlined_text_field_caret_color = 0x7f0e00b2
com.airdoc.mpd:style/TextAppearance.Material3.TitleSmall = 0x7f13021f
com.airdoc.mpd:drawable/exo_styled_controls_subtitle_on = 0x7f08010a
com.airdoc.mpd:id/tag_screen_reader_focusable = 0x7f0a025a
com.airdoc.mpd:string/abc_menu_shift_shortcut_label = 0x7f12000e
com.airdoc.mpd:macro/m3_comp_outlined_card_outline_color = 0x7f0e00b0
com.airdoc.mpd:string/material_timepicker_pm = 0x7f1200a2
com.airdoc.mpd:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0e00ad
com.airdoc.mpd:macro/m3_comp_outlined_card_container_shape = 0x7f0e00ab
com.airdoc.mpd:macro/m3_comp_outlined_card_container_color = 0x7f0e00aa
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f130148
com.airdoc.mpd:attr/horizontalOffsetWithText = 0x7f040232
com.airdoc.mpd:color/common_google_signin_btn_tint = 0x7f06004e
com.airdoc.mpd:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0e00a9
com.airdoc.mpd:macro/m3_comp_outlined_button_outline_color = 0x7f0e00a8
com.airdoc.mpd:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0e00a7
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f13046c
com.airdoc.mpd:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0e00a6
com.airdoc.mpd:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0e00a4
com.airdoc.mpd:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0e00a3
com.airdoc.mpd:macro/m3_comp_navigation_rail_label_text_type = 0x7f0e00a1
com.airdoc.mpd:id/asConfigured = 0x7f0a0054
com.airdoc.mpd:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0e00a0
com.airdoc.mpd:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0e009f
com.airdoc.mpd:macro/m3_comp_navigation_rail_container_color = 0x7f0e009b
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f1301a3
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0e009a
com.airdoc.mpd:drawable/common_google_signin_btn_text_light_normal = 0x7f0800a2
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1300c9
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0e0091
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0e008f
com.airdoc.mpd:attr/liftOnScrollTargetViewId = 0x7f0402c8
com.airdoc.mpd:style/Theme.Design.NoActionBar = 0x7f130250
com.airdoc.mpd:attr/itemActiveIndicatorStyle = 0x7f040254
com.airdoc.mpd:id/textinput_counter = 0x7f0a026a
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0e008e
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0601b0
com.airdoc.mpd:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1300a9
com.airdoc.mpd:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1302ba
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0e008c
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f070225
com.airdoc.mpd:macro/m3_comp_navigation_drawer_container_color = 0x7f0e0086
com.airdoc.mpd:attr/backgroundInsetTop = 0x7f040052
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0e0085
com.airdoc.mpd:id/select_dialog_listview = 0x7f0a021f
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0e0081
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0e007f
com.airdoc.mpd:string/mtrl_switch_track_path = 0x7f1200e0
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral12 = 0x7f0600c8
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0e007d
com.airdoc.mpd:drawable/exo_legacy_controls_fastforward = 0x7f0800df
com.airdoc.mpd:macro/m3_comp_navigation_bar_label_text_type = 0x7f0e0079
com.airdoc.mpd:styleable/Chip = 0x7f14001e
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0e0078
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0e0075
com.airdoc.mpd:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1300d5
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0e0074
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0e0072
com.airdoc.mpd:string/exo_download_downloading = 0x7f12005f
com.airdoc.mpd:attr/materialCalendarHeaderTitle = 0x7f04030a
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0e006e
com.airdoc.mpd:attr/paddingRightSystemWindowInsets = 0x7f04038b
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f13022e
com.airdoc.mpd:macro/m3_comp_navigation_bar_container_color = 0x7f0e006d
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0e006a
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0e0068
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0e0067
com.airdoc.mpd:color/m3_ref_palette_secondary60 = 0x7f060157
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0e0063
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0e0061
com.airdoc.mpd:macro/m3_comp_linear_progress_indicator_track_color = 0x7f0e005f
com.airdoc.mpd:macro/m3_comp_linear_progress_indicator_active_indicator_color = 0x7f0e005e
com.airdoc.mpd:style/Base.Widget.AppCompat.SearchView = 0x7f1300f3
com.airdoc.mpd:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1300d9
com.airdoc.mpd:style/Base.AlertDialog.AppCompat = 0x7f13000c
com.airdoc.mpd:macro/m3_comp_input_chip_label_text_type = 0x7f0e005d
com.airdoc.mpd:style/Base.V26.Theme.AppCompat.Light = 0x7f1300b7
com.airdoc.mpd:macro/m3_comp_input_chip_container_shape = 0x7f0e005c
com.airdoc.mpd:macro/m3_comp_filter_chip_label_text_type = 0x7f0e0059
com.airdoc.mpd:dimen/m3_comp_filled_card_container_elevation = 0x7f070148
com.airdoc.mpd:id/aligned = 0x7f0a004b
com.airdoc.mpd:macro/m3_comp_filter_chip_container_shape = 0x7f0e0058
com.airdoc.mpd:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0e0055
com.airdoc.mpd:layout/ime_secondary_split_test_activity = 0x7f0d004d
com.airdoc.mpd:macro/m3_comp_filled_tonal_button_container_color = 0x7f0e0053
com.airdoc.mpd:dimen/exo_styled_progress_dragged_thumb_size = 0x7f0700ae
com.airdoc.mpd:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0e0052
com.airdoc.mpd:macro/m3_comp_filled_text_field_input_text_type = 0x7f0e0051
com.airdoc.mpd:macro/m3_comp_filled_text_field_container_shape = 0x7f0e004d
com.airdoc.mpd:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0e001c
com.airdoc.mpd:macro/m3_comp_filled_text_field_container_color = 0x7f0e004c
com.airdoc.mpd:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0e004b
com.airdoc.mpd:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0e0043
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f130169
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Headline = 0x7f130020
com.airdoc.mpd:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0e0172
com.airdoc.mpd:macro/m3_comp_fab_surface_icon_color = 0x7f0e003f
com.airdoc.mpd:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.airdoc.mpd:macro/m3_comp_fab_surface_container_color = 0x7f0e003e
com.airdoc.mpd:macro/m3_comp_fab_primary_large_container_shape = 0x7f0e003a
com.airdoc.mpd:attr/materialAlertDialogBodyTextStyle = 0x7f0402f9
com.airdoc.mpd:macro/m3_comp_fab_primary_icon_color = 0x7f0e0039
com.airdoc.mpd:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0e0036
com.airdoc.mpd:id/tag_unhandled_key_listeners = 0x7f0a025e
com.airdoc.mpd:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1303e4
com.airdoc.mpd:id/media_actions = 0x7f0a0192
com.airdoc.mpd:style/Base.V28.Theme.AppCompat = 0x7f1300b9
com.airdoc.mpd:string/mtrl_checkbox_state_description_checked = 0x7f1200ae
com.airdoc.mpd:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0e0034
com.airdoc.mpd:attr/cardMaxElevation = 0x7f0400a8
com.airdoc.mpd:style/TextAppearance.AppCompat.Display2 = 0x7f1301bd
com.airdoc.mpd:macro/m3_comp_elevated_card_container_shape = 0x7f0e002c
com.airdoc.mpd:macro/m3_comp_elevated_card_container_color = 0x7f0e002b
com.airdoc.mpd:macro/m3_comp_elevated_button_container_color = 0x7f0e002a
com.airdoc.mpd:macro/m3_comp_divider_color = 0x7f0e0029
com.airdoc.mpd:macro/m3_comp_dialog_supporting_text_type = 0x7f0e0028
com.airdoc.mpd:style/Base.Widget.AppCompat.ProgressBar = 0x7f1300ee
com.airdoc.mpd:dimen/mtrl_calendar_year_horizontal_padding = 0x7f0702b4
com.airdoc.mpd:color/m3_ref_palette_neutral95 = 0x7f060132
com.airdoc.mpd:string/str_unknown = 0x7f12012a
com.airdoc.mpd:macro/m3_comp_dialog_supporting_text_color = 0x7f0e0027
com.airdoc.mpd:macro/m3_comp_dialog_headline_color = 0x7f0e0025
com.airdoc.mpd:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0e0020
com.airdoc.mpd:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0e001f
com.airdoc.mpd:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0e001d
com.airdoc.mpd:attr/contentDescription = 0x7f040144
com.airdoc.mpd:styleable/MaterialCalendar = 0x7f140057
com.airdoc.mpd:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f130268
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Body.Text = 0x7f130145
com.airdoc.mpd:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0e001b
com.airdoc.mpd:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0e001a
com.airdoc.mpd:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0e0019
com.airdoc.mpd:string/str_collector_number = 0x7f1200f7
com.airdoc.mpd:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0e0017
com.airdoc.mpd:style/Widget.Design.BottomSheet.Modal = 0x7f13035b
com.airdoc.mpd:id/bounceStart = 0x7f0a0066
com.airdoc.mpd:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0e0016
com.airdoc.mpd:attr/dividerInsetStart = 0x7f04018d
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0e0015
com.airdoc.mpd:string/androidx_startup = 0x7f12001b
com.airdoc.mpd:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0700d4
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0e0013
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0e0011
com.airdoc.mpd:macro/m3_comp_circular_progress_indicator_active_indicator_color = 0x7f0e000d
com.airdoc.mpd:macro/m3_comp_checkbox_selected_icon_color = 0x7f0e000b
com.airdoc.mpd:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0e000a
com.airdoc.mpd:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0c0000
com.airdoc.mpd:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0e0009
com.airdoc.mpd:drawable/exo_styled_controls_vr = 0x7f08010b
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1302b6
com.airdoc.mpd:styleable/MaterialButton = 0x7f140055
com.airdoc.mpd:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0e0008
com.airdoc.mpd:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0e0007
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0e00fc
com.airdoc.mpd:macro/m3_comp_checkbox_selected_container_color = 0x7f0e0006
com.airdoc.mpd:color/m3_ref_palette_primary80 = 0x7f06014c
com.airdoc.mpd:macro/m3_comp_assist_chip_label_text_type = 0x7f0e0001
com.airdoc.mpd:layout/support_simple_spinner_dropdown_item = 0x7f0d0096
com.airdoc.mpd:attr/actionModeSelectAllDrawable = 0x7f04001b
com.airdoc.mpd:layout/select_dialog_item_material = 0x7f0d0093
com.airdoc.mpd:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0e009d
com.airdoc.mpd:layout/notification_template_part_time = 0x7f0d0092
com.airdoc.mpd:layout/notification_template_media = 0x7f0d008f
com.airdoc.mpd:layout/notification_template_big_media_narrow = 0x7f0d008a
com.airdoc.mpd:drawable/notification_template_icon_bg = 0x7f0801ae
com.airdoc.mpd:layout/notification_template_big_media_custom = 0x7f0d0089
com.airdoc.mpd:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f130408
com.airdoc.mpd:layout/notification_template_big_media = 0x7f0d0088
com.airdoc.mpd:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f130105
com.airdoc.mpd:layout/notification_media_action = 0x7f0d0086
com.airdoc.mpd:layout/m3_auto_complete_simple_item = 0x7f0d0054
com.airdoc.mpd:layout/notification_action_tombstone = 0x7f0d0085
com.airdoc.mpd:id/sharedValueUnset = 0x7f0a0224
com.airdoc.mpd:layout/notification_action = 0x7f0d0084
com.airdoc.mpd:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f130112
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f130208
com.airdoc.mpd:string/mtrl_picker_date_header_title = 0x7f1200bc
com.airdoc.mpd:layout/mtrl_picker_header_dialog = 0x7f0d007b
com.airdoc.mpd:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080041
com.airdoc.mpd:style/Widget.AppCompat.SeekBar = 0x7f13034d
com.airdoc.mpd:drawable/icon_close_airdoc_ai = 0x7f080152
com.airdoc.mpd:layout/mtrl_picker_fullscreen = 0x7f0d007a
com.airdoc.mpd:layout/mtrl_picker_dialog = 0x7f0d0079
com.airdoc.mpd:layout/mtrl_navigation_rail_item = 0x7f0d0077
com.airdoc.mpd:layout/mtrl_calendar_months = 0x7f0d0072
com.airdoc.mpd:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1200d7
com.airdoc.mpd:layout/mtrl_calendar_horizontal = 0x7f0d006e
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0e0166
com.airdoc.mpd:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.airdoc.mpd:layout/mtrl_calendar_day = 0x7f0d006b
com.airdoc.mpd:layout/mtrl_alert_dialog_title = 0x7f0d0066
com.airdoc.mpd:attr/itemPadding = 0x7f04025e
com.airdoc.mpd:id/beginning = 0x7f0a005e
com.airdoc.mpd:layout/material_timepicker_textinput_display = 0x7f0d0063
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f130077
com.airdoc.mpd:dimen/mtrl_slider_tick_radius = 0x7f070307
com.airdoc.mpd:attr/color = 0x7f0400fc
com.airdoc.mpd:attr/fabCradleMargin = 0x7f0401db
com.airdoc.mpd:layout/material_timepicker_dialog = 0x7f0d0062
com.airdoc.mpd:layout/material_textinput_timepicker = 0x7f0d005e
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0e0082
com.airdoc.mpd:layout/material_clockface_view = 0x7f0d005c
com.airdoc.mpd:id/left = 0x7f0a015f
com.airdoc.mpd:layout/material_clock_display_divider = 0x7f0d0058
com.airdoc.mpd:layout/material_chip_input_combo = 0x7f0d0056
com.airdoc.mpd:layout/m3_side_sheet_dialog = 0x7f0d0055
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f130459
com.airdoc.mpd:layout/m3_alert_dialog_actions = 0x7f0d0052
com.airdoc.mpd:id/fill_vertical = 0x7f0a010b
com.airdoc.mpd:layout/item_language_settings = 0x7f0d004f
com.airdoc.mpd:color/mtrl_navigation_bar_colored_item_tint = 0x7f0602db
com.airdoc.mpd:dimen/material_textinput_max_width = 0x7f07025d
com.airdoc.mpd:layout/item_detection = 0x7f0d004e
com.airdoc.mpd:layout/fragment_device_exception = 0x7f0d0049
com.airdoc.mpd:id/centerInside = 0x7f0a0072
com.airdoc.mpd:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0e0050
com.airdoc.mpd:layout/exo_styled_sub_settings_list_item = 0x7f0d0046
com.airdoc.mpd:layout/exo_styled_settings_list = 0x7f0d0044
com.airdoc.mpd:id/status_bar_latest_event_content = 0x7f0a0248
com.airdoc.mpd:style/Base.Widget.AppCompat.Button = 0x7f1300ce
com.airdoc.mpd:layout/exo_track_selection_dialog = 0x7f0d0047
com.airdoc.mpd:layout/exo_player_view = 0x7f0d0043
com.airdoc.mpd:dimen/exo_small_icon_padding_horizontal = 0x7f0700a5
com.airdoc.mpd:layout/exo_player_control_rewind_button = 0x7f0d0041
com.airdoc.mpd:layout/exo_legacy_player_control_view = 0x7f0d003e
com.airdoc.mpd:id/pathRelative = 0x7f0a01e7
com.airdoc.mpd:layout/dialog_update = 0x7f0d003d
com.airdoc.mpd:dimen/notification_media_narrow_margin = 0x7f07032e
com.airdoc.mpd:layout/dialog_selection_age = 0x7f0d003b
com.airdoc.mpd:color/ripple_material_dark = 0x7f060305
com.airdoc.mpd:layout/dialog_common_loading = 0x7f0d0037
com.airdoc.mpd:layout/design_text_input_end_icon = 0x7f0d0035
com.airdoc.mpd:layout/design_navigation_menu_item = 0x7f0d0034
com.airdoc.mpd:layout/design_navigation_item_header = 0x7f0d0030
com.airdoc.mpd:attr/motionEffect_translationY = 0x7f040366
com.airdoc.mpd:layout/design_menu_item_action_area = 0x7f0d002e
com.airdoc.mpd:layout/design_layout_tab_icon = 0x7f0d002c
com.airdoc.mpd:layout/design_bottom_navigation_item = 0x7f0d0028
com.airdoc.mpd:layout/activity_update = 0x7f0d0026
com.airdoc.mpd:layout/material_time_input = 0x7f0d0060
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f06019c
com.airdoc.mpd:color/m3_sys_color_light_tertiary_container = 0x7f0601fe
com.airdoc.mpd:layout/activity_title = 0x7f0d0025
com.airdoc.mpd:layout/activity_scan = 0x7f0d0024
com.airdoc.mpd:layout/activity_detection1 = 0x7f0d001f
com.airdoc.mpd:attr/collapsedSize = 0x7f0400f3
com.airdoc.mpd:layout/activity_config = 0x7f0d001d
com.airdoc.mpd:string/material_motion_easing_accelerated = 0x7f120096
com.airdoc.mpd:drawable/icon_tsc_bg = 0x7f080162
com.airdoc.mpd:layout/abc_list_menu_item_layout = 0x7f0d0010
com.airdoc.mpd:layout/abc_list_menu_item_icon = 0x7f0d000f
com.airdoc.mpd:styleable/StateListDrawable = 0x7f14008c
com.airdoc.mpd:layout/abc_expanded_menu_layout = 0x7f0d000d
com.airdoc.mpd:layout/abc_dialog_title_material = 0x7f0d000c
com.airdoc.mpd:layout/abc_alert_dialog_button_bar_material = 0x7f0d0008
com.airdoc.mpd:macro/m3_comp_search_view_header_input_text_type = 0x7f0e00f7
com.airdoc.mpd:attr/cornerSizeTopLeft = 0x7f040161
com.airdoc.mpd:layout/abc_activity_chooser_view = 0x7f0d0006
com.airdoc.mpd:style/ExoStyledControls.TimeText = 0x7f13013f
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f070183
com.airdoc.mpd:layout/abc_action_mode_close_item_material = 0x7f0d0005
com.airdoc.mpd:layout/abc_action_mode_bar = 0x7f0d0004
com.airdoc.mpd:style/Base.Widget.Material3.Snackbar = 0x7f13010e
com.airdoc.mpd:styleable/PopupWindowBackgroundState = 0x7f140078
com.airdoc.mpd:layout/abc_action_menu_item_layout = 0x7f0d0002
com.airdoc.mpd:layout/abc_action_bar_up_container = 0x7f0d0001
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1303fe
com.airdoc.mpd:layout/abc_action_bar_title_item = 0x7f0d0000
com.airdoc.mpd:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0e016f
com.airdoc.mpd:style/Widget.Material3.PopupMenu = 0x7f1303e0
com.airdoc.mpd:interpolator/mtrl_linear = 0x7f0c0010
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Light = 0x7f1302f8
com.airdoc.mpd:dimen/m3_carousel_extra_small_item_size = 0x7f070111
com.airdoc.mpd:interpolator/mtrl_fast_out_slow_in = 0x7f0c000f
com.airdoc.mpd:interpolator/m3_sys_motion_easing_standard = 0x7f0c000b
com.airdoc.mpd:interpolator/m3_sys_motion_easing_linear = 0x7f0c000a
com.airdoc.mpd:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0c0008
com.airdoc.mpd:interpolator/m3_sys_motion_easing_emphasized = 0x7f0c0007
com.airdoc.mpd:interpolator/fast_out_slow_in = 0x7f0c0006
com.airdoc.mpd:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0c0004
com.airdoc.mpd:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0c0001
com.airdoc.mpd:color/m3_sys_color_dark_surface = 0x7f06018a
com.airdoc.mpd:attr/actionTextColorAlpha = 0x7f040024
com.airdoc.mpd:integer/status_bar_notification_info_maxnum = 0x7f0b0046
com.airdoc.mpd:color/material_personalized_color_outline_variant = 0x7f060296
com.airdoc.mpd:style/Theme.Material3.DayNight = 0x7f130259
com.airdoc.mpd:integer/show_password_duration = 0x7f0b0045
com.airdoc.mpd:integer/mtrl_view_invisible = 0x7f0b0043
com.airdoc.mpd:integer/mtrl_switch_track_viewport_width = 0x7f0b0040
com.airdoc.mpd:integer/mtrl_switch_track_viewport_height = 0x7f0b003f
com.airdoc.mpd:attr/badgeHeight = 0x7f040059
com.airdoc.mpd:integer/mtrl_switch_thumb_viewport_size = 0x7f0b003e
com.airdoc.mpd:integer/mtrl_switch_thumb_motion_duration = 0x7f0b0039
com.airdoc.mpd:attr/actionModePopupWindowStyle = 0x7f04001a
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f13044c
com.airdoc.mpd:integer/mtrl_card_anim_delay_ms = 0x7f0b0036
com.airdoc.mpd:integer/mtrl_calendar_header_orientation = 0x7f0b0033
com.airdoc.mpd:integer/mtrl_btn_anim_duration_ms = 0x7f0b0032
com.airdoc.mpd:style/Widget.Material3.SearchView.Prefix = 0x7f1303e9
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1300e3
com.airdoc.mpd:attr/artwork_display_mode = 0x7f04003e
com.airdoc.mpd:integer/mtrl_btn_anim_delay_ms = 0x7f0b0031
com.airdoc.mpd:integer/material_motion_path = 0x7f0b002f
com.airdoc.mpd:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1300ef
com.airdoc.mpd:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f130106
com.airdoc.mpd:id/texture_view = 0x7f0a0270
com.airdoc.mpd:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1300db
com.airdoc.mpd:integer/material_motion_duration_short_2 = 0x7f0b002e
com.airdoc.mpd:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f13025d
com.airdoc.mpd:integer/material_motion_duration_short_1 = 0x7f0b002d
com.airdoc.mpd:integer/material_motion_duration_long_2 = 0x7f0b002a
com.airdoc.mpd:attr/closeItemLayout = 0x7f0400f0
com.airdoc.mpd:styleable/AppBarLayout = 0x7f14000a
com.airdoc.mpd:integer/material_motion_duration_long_1 = 0x7f0b0029
com.airdoc.mpd:string/m3_sys_motion_easing_legacy = 0x7f120088
com.airdoc.mpd:integer/m3_sys_shape_corner_medium_corner_family = 0x7f0b0027
com.airdoc.mpd:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f0b0024
com.airdoc.mpd:dimen/m3_comp_fab_primary_icon_size = 0x7f07013e
com.airdoc.mpd:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f0b0023
com.airdoc.mpd:integer/m3_sys_motion_path = 0x7f0b0022
com.airdoc.mpd:integer/m3_sys_motion_duration_short4 = 0x7f0b0021
com.airdoc.mpd:id/reverse = 0x7f0a01fe
com.airdoc.mpd:macro/m3_comp_filled_card_container_shape = 0x7f0e0048
com.airdoc.mpd:drawable/ic_mtrl_checked_circle = 0x7f080138
com.airdoc.mpd:integer/m3_sys_motion_duration_short3 = 0x7f0b0020
com.airdoc.mpd:integer/m3_sys_motion_duration_medium3 = 0x7f0b001c
com.airdoc.mpd:integer/m3_sys_motion_duration_medium2 = 0x7f0b001b
com.airdoc.mpd:integer/m3_sys_motion_duration_medium1 = 0x7f0b001a
com.airdoc.mpd:color/m3_ref_palette_neutral50 = 0x7f060129
com.airdoc.mpd:integer/m3_sys_motion_duration_extra_long4 = 0x7f0b0015
com.airdoc.mpd:dimen/m3_carousel_gone_size = 0x7f070112
com.airdoc.mpd:raw/speech_6 = 0x7f11000f
com.airdoc.mpd:string/common_google_play_services_wear_update_text = 0x7f12003d
com.airdoc.mpd:color/m3_ref_palette_neutral_variant80 = 0x7f06013f
com.airdoc.mpd:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f070128
com.airdoc.mpd:integer/m3_sys_motion_duration_extra_long1 = 0x7f0b0012
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1300df
com.airdoc.mpd:integer/m3_chip_anim_duration = 0x7f0b0011
com.airdoc.mpd:styleable/OnClick = 0x7f140073
com.airdoc.mpd:integer/m3_btn_anim_duration_ms = 0x7f0b000e
com.airdoc.mpd:integer/m3_btn_anim_delay_ms = 0x7f0b000d
com.airdoc.mpd:integer/m3_badge_max_number = 0x7f0b000c
com.airdoc.mpd:integer/design_tab_indicator_anim_duration_ms = 0x7f0b0007
com.airdoc.mpd:attr/materialSearchBarStyle = 0x7f04031e
com.airdoc.mpd:dimen/mtrl_btn_inset = 0x7f07027e
com.airdoc.mpd:id/progress_horizontal = 0x7f0a01f3
com.airdoc.mpd:integer/design_snackbar_text_max_lines = 0x7f0b0006
com.airdoc.mpd:integer/config_tooltipAnimTime = 0x7f0b0005
com.airdoc.mpd:attr/cornerFamilyBottomLeft = 0x7f040159
com.airdoc.mpd:integer/app_bar_elevation_anim_duration = 0x7f0b0002
com.airdoc.mpd:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0c0002
com.airdoc.mpd:integer/abc_config_activityDefaultDur = 0x7f0b0000
com.airdoc.mpd:id/iv_logo = 0x7f0a014c
com.airdoc.mpd:id/zoom = 0x7f0a02c9
com.airdoc.mpd:id/wrap_content_constrained = 0x7f0a02c6
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f13017b
com.airdoc.mpd:string/mtrl_picker_date_header_selected = 0x7f1200bb
com.airdoc.mpd:id/wrap = 0x7f0a02c4
com.airdoc.mpd:attr/textAppearanceSubtitle1 = 0x7f040491
com.airdoc.mpd:id/bestChoice = 0x7f0a005f
com.airdoc.mpd:id/with_icon = 0x7f0a02c2
com.airdoc.mpd:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1300ea
com.airdoc.mpd:id/wide = 0x7f0a02c0
com.airdoc.mpd:id/when_playing = 0x7f0a02bf
com.airdoc.mpd:layout/layout_menu_popup_window = 0x7f0d0050
com.airdoc.mpd:id/west = 0x7f0a02be
com.airdoc.mpd:id/wb_hrv = 0x7f0a02bd
com.airdoc.mpd:style/Animation.Material3.SideSheetDialog.Right = 0x7f130009
com.airdoc.mpd:id/visible_removing_fragment_view_tag = 0x7f0a02bb
com.airdoc.mpd:layout/design_text_input_start_icon = 0x7f0d0036
com.airdoc.mpd:id/visible = 0x7f0a02ba
com.airdoc.mpd:id/view_tree_view_model_store_owner = 0x7f0a02b9
com.airdoc.mpd:id/view_tree_saved_state_registry_owner = 0x7f0a02b8
com.airdoc.mpd:id/view_transition = 0x7f0a02b5
com.airdoc.mpd:style/Base.Widget.AppCompat.Toolbar = 0x7f1300fb
com.airdoc.mpd:id/view_offset_helper = 0x7f0a02b4
com.airdoc.mpd:drawable/$avd_hide_password__1 = 0x7f080001
com.airdoc.mpd:id/version_red_dot = 0x7f0a02af
com.airdoc.mpd:id/unlabeled = 0x7f0a02ac
com.airdoc.mpd:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.airdoc.mpd:id/tv_view_report = 0x7f0a02a9
com.airdoc.mpd:styleable/Snackbar = 0x7f140088
com.airdoc.mpd:id/tv_version = 0x7f0a02a8
com.airdoc.mpd:macro/m3_comp_dialog_headline_type = 0x7f0e0026
com.airdoc.mpd:id/tv_user_name = 0x7f0a02a5
com.airdoc.mpd:styleable/DefaultTimeBar = 0x7f140031
com.airdoc.mpd:id/tv_update = 0x7f0a02a3
com.airdoc.mpd:id/tv_title = 0x7f0a02a2
com.airdoc.mpd:id/tv_test_version = 0x7f0a02a1
com.airdoc.mpd:style/Base.V7.Widget.AppCompat.EditText = 0x7f1300c1
com.airdoc.mpd:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0e00ae
com.airdoc.mpd:id/iv_user_avatar = 0x7f0a0157
com.airdoc.mpd:id/tv_start_detection = 0x7f0a02a0
com.airdoc.mpd:style/TextAppearance.AppCompat.Large = 0x7f1301c2
com.airdoc.mpd:id/tv_residual_degree = 0x7f0a029b
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f13007d
com.airdoc.mpd:color/design_dark_default_color_on_surface = 0x7f060057
com.airdoc.mpd:string/str_version_number_s = 0x7f120130
com.airdoc.mpd:id/tv_proactively_greet = 0x7f0a0299
com.airdoc.mpd:dimen/m3_btn_translation_z_base = 0x7f070105
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0e007c
com.airdoc.mpd:id/tv_ok = 0x7f0a0297
com.airdoc.mpd:id/tv_more_settings = 0x7f0a0295
com.airdoc.mpd:id/tv_introduction = 0x7f0a0293
com.airdoc.mpd:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1300fe
com.airdoc.mpd:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f130116
com.airdoc.mpd:id/tv_exception = 0x7f0a0290
com.airdoc.mpd:id/tv_device_info = 0x7f0a028e
com.airdoc.mpd:id/tv_detection = 0x7f0a028c
com.airdoc.mpd:id/tv_details = 0x7f0a028b
com.airdoc.mpd:id/tv_data_cache_status = 0x7f0a028a
com.airdoc.mpd:attr/state_indeterminate = 0x7f040430
com.airdoc.mpd:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f13023c
com.airdoc.mpd:attr/waveShape = 0x7f04051a
com.airdoc.mpd:color/white_60 = 0x7f060321
com.airdoc.mpd:id/exo_settings = 0x7f0a00f8
com.airdoc.mpd:id/tv_customer_service_hotline = 0x7f0a0289
com.airdoc.mpd:id/tv_collector_number = 0x7f0a0287
com.airdoc.mpd:id/tv_cancel = 0x7f0a0284
com.airdoc.mpd:id/tv_back = 0x7f0a0283
com.airdoc.mpd:id/tv_app_size = 0x7f0a0282
com.airdoc.mpd:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0e002f
com.airdoc.mpd:id/tv_app_name = 0x7f0a0281
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1301e6
com.airdoc.mpd:id/transition_transform = 0x7f0a027f
com.airdoc.mpd:id/transition_current_scene = 0x7f0a027b
com.airdoc.mpd:id/transitionToStart = 0x7f0a027a
com.airdoc.mpd:id/transitionToEnd = 0x7f0a0279
com.airdoc.mpd:style/Widget.Material3.SearchView.Toolbar = 0x7f1303ea
com.airdoc.mpd:macro/m3_comp_switch_selected_focus_track_color = 0x7f0e0124
com.airdoc.mpd:id/touch_outside = 0x7f0a0278
com.airdoc.mpd:drawable/selector_param_setting_mode_bg = 0x7f0801bb
com.airdoc.mpd:id/toggle = 0x7f0a0275
com.airdoc.mpd:id/constraint = 0x7f0a0092
com.airdoc.mpd:id/title = 0x7f0a0272
com.airdoc.mpd:id/textinput_suffix_text = 0x7f0a026f
com.airdoc.mpd:color/design_dark_default_color_surface = 0x7f06005d
com.airdoc.mpd:id/textinput_placeholder = 0x7f0a026d
com.airdoc.mpd:id/disableHome = 0x7f0a00b1
com.airdoc.mpd:id/text_input_error_icon = 0x7f0a0268
com.airdoc.mpd:id/dragAnticlockwise = 0x7f0a00b6
com.airdoc.mpd:id/textTop = 0x7f0a0266
com.airdoc.mpd:id/textSpacerNoTitle = 0x7f0a0264
com.airdoc.mpd:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f130457
com.airdoc.mpd:id/textSpacerNoButtons = 0x7f0a0263
com.airdoc.mpd:attr/nestedScrollViewStyle = 0x7f040379
com.airdoc.mpd:color/common_google_signin_btn_text_light_pressed = 0x7f06004d
com.airdoc.mpd:color/m3_icon_button_icon_color_selector = 0x7f0600b5
com.airdoc.mpd:id/textEnd = 0x7f0a0262
com.airdoc.mpd:id/tag_window_insets_animation_callback = 0x7f0a025f
com.airdoc.mpd:layout/mtrl_calendar_month_labeled = 0x7f0d0070
com.airdoc.mpd:id/tag_unhandled_key_event_manager = 0x7f0a025d
com.airdoc.mpd:id/tag_state_description = 0x7f0a025b
com.airdoc.mpd:drawable/exo_icon_shuffle_off = 0x7f0800db
com.airdoc.mpd:id/tag_on_receive_content_mime_types = 0x7f0a0259
com.airdoc.mpd:attr/startIconContentDescription = 0x7f040425
com.airdoc.mpd:layout/notification_template_icon_group = 0x7f0d008d
com.airdoc.mpd:attr/extendMotionSpec = 0x7f0401cf
com.airdoc.mpd:id/tag_on_apply_window_listener = 0x7f0a0257
com.airdoc.mpd:color/m3_sys_color_dark_surface_container_lowest = 0x7f060190
com.airdoc.mpd:color/material_dynamic_primary0 = 0x7f060245
com.airdoc.mpd:styleable/Fragment = 0x7f14003c
com.airdoc.mpd:id/tag_accessibility_clickable_spans = 0x7f0a0254
com.airdoc.mpd:attr/auto_show = 0x7f04004b
com.airdoc.mpd:id/tabMode = 0x7f0a0252
com.airdoc.mpd:id/switch_proactively_greet = 0x7f0a0251
com.airdoc.mpd:drawable/abc_ratingbar_small_material = 0x7f08005d
com.airdoc.mpd:id/iv_scan_anim = 0x7f0a0151
com.airdoc.mpd:layout/activity_calibration = 0x7f0d001c
com.airdoc.mpd:id/supportScrollUp = 0x7f0a024d
com.airdoc.mpd:drawable/selector_param_setting_scale_icon = 0x7f0801bc
com.airdoc.mpd:styleable/AppCompatTextHelper = 0x7f140010
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f130166
com.airdoc.mpd:id/submenuarrow = 0x7f0a024b
com.airdoc.mpd:id/stop = 0x7f0a0249
com.airdoc.mpd:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1303a0
com.airdoc.mpd:string/str_model_version = 0x7f120114
com.airdoc.mpd:id/staticLayout = 0x7f0a0246
com.airdoc.mpd:id/start = 0x7f0a0242
com.airdoc.mpd:style/Base.Theme.Material3.Dark = 0x7f13005a
com.airdoc.mpd:attr/showAsAction = 0x7f0403f7
com.airdoc.mpd:id/standard = 0x7f0a0241
com.airdoc.mpd:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0700c8
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Large = 0x7f130022
com.airdoc.mpd:string/str_please_enter_age = 0x7f12011c
com.airdoc.mpd:attr/itemShapeInsetEnd = 0x7f040266
com.airdoc.mpd:id/src_over = 0x7f0a0240
com.airdoc.mpd:id/src_in = 0x7f0a023f
com.airdoc.mpd:id/square = 0x7f0a023d
com.airdoc.mpd:string/str_align_qr_code_recognition = 0x7f1200ef
com.airdoc.mpd:id/sharedValueSet = 0x7f0a0223
com.airdoc.mpd:id/spring = 0x7f0a023c
com.airdoc.mpd:attr/displayOptions = 0x7f040188
com.airdoc.mpd:id/split_action_bar = 0x7f0a0239
com.airdoc.mpd:id/spline = 0x7f0a0238
com.airdoc.mpd:id/spacer = 0x7f0a0235
com.airdoc.mpd:id/space_line1 = 0x7f0a0234
com.airdoc.mpd:dimen/hint_alpha_material_dark = 0x7f0700b9
com.airdoc.mpd:id/south = 0x7f0a0232
com.airdoc.mpd:color/m3_highlighted_text = 0x7f0600b3
com.airdoc.mpd:drawable/icon_airdoc_digital_therapy_center = 0x7f08014c
com.airdoc.mpd:style/TextAppearance.Material3.BodySmall = 0x7f13020f
com.airdoc.mpd:id/snapMargins = 0x7f0a0230
com.airdoc.mpd:id/sin = 0x7f0a0229
com.airdoc.mpd:id/showHome = 0x7f0a0227
com.airdoc.mpd:attr/layout_constraintGuide_begin = 0x7f040295
com.airdoc.mpd:style/Theme.Design.BottomSheetDialog = 0x7f13024c
com.airdoc.mpd:attr/textOutlineThickness = 0x7f0404aa
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0e008a
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1302fa
com.airdoc.mpd:string/mtrl_picker_text_input_date_hint = 0x7f1200ce
com.airdoc.mpd:id/serif = 0x7f0a0222
com.airdoc.mpd:style/Base.Widget.AppCompat.Spinner = 0x7f1300f7
com.airdoc.mpd:id/selected = 0x7f0a0220
com.airdoc.mpd:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.airdoc.mpd:id/search_voice_btn = 0x7f0a021e
com.airdoc.mpd:raw/a11pmnp5zxp = 0x7f110000
com.airdoc.mpd:styleable/MaterialAutoCompleteTextView = 0x7f140054
com.airdoc.mpd:styleable/ForegroundLinearLayout = 0x7f14003b
com.airdoc.mpd:id/search_plate = 0x7f0a021c
com.airdoc.mpd:color/m3_ref_palette_primary95 = 0x7f06014e
com.airdoc.mpd:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1301c3
com.airdoc.mpd:id/search_mag_icon = 0x7f0a021b
com.airdoc.mpd:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0600bd
com.airdoc.mpd:id/search_button = 0x7f0a0217
com.airdoc.mpd:id/search_badge = 0x7f0a0215
com.airdoc.mpd:dimen/tooltip_y_offset_non_touch = 0x7f07033c
com.airdoc.mpd:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1300a8
com.airdoc.mpd:id/scrollable = 0x7f0a0214
com.airdoc.mpd:id/scrollIndicatorUp = 0x7f0a0212
com.airdoc.mpd:color/m3_dark_primary_text_disable_only = 0x7f0600a0
com.airdoc.mpd:id/transition_scene_layoutid_cache = 0x7f0a027e
com.airdoc.mpd:id/startVertical = 0x7f0a0245
com.airdoc.mpd:id/scroll = 0x7f0a0210
com.airdoc.mpd:id/screen = 0x7f0a020f
com.airdoc.mpd:attr/chipIconVisible = 0x7f0400cf
com.airdoc.mpd:id/scale = 0x7f0a020e
com.airdoc.mpd:raw/we_chat = 0x7f110014
com.airdoc.mpd:id/save_non_transition_alpha = 0x7f0a020b
com.airdoc.mpd:style/Widget.AppCompat.SearchView = 0x7f13034b
com.airdoc.mpd:id/sans = 0x7f0a020a
com.airdoc.mpd:styleable/CircularProgressBar = 0x7f140020
com.airdoc.mpd:string/mtrl_badge_numberless_content_description = 0x7f1200a5
com.airdoc.mpd:macro/m3_comp_filled_button_label_text_type = 0x7f0e0046
com.airdoc.mpd:id/rv_detection_project = 0x7f0a0208
com.airdoc.mpd:id/row_index_key = 0x7f0a0207
com.airdoc.mpd:id/rounded = 0x7f0a0206
com.airdoc.mpd:id/right_icon = 0x7f0a0204
com.airdoc.mpd:id/rg_scan_type = 0x7f0a0201
com.airdoc.mpd:id/rg_gender = 0x7f0a0200
com.airdoc.mpd:id/reverseSawtooth = 0x7f0a01ff
com.airdoc.mpd:id/x_left = 0x7f0a02c7
com.airdoc.mpd:id/report_drawn = 0x7f0a01fc
com.airdoc.mpd:style/ThemeOverlay.AppCompat.DayNight = 0x7f1302a0
com.airdoc.mpd:raw/speech_5 = 0x7f11000e
com.airdoc.mpd:id/rb_male = 0x7f0a01f9
com.airdoc.mpd:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1302a1
com.airdoc.mpd:id/rb_h5_qr_code = 0x7f0a01f8
com.airdoc.mpd:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1300ab
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f070216
com.airdoc.mpd:id/ratio = 0x7f0a01f5
com.airdoc.mpd:id/mini = 0x7f0a0196
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0e016b
com.airdoc.mpd:id/radio = 0x7f0a01f4
com.airdoc.mpd:attr/enableEdgeToEdge = 0x7f0401ae
com.airdoc.mpd:id/progress_circular = 0x7f0a01f2
com.airdoc.mpd:id/preview_view = 0x7f0a01f0
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f130421
com.airdoc.mpd:string/m3_sys_motion_easing_standard = 0x7f12008c
com.airdoc.mpd:macro/m3_comp_fab_primary_container_shape = 0x7f0e0038
com.airdoc.mpd:id/pressed = 0x7f0a01ef
com.airdoc.mpd:id/pooling_container_listener_holder_tag = 0x7f0a01ec
com.airdoc.mpd:id/pin = 0x7f0a01eb
com.airdoc.mpd:color/material_personalized_hint_foreground_inverse = 0x7f0602b2
com.airdoc.mpd:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f130104
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f060198
com.airdoc.mpd:id/performance = 0x7f0a01ea
com.airdoc.mpd:attr/startIconDrawable = 0x7f040426
com.airdoc.mpd:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.airdoc.mpd:id/percent = 0x7f0a01e9
com.airdoc.mpd:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f130407
com.airdoc.mpd:layout/design_layout_snackbar = 0x7f0d002a
com.airdoc.mpd:id/peekHeight = 0x7f0a01e8
com.airdoc.mpd:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0e013f
com.airdoc.mpd:id/parentPanel = 0x7f0a01e2
com.airdoc.mpd:string/bottomsheet_action_expand = 0x7f120020
com.airdoc.mpd:id/parent = 0x7f0a01e1
com.airdoc.mpd:id/packed = 0x7f0a01df
com.airdoc.mpd:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f130256
com.airdoc.mpd:id/outward = 0x7f0a01dd
com.airdoc.mpd:drawable/m3_tabs_background = 0x7f08016f
com.airdoc.mpd:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f13045e
com.airdoc.mpd:id/outline = 0x7f0a01dc
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1301b2
com.airdoc.mpd:id/open_search_view_toolbar_container = 0x7f0a01db
com.airdoc.mpd:id/open_search_view_toolbar = 0x7f0a01da
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f13016a
com.airdoc.mpd:layout/mtrl_search_bar = 0x7f0d0082
com.airdoc.mpd:id/open_search_view_search_prefix = 0x7f0a01d8
com.airdoc.mpd:id/open_search_view_scrim = 0x7f0a01d7
com.airdoc.mpd:layout/material_clockface_textview = 0x7f0d005b
com.airdoc.mpd:id/open_search_view_header_container = 0x7f0a01d5
com.airdoc.mpd:id/open_search_view_dummy_toolbar = 0x7f0a01d3
com.airdoc.mpd:id/open_search_view_divider = 0x7f0a01d2
com.airdoc.mpd:id/open_search_view_background = 0x7f0a01cf
com.airdoc.mpd:id/text_input_start_icon = 0x7f0a0269
com.airdoc.mpd:id/open_search_bar_text_view = 0x7f0a01ce
com.airdoc.mpd:id/onInterceptTouchReturnSwipe = 0x7f0a01cc
com.airdoc.mpd:id/notification_main_column_container = 0x7f0a01c8
com.airdoc.mpd:id/notification_main_column = 0x7f0a01c7
com.airdoc.mpd:dimen/fastscroll_minimum_range = 0x7f0700b5
com.airdoc.mpd:id/notification_background = 0x7f0a01c6
com.airdoc.mpd:id/normal = 0x7f0a01c4
com.airdoc.mpd:attr/itemStrokeColor = 0x7f04026a
com.airdoc.mpd:string/common_google_play_services_enable_title = 0x7f120031
com.airdoc.mpd:id/neverCompleteToStart = 0x7f0a01c0
com.airdoc.mpd:id/neverCompleteToEnd = 0x7f0a01bf
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f070137
com.airdoc.mpd:attr/motionDurationMedium1 = 0x7f04034c
com.airdoc.mpd:id/never = 0x7f0a01be
com.airdoc.mpd:id/navigation_bar_item_labels_group = 0x7f0a01ba
com.airdoc.mpd:id/navigation_bar_item_icon_view = 0x7f0a01b9
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary70 = 0x7f0600f2
com.airdoc.mpd:layout/abc_list_menu_item_checkbox = 0x7f0d000e
com.airdoc.mpd:id/navigation_bar_item_icon_container = 0x7f0a01b8
com.airdoc.mpd:id/multiply = 0x7f0a01b6
com.airdoc.mpd:style/TextAppearance.Material3.DisplayLarge = 0x7f130210
com.airdoc.mpd:id/mtrl_picker_text_input_range_end = 0x7f0a01b2
com.airdoc.mpd:id/mtrl_picker_text_input_date = 0x7f0a01b1
com.airdoc.mpd:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f130482
com.airdoc.mpd:id/mtrl_motion_snapshot_view = 0x7f0a01ab
com.airdoc.mpd:id/noState = 0x7f0a01c2
com.airdoc.mpd:id/mtrl_calendar_days_of_week = 0x7f0a01a1
com.airdoc.mpd:attr/chipStandaloneStyle = 0x7f0400d5
com.airdoc.mpd:id/motion_base = 0x7f0a019e
com.airdoc.mpd:layout/mtrl_alert_dialog = 0x7f0d0064
com.airdoc.mpd:string/call_notification_ongoing_text = 0x7f120029
com.airdoc.mpd:id/month_navigation_bar = 0x7f0a0199
com.airdoc.mpd:id/month_grid = 0x7f0a0198
com.airdoc.mpd:id/middle = 0x7f0a0195
com.airdoc.mpd:attr/colorSecondaryContainer = 0x7f040127
com.airdoc.mpd:id/month_navigation_previous = 0x7f0a019c
com.airdoc.mpd:string/mtrl_checkbox_button_icon_path_group_name = 0x7f1200a7
com.airdoc.mpd:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0e00bc
com.airdoc.mpd:id/matrix = 0x7f0a0191
com.airdoc.mpd:id/material_value_index = 0x7f0a0190
com.airdoc.mpd:style/TextAppearance.Design.Hint = 0x7f1301f6
com.airdoc.mpd:id/material_timepicker_view = 0x7f0a018f
com.airdoc.mpd:id/material_timepicker_cancel_button = 0x7f0a018b
com.airdoc.mpd:id/material_minute_text_input = 0x7f0a0188
com.airdoc.mpd:attr/path_percent = 0x7f040399
com.airdoc.mpd:styleable/FragmentContainerView = 0x7f14003d
com.airdoc.mpd:dimen/design_navigation_icon_size = 0x7f070077
com.airdoc.mpd:string/abc_searchview_description_clear = 0x7f120013
com.airdoc.mpd:id/material_label = 0x7f0a0187
com.airdoc.mpd:id/material_hour_tv = 0x7f0a0186
com.airdoc.mpd:id/material_clock_period_pm_button = 0x7f0a0183
com.airdoc.mpd:dimen/exo_icon_padding = 0x7f070096
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0e00e3
com.airdoc.mpd:color/secondary_text_disabled_material_dark = 0x7f060309
com.airdoc.mpd:id/material_clock_period_am_button = 0x7f0a0182
com.airdoc.mpd:id/material_clock_level = 0x7f0a0181
com.airdoc.mpd:styleable/RadialViewGroup = 0x7f14007b
com.airdoc.mpd:id/material_clock_face = 0x7f0a017f
com.airdoc.mpd:id/match_constraint = 0x7f0a017b
com.airdoc.mpd:id/marquee = 0x7f0a0179
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1303a3
com.airdoc.mpd:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1302ca
com.airdoc.mpd:macro/m3_comp_search_view_docked_container_shape = 0x7f0e00f5
com.airdoc.mpd:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f0b003a
com.airdoc.mpd:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f07026d
com.airdoc.mpd:id/m3_side_sheet = 0x7f0a0178
com.airdoc.mpd:id/lottie_layer_name = 0x7f0a0177
com.airdoc.mpd:id/ll_scanner_settings = 0x7f0a0173
com.airdoc.mpd:id/ll_loading = 0x7f0a0170
com.airdoc.mpd:id/ll_domain = 0x7f0a016d
com.airdoc.mpd:drawable/$ic_launcher_foreground__0 = 0x7f080006
com.airdoc.mpd:id/parallax = 0x7f0a01e0
com.airdoc.mpd:id/list_item = 0x7f0a0167
com.airdoc.mpd:id/listMode = 0x7f0a0166
com.airdoc.mpd:id/linear = 0x7f0a0165
com.airdoc.mpd:id/line3 = 0x7f0a0164
com.airdoc.mpd:drawable/ic_m3_chip_close = 0x7f080131
com.airdoc.mpd:id/light = 0x7f0a0162
com.airdoc.mpd:id/legacy = 0x7f0a0161
com.airdoc.mpd:style/Base.Theme.AppCompat.Light = 0x7f130053
com.airdoc.mpd:string/exo_track_unknown_name = 0x7f120078
com.airdoc.mpd:id/leftToRight = 0x7f0a0160
com.airdoc.mpd:style/Widget.AppCompat.PopupMenu = 0x7f130343
com.airdoc.mpd:string/exo_track_bitrate = 0x7f120067
com.airdoc.mpd:id/layout = 0x7f0a015e
com.airdoc.mpd:id/labeled = 0x7f0a015d
com.airdoc.mpd:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f130381
com.airdoc.mpd:dimen/mtrl_btn_icon_padding = 0x7f07027d
com.airdoc.mpd:id/jumpToEnd = 0x7f0a015b
com.airdoc.mpd:id/iv_version = 0x7f0a0159
com.airdoc.mpd:id/iv_user_info_bg = 0x7f0a0158
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f130272
com.airdoc.mpd:style/Widget.MaterialComponents.TabLayout = 0x7f130466
com.airdoc.mpd:id/iv_setting_red_dot = 0x7f0a0156
com.airdoc.mpd:style/ThemeOverlay.AppCompat.Light = 0x7f1302a4
com.airdoc.mpd:style/Base.V24.Theme.Material3.Dark = 0x7f1300b2
com.airdoc.mpd:attr/scaleFromTextSize = 0x7f0403d4
com.airdoc.mpd:id/textinput_error = 0x7f0a026b
com.airdoc.mpd:attr/constraintSetStart = 0x7f04013f
com.airdoc.mpd:drawable/avd_show_password = 0x7f08007a
com.airdoc.mpd:id/iv_select = 0x7f0a0154
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Button = 0x7f130477
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1302eb
com.airdoc.mpd:id/iv_scan_frame = 0x7f0a0152
com.airdoc.mpd:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.airdoc.mpd:color/mtrl_tabs_icon_color_selector = 0x7f0602ed
com.airdoc.mpd:id/iv_scan = 0x7f0a0150
com.airdoc.mpd:id/exo_text = 0x7f0a00ff
com.airdoc.mpd:string/material_slider_value = 0x7f12009d
com.airdoc.mpd:drawable/read_common_bg = 0x7f0801b2
com.airdoc.mpd:id/iv_right_btn = 0x7f0a014f
com.airdoc.mpd:drawable/exo_legacy_controls_repeat_one = 0x7f0800e8
com.airdoc.mpd:id/exo_icon = 0x7f0a00e5
com.airdoc.mpd:id/iv_refresh = 0x7f0a014e
com.airdoc.mpd:id/iv_loading = 0x7f0a014b
com.airdoc.mpd:drawable/abc_ic_clear_material = 0x7f080040
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1303cc
com.airdoc.mpd:id/iv_language = 0x7f0a014a
com.airdoc.mpd:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0e0094
com.airdoc.mpd:attr/layout = 0x7f04027d
com.airdoc.mpd:color/m3_ref_palette_error10 = 0x7f060112
com.airdoc.mpd:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0a02b7
com.airdoc.mpd:id/iv_input_phone = 0x7f0a0149
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Overline = 0x7f13022c
com.airdoc.mpd:drawable/exo_styled_controls_play = 0x7f0800ff
com.airdoc.mpd:id/iv_input_name = 0x7f0a0148
com.airdoc.mpd:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f070152
com.airdoc.mpd:id/iv_input_gender = 0x7f0a0147
com.airdoc.mpd:dimen/material_clock_size = 0x7f07024a
com.airdoc.mpd:id/iv_input_age = 0x7f0a0146
com.airdoc.mpd:id/iv_device_info = 0x7f0a0144
com.airdoc.mpd:macro/m3_comp_bottom_app_bar_container_color = 0x7f0e0005
com.airdoc.mpd:layout/abc_screen_toolbar = 0x7f0d0017
com.airdoc.mpd:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1302e1
com.airdoc.mpd:id/exo_shutter = 0x7f0a00fb
com.airdoc.mpd:id/iv_detection = 0x7f0a0143
com.airdoc.mpd:id/iv_cross = 0x7f0a0142
com.airdoc.mpd:style/Widget.Design.BottomNavigationView = 0x7f13035a
com.airdoc.mpd:layout/mtrl_picker_header_toggle = 0x7f0d007f
com.airdoc.mpd:id/iv_code_loading = 0x7f0a0141
com.airdoc.mpd:id/iv_code = 0x7f0a0140
com.airdoc.mpd:id/inward = 0x7f0a013b
com.airdoc.mpd:id/info = 0x7f0a0139
com.airdoc.mpd:id/included = 0x7f0a0137
com.airdoc.mpd:id/mtrl_internal_children_alpha_tag = 0x7f0a01aa
com.airdoc.mpd:id/immediateStop = 0x7f0a0136
com.airdoc.mpd:attr/cornerSizeBottomRight = 0x7f040160
com.airdoc.mpd:id/image = 0x7f0a0135
com.airdoc.mpd:id/ifRoom = 0x7f0a0132
com.airdoc.mpd:id/icon_only = 0x7f0a0131
com.airdoc.mpd:color/material_on_surface_emphasis_medium = 0x7f06027f
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302ee
com.airdoc.mpd:id/icon = 0x7f0a012f
com.airdoc.mpd:id/horizontal_only = 0x7f0a012e
com.airdoc.mpd:id/hide_ime_id = 0x7f0a0129
com.airdoc.mpd:id/grouping = 0x7f0a0125
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1302b4
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f13016f
com.airdoc.mpd:id/graph_wrap = 0x7f0a0123
com.airdoc.mpd:id/glide_custom_view_target_tag = 0x7f0a0120
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f130043
com.airdoc.mpd:string/exo_controls_fullscreen_exit_description = 0x7f120048
com.airdoc.mpd:id/fullscreen_header = 0x7f0a011d
com.airdoc.mpd:id/fragment_container_view_tag = 0x7f0a011b
com.airdoc.mpd:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f130065
com.airdoc.mpd:id/fl_start_up_mode = 0x7f0a0117
com.airdoc.mpd:style/Widget.Material3.Chip.Assist = 0x7f13038c
com.airdoc.mpd:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f130239
com.airdoc.mpd:layout/mtrl_alert_dialog_actions = 0x7f0d0065
com.airdoc.mpd:drawable/abc_cab_background_top_mtrl_alpha = 0x7f08003a
com.airdoc.mpd:id/fl_calibration_root = 0x7f0a0116
com.airdoc.mpd:attr/behavior_fitToContents = 0x7f040073
com.airdoc.mpd:attr/backgroundTint = 0x7f040056
com.airdoc.mpd:style/Base.Widget.AppCompat.Button.Colored = 0x7f1300d2
com.airdoc.mpd:integer/google_play_services_version = 0x7f0b000a
com.airdoc.mpd:id/fixed_width = 0x7f0a0115
com.airdoc.mpd:id/password_toggle = 0x7f0a01e5
com.airdoc.mpd:id/fixed = 0x7f0a0113
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f130278
com.airdoc.mpd:id/fitToContents = 0x7f0a0111
com.airdoc.mpd:id/fit = 0x7f0a010d
com.airdoc.mpd:layout/abc_popup_menu_item_layout = 0x7f0d0013
com.airdoc.mpd:raw/speech_2 = 0x7f11000b
com.airdoc.mpd:drawable/notification_oversize_large_icon_bg = 0x7f0801ad
com.airdoc.mpd:id/fillStart = 0x7f0a0109
com.airdoc.mpd:id/fillEnd = 0x7f0a0108
com.airdoc.mpd:id/fade = 0x7f0a0105
com.airdoc.mpd:id/expanded_menu = 0x7f0a0104
com.airdoc.mpd:id/exo_vr = 0x7f0a0102
com.airdoc.mpd:id/exo_sub_text = 0x7f0a00fc
com.airdoc.mpd:id/exo_shuffle = 0x7f0a00fa
com.airdoc.mpd:id/exo_rew_with_amount = 0x7f0a00f7
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f130031
com.airdoc.mpd:id/fitEnd = 0x7f0a010f
com.airdoc.mpd:color/m3_sys_color_light_inverse_on_surface = 0x7f0601e0
com.airdoc.mpd:macro/m3_comp_text_button_label_text_type = 0x7f0e0146
com.airdoc.mpd:id/exo_pause = 0x7f0a00ed
com.airdoc.mpd:id/exo_overflow_show = 0x7f0a00eb
com.airdoc.mpd:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f130351
com.airdoc.mpd:id/exo_overflow_hide = 0x7f0a00ea
com.airdoc.mpd:layout/design_navigation_item_separator = 0x7f0d0031
com.airdoc.mpd:color/white_0 = 0x7f06031a
com.airdoc.mpd:id/exo_next = 0x7f0a00e9
com.airdoc.mpd:drawable/btn_checkbox_checked_mtrl = 0x7f08007b
com.airdoc.mpd:dimen/m3_badge_with_text_vertical_offset = 0x7f0700dd
com.airdoc.mpd:id/exo_minimal_fullscreen = 0x7f0a00e8
com.airdoc.mpd:drawable/exo_icon_play = 0x7f0800d5
com.airdoc.mpd:id/exo_fullscreen = 0x7f0a00e4
com.airdoc.mpd:string/abc_activitychooserview_choose_application = 0x7f120005
com.airdoc.mpd:id/exo_ffwd_with_amount = 0x7f0a00e3
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1302ed
com.airdoc.mpd:color/material_dynamic_neutral_variant90 = 0x7f060242
com.airdoc.mpd:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0e00b8
com.airdoc.mpd:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f0701b7
com.airdoc.mpd:string/abc_activity_chooser_view_see_all = 0x7f120004
com.airdoc.mpd:id/exo_extra_controls = 0x7f0a00e0
com.airdoc.mpd:id/exo_controls_background = 0x7f0a00dd
com.airdoc.mpd:id/exo_check = 0x7f0a00d9
com.airdoc.mpd:id/exo_center_controls = 0x7f0a00d8
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline3 = 0x7f130228
com.airdoc.mpd:dimen/m3_fab_border_width = 0x7f0701d2
com.airdoc.mpd:id/exo_buffering = 0x7f0a00d7
com.airdoc.mpd:id/exo_bottom_bar = 0x7f0a00d6
com.airdoc.mpd:id/exo_basic_controls = 0x7f0a00d5
com.airdoc.mpd:id/exitUntilCollapsed = 0x7f0a00d1
com.airdoc.mpd:id/et_phone = 0x7f0a00d0
com.airdoc.mpd:attr/layout_constraintDimensionRatio = 0x7f040292
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0e0012
com.airdoc.mpd:layout/activity_detection = 0x7f0d001e
com.airdoc.mpd:id/et_detection_code = 0x7f0a00ce
com.airdoc.mpd:id/enterAlwaysCollapsed = 0x7f0a00cc
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1303bf
com.airdoc.mpd:color/material_personalized__highlighted_text_inverse = 0x7f060282
com.airdoc.mpd:color/mtrl_btn_ripple_color = 0x7f0602c1
com.airdoc.mpd:id/end_padder = 0x7f0a00ca
com.airdoc.mpd:styleable/ConstraintOverride = 0x7f14002c
com.airdoc.mpd:string/mtrl_picker_range_header_only_start_selected = 0x7f1200c8
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary40 = 0x7f0600ef
com.airdoc.mpd:id/end = 0x7f0a00c8
com.airdoc.mpd:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f130255
com.airdoc.mpd:string/exo_track_surround_7_point_1 = 0x7f120076
com.airdoc.mpd:attr/fontProviderAuthority = 0x7f04020a
com.airdoc.mpd:id/embed = 0x7f0a00c7
com.airdoc.mpd:style/Widget.MaterialComponents.ProgressIndicator = 0x7f130460
com.airdoc.mpd:attr/windowActionModeOverlay = 0x7f04051e
com.airdoc.mpd:id/elastic = 0x7f0a00c6
com.airdoc.mpd:id/edit_text_id = 0x7f0a00c5
com.airdoc.mpd:id/edit_query = 0x7f0a00c4
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar = 0x7f130328
com.airdoc.mpd:id/edge = 0x7f0a00c3
com.airdoc.mpd:id/east = 0x7f0a00c2
com.airdoc.mpd:id/easeOut = 0x7f0a00c1
com.airdoc.mpd:id/easeIn = 0x7f0a00bf
com.airdoc.mpd:attr/fabSize = 0x7f0401df
com.airdoc.mpd:id/dragLeft = 0x7f0a00ba
com.airdoc.mpd:attr/boxCornerRadiusTopEnd = 0x7f04008b
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0e0071
com.airdoc.mpd:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0602dc
com.airdoc.mpd:id/dragDown = 0x7f0a00b8
com.airdoc.mpd:drawable/exo_styled_controls_audiotrack = 0x7f0800f6
com.airdoc.mpd:id/dragClockwise = 0x7f0a00b7
com.airdoc.mpd:string/mtrl_chip_close_icon_content_description = 0x7f1200b1
com.airdoc.mpd:attr/motionEasingStandardAccelerateInterpolator = 0x7f04035d
com.airdoc.mpd:id/disjoint = 0x7f0a00b5
com.airdoc.mpd:styleable/AspectRatioFrameLayout = 0x7f140013
com.airdoc.mpd:id/disableScroll = 0x7f0a00b4
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary80 = 0x7f0600f3
com.airdoc.mpd:id/disablePostScroll = 0x7f0a00b3
com.airdoc.mpd:id/dimensions = 0x7f0a00af
com.airdoc.mpd:id/dialog_button = 0x7f0a00ae
com.airdoc.mpd:id/dependency_ordering = 0x7f0a00a8
com.airdoc.mpd:style/Base.Widget.AppCompat.RatingBar = 0x7f1300f0
com.airdoc.mpd:id/decelerate = 0x7f0a00a3
com.airdoc.mpd:style/Platform.MaterialComponents.Light = 0x7f13015b
com.airdoc.mpd:id/dark = 0x7f0a00a1
com.airdoc.mpd:id/custom = 0x7f0a009e
com.airdoc.mpd:id/cradle = 0x7f0a009c
com.airdoc.mpd:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0e0119
com.airdoc.mpd:id/cos = 0x7f0a009a
com.airdoc.mpd:string/exo_track_surround = 0x7f120074
com.airdoc.mpd:id/continuousVelocity = 0x7f0a0098
com.airdoc.mpd:id/container = 0x7f0a0093
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog = 0x7f13028e
com.airdoc.mpd:id/confirm_button = 0x7f0a0091
com.airdoc.mpd:id/compress = 0x7f0a0090
com.airdoc.mpd:styleable/State = 0x7f14008b
com.airdoc.mpd:id/compatible = 0x7f0a008f
com.airdoc.mpd:color/m3_timepicker_button_background_color = 0x7f060219
com.airdoc.mpd:id/disableIntraAutoTransition = 0x7f0a00b2
com.airdoc.mpd:id/common_title = 0x7f0a008e
com.airdoc.mpd:id/north = 0x7f0a01c5
com.airdoc.mpd:id/collapseActionView = 0x7f0a008d
com.airdoc.mpd:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0e004e
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f130205
com.airdoc.mpd:id/clockwise = 0x7f0a008b
com.airdoc.mpd:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f13023a
com.airdoc.mpd:id/clip_vertical = 0x7f0a008a
com.airdoc.mpd:id/clear_text = 0x7f0a0088
com.airdoc.mpd:style/ShapeAppearance.Material3.Corner.Large = 0x7f130192
com.airdoc.mpd:id/cl_setting = 0x7f0a0086
com.airdoc.mpd:id/cl_scan_code = 0x7f0a0085
com.airdoc.mpd:string/path_password_eye = 0x7f1200e4
com.airdoc.mpd:id/cl_main_root = 0x7f0a0084
com.airdoc.mpd:id/cl_language_root = 0x7f0a0082
com.airdoc.mpd:id/cl_input_name = 0x7f0a0080
com.airdoc.mpd:attr/carousel_backwardTransition = 0x7f0400ac
com.airdoc.mpd:id/cl_input_gender = 0x7f0a007f
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0e0164
com.airdoc.mpd:id/cl_detection_root = 0x7f0a007d
com.airdoc.mpd:id/cl_content = 0x7f0a007c
com.airdoc.mpd:id/chronometer = 0x7f0a007a
com.airdoc.mpd:id/checked = 0x7f0a0079
com.airdoc.mpd:id/checkbox = 0x7f0a0078
com.airdoc.mpd:color/m3_ref_palette_error0 = 0x7f060111
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f13018c
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral10 = 0x7f0600c6
com.airdoc.mpd:id/chains = 0x7f0a0077
com.airdoc.mpd:attr/checkedIconMargin = 0x7f0400c1
com.airdoc.mpd:id/chain = 0x7f0a0075
com.airdoc.mpd:id/center_horizontal = 0x7f0a0073
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0e0064
com.airdoc.mpd:id/centerCrop = 0x7f0a0071
com.airdoc.mpd:id/cancel_button = 0x7f0a006e
com.airdoc.mpd:attr/barrierAllowsGoneWidgets = 0x7f04006c
com.airdoc.mpd:id/hideable = 0x7f0a012a
com.airdoc.mpd:id/cancel_action = 0x7f0a006d
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0e007e
com.airdoc.mpd:id/callMeasure = 0x7f0a006c
com.airdoc.mpd:string/common_google_play_services_unsupported_text = 0x7f120038
com.airdoc.mpd:id/stretch = 0x7f0a024a
com.airdoc.mpd:dimen/tooltip_corner_radius = 0x7f070336
com.airdoc.mpd:id/cache_measures = 0x7f0a006b
com.airdoc.mpd:id/btn_update_test = 0x7f0a0068
com.airdoc.mpd:id/btn_change_collector = 0x7f0a0067
com.airdoc.mpd:style/shape_image_round_10dp = 0x7f130487
com.airdoc.mpd:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1300a5
com.airdoc.mpd:id/bounce = 0x7f0a0063
com.airdoc.mpd:macro/m3_comp_badge_large_label_text_color = 0x7f0e0003
com.airdoc.mpd:id/expand_activities_button = 0x7f0a0103
com.airdoc.mpd:id/baseline = 0x7f0a005c
com.airdoc.mpd:id/barrier = 0x7f0a005b
com.airdoc.mpd:dimen/material_clock_face_margin_top = 0x7f070241
com.airdoc.mpd:id/icon_group = 0x7f0a0130
com.airdoc.mpd:id/automatic = 0x7f0a005a
com.airdoc.mpd:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0602e2
com.airdoc.mpd:id/open_search_view_content_container = 0x7f0a01d1
com.airdoc.mpd:id/autoComplete = 0x7f0a0057
com.airdoc.mpd:id/anticipate = 0x7f0a0052
com.airdoc.mpd:id/antiClockwise = 0x7f0a0051
com.airdoc.mpd:id/always = 0x7f0a004e
com.airdoc.mpd:id/alertTitle = 0x7f0a004a
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0e0159
com.airdoc.mpd:id/adjust_width = 0x7f0a0049
com.airdoc.mpd:id/adjust_height = 0x7f0a0048
com.airdoc.mpd:id/add = 0x7f0a0047
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f070222
com.airdoc.mpd:id/action_text = 0x7f0a0044
com.airdoc.mpd:attr/indeterminateProgressStyle = 0x7f040248
com.airdoc.mpd:id/ll_back = 0x7f0a0168
com.airdoc.mpd:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f07013d
com.airdoc.mpd:id/action_mode_close_button = 0x7f0a0043
com.airdoc.mpd:id/action_mode_bar_stub = 0x7f0a0042
com.airdoc.mpd:attr/buttonBarNegativeButtonStyle = 0x7f040094
com.airdoc.mpd:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0e0109
com.airdoc.mpd:id/action_image = 0x7f0a003e
com.airdoc.mpd:dimen/exo_setting_width = 0x7f07009c
com.airdoc.mpd:id/action_divider = 0x7f0a003d
com.airdoc.mpd:id/action_bar_subtitle = 0x7f0a0039
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary70 = 0x7f06010c
com.airdoc.mpd:color/m3_ref_palette_neutral92 = 0x7f060130
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f130301
com.airdoc.mpd:id/action_bar_root = 0x7f0a0037
com.airdoc.mpd:attr/layout_constraintWidth_percent = 0x7f0402b3
com.airdoc.mpd:id/actionUp = 0x7f0a0033
com.airdoc.mpd:id/actionDown = 0x7f0a0031
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f070235
com.airdoc.mpd:id/accessibility_custom_action_9 = 0x7f0a002f
com.airdoc.mpd:attr/closeIconEnabled = 0x7f0400ea
com.airdoc.mpd:id/accessibility_custom_action_8 = 0x7f0a002e
com.airdoc.mpd:id/accessibility_custom_action_7 = 0x7f0a002d
com.airdoc.mpd:id/accessibility_custom_action_5 = 0x7f0a002b
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0e0100
com.airdoc.mpd:id/accessibility_custom_action_31 = 0x7f0a0029
com.airdoc.mpd:id/accessibility_custom_action_30 = 0x7f0a0028
com.airdoc.mpd:id/mtrl_picker_text_input_range_start = 0x7f0a01b3
com.airdoc.mpd:id/accessibility_custom_action_29 = 0x7f0a0026
com.airdoc.mpd:attr/colorSecondary = 0x7f040126
com.airdoc.mpd:id/accessibility_custom_action_28 = 0x7f0a0025
com.airdoc.mpd:attr/tabMode = 0x7f04045c
com.airdoc.mpd:drawable/tooltip_frame_dark = 0x7f0801c7
com.airdoc.mpd:id/accessibility_custom_action_27 = 0x7f0a0024
com.airdoc.mpd:style/Theme.Material3.Dark.Dialog = 0x7f130253
com.airdoc.mpd:style/AlertDialog.AppCompat.Light = 0x7f130001
com.airdoc.mpd:id/accessibility_custom_action_26 = 0x7f0a0023
com.airdoc.mpd:id/accessibility_custom_action_24 = 0x7f0a0021
com.airdoc.mpd:dimen/design_snackbar_text_size = 0x7f070088
com.airdoc.mpd:id/accessibility_custom_action_23 = 0x7f0a0020
com.airdoc.mpd:attr/iconifiedByDefault = 0x7f04023c
com.airdoc.mpd:id/material_clock_display = 0x7f0a017d
com.airdoc.mpd:id/accessibility_custom_action_22 = 0x7f0a001f
com.airdoc.mpd:color/black_5 = 0x7f060027
com.airdoc.mpd:attr/errorAccessibilityLabel = 0x7f0401ba
com.airdoc.mpd:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f130165
com.airdoc.mpd:id/accessibility_custom_action_2 = 0x7f0a001c
com.airdoc.mpd:layout/material_clock_period_toggle_land = 0x7f0d005a
com.airdoc.mpd:style/Widget.Material3.CardView.Filled = 0x7f130389
com.airdoc.mpd:id/accessibility_custom_action_16 = 0x7f0a0018
com.airdoc.mpd:id/accessibility_custom_action_15 = 0x7f0a0017
com.airdoc.mpd:id/accessibility_custom_action_10 = 0x7f0a0012
com.airdoc.mpd:id/accessibility_custom_action_0 = 0x7f0a0010
com.airdoc.mpd:id/accessibility_action_clickable_span = 0x7f0a000f
com.airdoc.mpd:id/SHOW_PROGRESS = 0x7f0a000a
com.airdoc.mpd:styleable/GradientColorItem = 0x7f14003f
com.airdoc.mpd:id/SHOW_ALL = 0x7f0a0008
com.airdoc.mpd:id/NO_DEBUG = 0x7f0a0006
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f130422
com.airdoc.mpd:id/groups = 0x7f0a0126
com.airdoc.mpd:id/FUNCTION = 0x7f0a0004
com.airdoc.mpd:layout/dialog_device_info = 0x7f0d0038
com.airdoc.mpd:id/exo_position = 0x7f0a00f1
com.airdoc.mpd:id/CTRL = 0x7f0a0003
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f08001b
com.airdoc.mpd:id/BOTTOM_END = 0x7f0a0001
com.airdoc.mpd:drawable/update_progress_drawable = 0x7f0801c9
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary99 = 0x7f060110
com.airdoc.mpd:drawable/test_level_drawable = 0x7f0801c6
com.airdoc.mpd:drawable/$avd_show_password__1 = 0x7f080004
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.FullScreen = 0x7f130130
com.airdoc.mpd:dimen/mtrl_fab_elevation = 0x7f0702d2
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f13030b
com.airdoc.mpd:font/roboto_medium_numbers = 0x7f090000
com.airdoc.mpd:drawable/switch_mask_therapy_style = 0x7f0801c4
com.airdoc.mpd:drawable/switch_behavior_guidance_style = 0x7f0801c2
com.airdoc.mpd:drawable/stream_logo = 0x7f0801c1
com.airdoc.mpd:drawable/config_switch_mask_therapy_unselected = 0x7f0800b7
com.airdoc.mpd:drawable/selector_play_read_track_head_map_icon = 0x7f0801bd
com.airdoc.mpd:style/Widget.Material3.CheckedTextView = 0x7f13038b
com.airdoc.mpd:drawable/selector_check_protocol_red = 0x7f0801b8
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f130454
com.airdoc.mpd:drawable/seekbar_shaded_area_thumb = 0x7f0801b7
com.airdoc.mpd:drawable/seekbar_shaded_area_progress_drawable = 0x7f0801b6
com.airdoc.mpd:drawable/rounded_pink_button = 0x7f0801b5
com.airdoc.mpd:layout/abc_screen_simple_overlay_action_mode = 0x7f0d0016
com.airdoc.mpd:drawable/read_speed_standard_annotation_bg = 0x7f0801b4
com.airdoc.mpd:drawable/notify_panel_notification_icon_bg = 0x7f0801b1
com.airdoc.mpd:attr/text = 0x7f04046e
com.airdoc.mpd:drawable/notification_tile_bg = 0x7f0801b0
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline1 = 0x7f130226
com.airdoc.mpd:id/ll_view_report = 0x7f0a0175
com.airdoc.mpd:styleable/ConstraintLayout_Layout = 0x7f140029
com.airdoc.mpd:drawable/notification_template_icon_low_bg = 0x7f0801af
com.airdoc.mpd:id/META = 0x7f0a0005
com.airdoc.mpd:drawable/icon_read_assessment_report = 0x7f08015d
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionBar = 0x7f1300c3
com.airdoc.mpd:color/m3_radiobutton_ripple_tint = 0x7f0600c3
com.airdoc.mpd:drawable/notification_icon_background = 0x7f0801ac
com.airdoc.mpd:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0701ba
com.airdoc.mpd:drawable/switch_behavior_guidance_thumb = 0x7f0801c3
com.airdoc.mpd:drawable/notification_bg_normal_pressed = 0x7f0801ab
com.airdoc.mpd:drawable/notification_bg_low_normal = 0x7f0801a8
com.airdoc.mpd:drawable/notification_action_background = 0x7f0801a5
com.airdoc.mpd:color/material_personalized_color_surface_container_lowest = 0x7f0602a6
com.airdoc.mpd:drawable/mtrl_tabs_default_indicator = 0x7f0801a3
com.airdoc.mpd:animator/m3_chip_state_list_anim = 0x7f02000e
com.airdoc.mpd:drawable/mtrl_switch_track_decoration = 0x7f0801a2
com.airdoc.mpd:attr/textAppearanceHeadline6 = 0x7f040480
com.airdoc.mpd:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0700d2
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f130447
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1303ff
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0e006f
com.airdoc.mpd:style/TextAppearance.AppCompat.Caption = 0x7f1301bb
com.airdoc.mpd:drawable/mtrl_switch_track = 0x7f0801a1
com.airdoc.mpd:string/bottomsheet_drag_handle_clicked = 0x7f120022
com.airdoc.mpd:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f08019f
com.airdoc.mpd:color/m3_sys_color_dark_primary = 0x7f060186
com.airdoc.mpd:drawable/mtrl_switch_thumb_unchecked = 0x7f08019e
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0601b9
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f13003c
com.airdoc.mpd:attr/flow_lastHorizontalBias = 0x7f0401fd
com.airdoc.mpd:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f08019d
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents = 0x7f13014d
com.airdoc.mpd:drawable/mtrl_switch_thumb_pressed_checked = 0x7f08019c
com.airdoc.mpd:drawable/mtrl_switch_thumb_pressed = 0x7f08019b
com.airdoc.mpd:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1302e4
com.airdoc.mpd:id/exo_content_frame = 0x7f0a00da
com.airdoc.mpd:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f08019a
com.airdoc.mpd:attr/actionModeShareDrawable = 0x7f04001c
com.airdoc.mpd:style/ThemeOverlay.AppCompat.Dark = 0x7f13029e
com.airdoc.mpd:drawable/mtrl_popupmenu_background_overlay = 0x7f080196
com.airdoc.mpd:drawable/mtrl_navigation_bar_item_background = 0x7f080194
com.airdoc.mpd:drawable/mtrl_ic_indeterminate = 0x7f080193
com.airdoc.mpd:dimen/abc_panel_menu_list_width = 0x7f070034
com.airdoc.mpd:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f08017e
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f13030d
com.airdoc.mpd:drawable/mtrl_ic_error = 0x7f080192
com.airdoc.mpd:drawable/mtrl_ic_checkbox_checked = 0x7f080190
com.airdoc.mpd:string/search_menu_title = 0x7f1200e8
com.airdoc.mpd:attr/textAppearanceSubtitle2 = 0x7f040492
com.airdoc.mpd:attr/colorOutlineVariant = 0x7f04011c
com.airdoc.mpd:drawable/mtrl_ic_arrow_drop_down = 0x7f08018c
com.airdoc.mpd:drawable/mtrl_dropdown_arrow = 0x7f08018b
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f080188
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f130042
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f080186
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f080183
com.airdoc.mpd:drawable/ic_detection_bg = 0x7f08011c
com.airdoc.mpd:styleable/FontFamilyFont = 0x7f14003a
com.airdoc.mpd:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f080181
com.airdoc.mpd:attr/materialCalendarHeaderSelection = 0x7f040309
com.airdoc.mpd:dimen/m3_navigation_item_vertical_padding = 0x7f0701e2
com.airdoc.mpd:layout/exo_list_divider = 0x7f0d003f
com.airdoc.mpd:drawable/mtrl_checkbox_button = 0x7f080180
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0e0158
com.airdoc.mpd:color/m3_sys_color_tertiary_fixed = 0x7f060209
com.airdoc.mpd:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f08017c
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0e0098
com.airdoc.mpd:attr/behavior_autoShrink = 0x7f040070
com.airdoc.mpd:attr/circleCrop = 0x7f0400db
com.airdoc.mpd:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f08017b
com.airdoc.mpd:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f130245
com.airdoc.mpd:drawable/material_ic_calendar_black_24dp = 0x7f080176
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1301e2
com.airdoc.mpd:drawable/material_cursor_drawable = 0x7f080175
com.airdoc.mpd:drawable/main_input_bg = 0x7f080174
com.airdoc.mpd:style/Base.V23.Theme.AppCompat.Light = 0x7f1300b1
com.airdoc.mpd:id/mtrl_card_checked_layer_id = 0x7f0a01a8
com.airdoc.mpd:macro/m3_comp_filled_button_container_color = 0x7f0e0044
com.airdoc.mpd:drawable/m3_tabs_transparent_background = 0x7f080172
com.airdoc.mpd:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0700d0
com.airdoc.mpd:drawable/m3_tabs_line_indicator = 0x7f080170
com.airdoc.mpd:drawable/m3_selection_control_ripple = 0x7f08016e
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f13047f
com.airdoc.mpd:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1300ad
com.airdoc.mpd:drawable/m3_radiobutton_ripple = 0x7f08016d
com.airdoc.mpd:drawable/main_bt_common_bg = 0x7f080173
com.airdoc.mpd:drawable/m3_password_eye = 0x7f08016b
com.airdoc.mpd:attr/boxCornerRadiusBottomEnd = 0x7f040089
com.airdoc.mpd:string/mtrl_picker_save = 0x7f1200cc
com.airdoc.mpd:drawable/m3_bottom_sheet_drag_handle = 0x7f08016a
com.airdoc.mpd:attr/toolbarStyle = 0x7f0404e2
com.airdoc.mpd:drawable/m3_appbar_background = 0x7f080167
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1303b3
com.airdoc.mpd:drawable/login_input_bg = 0x7f080166
com.airdoc.mpd:drawable/input_cursor = 0x7f080165
com.airdoc.mpd:drawable/icon_visual_train_no_open = 0x7f080164
com.airdoc.mpd:style/Widget.Material3.SearchBar.Outlined = 0x7f1303e7
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1302d6
com.airdoc.mpd:attr/yearStyle = 0x7f040527
com.airdoc.mpd:drawable/icon_read_main_logo = 0x7f080160
com.airdoc.mpd:drawable/icon_read_main_ai_logo = 0x7f08015f
com.airdoc.mpd:dimen/m3_navigation_item_shape_inset_top = 0x7f0701e1
com.airdoc.mpd:drawable/icon_patient_library = 0x7f08015c
com.airdoc.mpd:drawable/icon_param_preview_bg = 0x7f08015b
com.airdoc.mpd:drawable/icon_main_bg = 0x7f080159
com.airdoc.mpd:color/abc_decor_view_status_guard_light = 0x7f060006
com.airdoc.mpd:id/action_bar_title = 0x7f0a003a
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0601aa
com.airdoc.mpd:drawable/icon_device_exception_menu = 0x7f080154
com.airdoc.mpd:drawable/icon_common_back = 0x7f080153
com.airdoc.mpd:attr/materialCalendarTheme = 0x7f04030f
com.airdoc.mpd:drawable/icon_calibration_m = 0x7f080151
com.airdoc.mpd:drawable/icon_back_fine_line_arrow = 0x7f08014f
com.airdoc.mpd:layout/material_timepicker = 0x7f0d0061
com.airdoc.mpd:dimen/design_navigation_icon_padding = 0x7f070076
com.airdoc.mpd:id/cl_input_age = 0x7f0a007e
com.airdoc.mpd:drawable/icon_back_black_fine = 0x7f08014e
com.airdoc.mpd:style/TextAppearance.Material3.BodyMedium = 0x7f13020e
com.airdoc.mpd:drawable/m3_tabs_rounded_line_indicator = 0x7f080171
com.airdoc.mpd:drawable/icon_advanced_settings = 0x7f08014a
com.airdoc.mpd:drawable/ic_user_info_bg = 0x7f080147
com.airdoc.mpd:drawable/ic_update_bg = 0x7f080146
com.airdoc.mpd:layout/abc_cascading_menu_item_layout = 0x7f0d000b
com.airdoc.mpd:attr/motionDurationMedium4 = 0x7f04034f
com.airdoc.mpd:drawable/ic_search_black_24 = 0x7f080145
com.airdoc.mpd:drawable/ic_scanner_settings = 0x7f080144
com.airdoc.mpd:drawable/material_ic_clear_black_24dp = 0x7f080177
com.airdoc.mpd:drawable/ic_scan = 0x7f080140
com.airdoc.mpd:drawable/ic_red_dot = 0x7f08013d
com.airdoc.mpd:attr/textAppearanceHeadlineSmall = 0x7f040483
com.airdoc.mpd:drawable/ic_network_exception = 0x7f08013c
com.airdoc.mpd:dimen/abc_dialog_padding_top_material = 0x7f070025
com.airdoc.mpd:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1301cb
com.airdoc.mpd:id/contentPanel = 0x7f0a0095
com.airdoc.mpd:attr/imageAspectRatio = 0x7f04023f
com.airdoc.mpd:drawable/ic_mtrl_chip_close_circle = 0x7f08013b
com.airdoc.mpd:drawable/ic_mtrl_chip_checked_circle = 0x7f08013a
com.airdoc.mpd:string/exo_controls_shuffle_on_description = 0x7f120059
com.airdoc.mpd:color/material_on_background_disabled = 0x7f060277
com.airdoc.mpd:drawable/ic_male_avatar_round = 0x7f080135
com.airdoc.mpd:drawable/ic_loading = 0x7f08012e
com.airdoc.mpd:drawable/ic_launcher_foreground = 0x7f08012d
com.airdoc.mpd:drawable/ic_launcher_background = 0x7f08012c
com.airdoc.mpd:drawable/ic_language_zh_cn = 0x7f08012b
com.airdoc.mpd:style/Platform.AppCompat.Light = 0x7f130158
com.airdoc.mpd:drawable/ic_language_en_us = 0x7f080129
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f0601a0
com.airdoc.mpd:drawable/ic_input_name = 0x7f080126
com.airdoc.mpd:drawable/ic_input_gender = 0x7f080125
com.airdoc.mpd:drawable/abc_cab_background_internal_bg = 0x7f080038
com.airdoc.mpd:id/exo_subtitle = 0x7f0a00fd
com.airdoc.mpd:drawable/ic_input_age = 0x7f080124
com.airdoc.mpd:id/tv_sn = 0x7f0a029f
com.airdoc.mpd:attr/actionModeCloseContentDescription = 0x7f040014
com.airdoc.mpd:drawable/ic_female_avatar_round = 0x7f080123
com.airdoc.mpd:dimen/tooltip_vertical_padding = 0x7f07033b
com.airdoc.mpd:styleable/AnimatedStateListDrawableItem = 0x7f140008
com.airdoc.mpd:drawable/ic_device_info_dialog_right = 0x7f080122
com.airdoc.mpd:drawable/ic_device_info_dialog_left = 0x7f080121
com.airdoc.mpd:drawable/ic_device_info_dialog_content_bg = 0x7f080120
com.airdoc.mpd:styleable/ActionMode = 0x7f140004
com.airdoc.mpd:styleable/CompoundButton = 0x7f140027
com.airdoc.mpd:drawable/ic_device_info = 0x7f08011e
com.airdoc.mpd:drawable/ic_cross = 0x7f08011b
com.airdoc.mpd:drawable/ic_configuration_exception = 0x7f08011a
com.airdoc.mpd:attr/windowActionBar = 0x7f04051c
com.airdoc.mpd:drawable/ic_close_scan = 0x7f080119
com.airdoc.mpd:drawable/ic_version = 0x7f080148
com.airdoc.mpd:drawable/ic_clock_black_24dp = 0x7f080118
com.airdoc.mpd:attr/showTitle = 0x7f0403fd
com.airdoc.mpd:drawable/ic_keyboard_black_24dp = 0x7f080128
com.airdoc.mpd:drawable/ic_call_decline_low = 0x7f080116
com.airdoc.mpd:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1300d8
com.airdoc.mpd:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f0701a4
com.airdoc.mpd:drawable/ic_call_answer_video = 0x7f080113
com.airdoc.mpd:style/Theme.MaterialComponents.CompactMenu = 0x7f13026f
com.airdoc.mpd:attr/motionEffect_strict = 0x7f040364
com.airdoc.mpd:attr/progressBarPadding = 0x7f0403b2
com.airdoc.mpd:id/top = 0x7f0a0276
com.airdoc.mpd:attr/circularflow_viewCenter = 0x7f0400e2
com.airdoc.mpd:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0601d0
com.airdoc.mpd:drawable/ic_call_answer_low = 0x7f080112
com.airdoc.mpd:drawable/ic_call_answer = 0x7f080111
com.airdoc.mpd:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0e009e
com.airdoc.mpd:drawable/ic_arrow_back_black_24 = 0x7f080110
com.airdoc.mpd:attr/SharedValue = 0x7f040000
com.airdoc.mpd:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1302df
com.airdoc.mpd:drawable/gradient_pink_background = 0x7f08010f
com.airdoc.mpd:drawable/googleg_standard_color_18 = 0x7f08010e
com.airdoc.mpd:id/exo_settings_listview = 0x7f0a00f9
com.airdoc.mpd:drawable/exo_styled_controls_speed = 0x7f080108
com.airdoc.mpd:color/design_dark_default_color_primary_variant = 0x7f06005a
com.airdoc.mpd:drawable/exo_styled_controls_shuffle_off = 0x7f080106
com.airdoc.mpd:layout/exo_styled_settings_list_item = 0x7f0d0045
com.airdoc.mpd:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0e014a
com.airdoc.mpd:drawable/exo_styled_controls_settings = 0x7f080105
com.airdoc.mpd:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0e00d7
com.airdoc.mpd:style/Widget.Material3.CollapsingToolbar.Large = 0x7f13039c
com.airdoc.mpd:id/material_timepicker_container = 0x7f0a018c
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary40 = 0x7f0600fc
com.airdoc.mpd:drawable/exo_styled_controls_rewind = 0x7f080104
com.airdoc.mpd:string/str_i_know = 0x7f12010f
com.airdoc.mpd:id/exo_play_pause = 0x7f0a00ef
com.airdoc.mpd:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f130118
com.airdoc.mpd:drawable/exo_styled_controls_pause = 0x7f0800fe
com.airdoc.mpd:layout/abc_tooltip = 0x7f0d001b
com.airdoc.mpd:id/tv_copyright = 0x7f0a0288
com.airdoc.mpd:attr/textAppearanceListItem = 0x7f040489
com.airdoc.mpd:drawable/exo_styled_controls_fastforward = 0x7f0800f8
com.airdoc.mpd:color/selector_common_switch_compat_track_tint = 0x7f06030c
com.airdoc.mpd:dimen/design_snackbar_extra_spacing_horizontal = 0x7f070082
com.airdoc.mpd:raw/speech_8 = 0x7f110011
com.airdoc.mpd:drawable/exo_styled_controls_check = 0x7f0800f7
com.airdoc.mpd:attr/floatingActionButtonSecondaryStyle = 0x7f0401ec
com.airdoc.mpd:drawable/exo_rounded_rectangle = 0x7f0800f5
com.airdoc.mpd:string/fab_transformation_scrim_behavior = 0x7f12007a
com.airdoc.mpd:drawable/exo_notification_stop = 0x7f0800f4
com.airdoc.mpd:drawable/exo_notification_small_icon = 0x7f0800f3
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f07021f
com.airdoc.mpd:drawable/exo_notification_pause = 0x7f0800ef
com.airdoc.mpd:drawable/exo_notification_next = 0x7f0800ee
com.airdoc.mpd:drawable/exo_notification_fastforward = 0x7f0800ed
com.airdoc.mpd:drawable/exo_legacy_controls_shuffle_on = 0x7f0800eb
com.airdoc.mpd:drawable/exo_legacy_controls_rewind = 0x7f0800e9
com.airdoc.mpd:attr/colorSurfaceContainerLow = 0x7f040130
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialDivider = 0x7f130455
com.airdoc.mpd:drawable/exo_legacy_controls_repeat_all = 0x7f0800e6
com.airdoc.mpd:dimen/mtrl_badge_text_size = 0x7f07026b
com.airdoc.mpd:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f13040c
com.airdoc.mpd:attr/deltaPolarAngle = 0x7f040182
com.airdoc.mpd:string/str_exit = 0x7f120105
com.airdoc.mpd:drawable/exo_legacy_controls_previous = 0x7f0800e5
com.airdoc.mpd:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f130156
com.airdoc.mpd:drawable/exo_legacy_controls_play = 0x7f0800e4
com.airdoc.mpd:drawable/exo_legacy_controls_fullscreen_exit = 0x7f0800e1
com.airdoc.mpd:drawable/exo_legacy_controls_fullscreen_enter = 0x7f0800e0
com.airdoc.mpd:style/Base.Widget.Material3.FloatingActionButton = 0x7f130109
com.airdoc.mpd:drawable/exo_icon_repeat_one = 0x7f0800d9
com.airdoc.mpd:layout/exo_player_control_ffwd_button = 0x7f0d0040
com.airdoc.mpd:drawable/abc_ic_voice_search_api_material = 0x7f08004a
com.airdoc.mpd:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13011a
com.airdoc.mpd:drawable/exo_icon_previous = 0x7f0800d6
com.airdoc.mpd:drawable/exo_icon_next = 0x7f0800d3
com.airdoc.mpd:attr/materialAlertDialogTitleIconStyle = 0x7f0402fc
com.airdoc.mpd:id/mtrl_view_tag_bottom_padding = 0x7f0a01b5
com.airdoc.mpd:drawable/exo_icon_fullscreen_exit = 0x7f0800d2
com.airdoc.mpd:drawable/exo_icon_fullscreen_enter = 0x7f0800d1
com.airdoc.mpd:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1302a9
com.airdoc.mpd:attr/dragThreshold = 0x7f040193
com.airdoc.mpd:id/off = 0x7f0a01ca
com.airdoc.mpd:drawable/exo_icon_fastforward = 0x7f0800d0
com.airdoc.mpd:attr/queryPatterns = 0x7f0403b9
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0e00fd
com.airdoc.mpd:drawable/exo_icon_circular_play = 0x7f0800cf
com.airdoc.mpd:styleable/CardView = 0x7f14001b
com.airdoc.mpd:drawable/exo_ic_subtitle_on = 0x7f0800ce
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0e0077
com.airdoc.mpd:id/cut = 0x7f0a00a0
com.airdoc.mpd:drawable/exo_ic_settings = 0x7f0800c9
com.airdoc.mpd:drawable/exo_ic_forward = 0x7f0800c3
com.airdoc.mpd:attr/framePosition = 0x7f040217
com.airdoc.mpd:drawable/exo_ic_default_album_image = 0x7f0800c2
com.airdoc.mpd:id/tv_detection_code = 0x7f0a028d
com.airdoc.mpd:drawable/selector_common_radio_button = 0x7f0801b9
com.airdoc.mpd:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0702d9
com.airdoc.mpd:drawable/exo_ic_chevron_left = 0x7f0800c0
com.airdoc.mpd:drawable/exo_ic_audiotrack = 0x7f0800be
com.airdoc.mpd:attr/listChoiceIndicatorSingleAnimated = 0x7f0402cf
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f13003e
com.airdoc.mpd:drawable/design_snackbar_background = 0x7f0800bc
com.airdoc.mpd:drawable/design_password_eye = 0x7f0800bb
com.airdoc.mpd:attr/colorError = 0x7f040104
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1302b7
com.airdoc.mpd:id/mtrl_picker_title_text = 0x7f0a01b4
com.airdoc.mpd:drawable/design_fab_background = 0x7f0800b8
com.airdoc.mpd:layout/mtrl_alert_select_dialog_multichoice = 0x7f0d0068
com.airdoc.mpd:style/Theme.Material3.Light = 0x7f130264
com.airdoc.mpd:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f130233
com.airdoc.mpd:attr/labelVisibilityMode = 0x7f040279
com.airdoc.mpd:drawable/common_white_round_60_bg = 0x7f0800b4
com.airdoc.mpd:drawable/common_white_round_50_bg = 0x7f0800b3
com.airdoc.mpd:drawable/common_white_round_40_bg = 0x7f0800b2
com.airdoc.mpd:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0e00b1
com.airdoc.mpd:drawable/common_white_round_25_bg = 0x7f0800b1
com.airdoc.mpd:drawable/common_title_back_bg = 0x7f0800ac
com.airdoc.mpd:style/Widget.MaterialComponents.BottomSheet = 0x7f13041a
com.airdoc.mpd:raw/proceed_detection = 0x7f110006
com.airdoc.mpd:attr/cardViewStyle = 0x7f0400ab
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0e006c
com.airdoc.mpd:drawable/common_stroke_round_bg = 0x7f0800ab
com.airdoc.mpd:color/m3_button_foreground_color_selector = 0x7f06008c
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1303a7
com.airdoc.mpd:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1302ce
com.airdoc.mpd:style/Widget.Material3.CircularProgressIndicator = 0x7f130397
com.airdoc.mpd:drawable/common_green_round_bg = 0x7f0800a5
com.airdoc.mpd:attr/circularflow_radiusInDP = 0x7f0400e1
com.airdoc.mpd:drawable/common_gray_round_bg = 0x7f0800a4
com.airdoc.mpd:drawable/exo_styled_controls_shuffle_on = 0x7f080107
com.airdoc.mpd:drawable/common_google_signin_btn_text_light_focused = 0x7f0800a1
com.airdoc.mpd:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1302c2
com.airdoc.mpd:color/dim_foreground_disabled_material_light = 0x7f060076
com.airdoc.mpd:color/m3_sys_color_dark_on_primary_container = 0x7f06017d
com.airdoc.mpd:id/ignoreRequest = 0x7f0a0134
com.airdoc.mpd:drawable/ic_scan_camera_tips = 0x7f080142
com.airdoc.mpd:drawable/common_google_signin_btn_text_disabled = 0x7f08009f
com.airdoc.mpd:styleable/MaterialAlertDialog = 0x7f140052
com.airdoc.mpd:dimen/mtrl_calendar_year_height = 0x7f0702b3
com.airdoc.mpd:drawable/common_google_signin_btn_icon_light_focused = 0x7f080098
com.airdoc.mpd:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0e0057
com.airdoc.mpd:attr/colorOnTertiaryContainer = 0x7f040118
com.airdoc.mpd:drawable/common_entity_button_enable_bg = 0x7f080090
com.airdoc.mpd:id/material_hour_text_input = 0x7f0a0185
com.airdoc.mpd:drawable/exo_legacy_controls_shuffle_off = 0x7f0800ea
com.airdoc.mpd:color/exo_black_opacity_60 = 0x7f06007b
com.airdoc.mpd:drawable/common_eb4e89_round_bg = 0x7f08008c
com.airdoc.mpd:color/material_on_surface_disabled = 0x7f06027d
com.airdoc.mpd:drawable/common_ea4e3d_round_bg = 0x7f08008b
com.airdoc.mpd:drawable/common_d6dce1_round_bg = 0x7f080089
com.airdoc.mpd:drawable/common_ce005c_stroke_20_bg = 0x7f080088
com.airdoc.mpd:drawable/common_blue_round_bg = 0x7f080087
com.airdoc.mpd:drawable/btn_checkbox_unchecked_mtrl = 0x7f08007d
com.airdoc.mpd:drawable/avd_hide_password = 0x7f080079
com.airdoc.mpd:color/m3_appbar_overlay_color = 0x7f060087
com.airdoc.mpd:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080074
com.airdoc.mpd:drawable/abc_textfield_default_mtrl_alpha = 0x7f080073
com.airdoc.mpd:drawable/abc_tab_indicator_mtrl_alpha = 0x7f08006d
com.airdoc.mpd:attr/motionDurationExtraLong4 = 0x7f040347
com.airdoc.mpd:drawable/abc_switch_track_mtrl_alpha = 0x7f08006b
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0e0161
com.airdoc.mpd:drawable/abc_star_half_black_48dp = 0x7f080069
com.airdoc.mpd:drawable/abc_spinner_textfield_background_material = 0x7f080067
com.airdoc.mpd:string/str_please_enter_name = 0x7f12011e
com.airdoc.mpd:attr/flow_firstHorizontalStyle = 0x7f0401f6
com.airdoc.mpd:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight = 0x7f130270
com.airdoc.mpd:drawable/abc_spinner_mtrl_am_alpha = 0x7f080066
com.airdoc.mpd:attr/collapsedTitleTextAppearance = 0x7f0400f5
com.airdoc.mpd:style/AlertDialog.AppCompat = 0x7f130000
com.airdoc.mpd:drawable/abc_seekbar_tick_mark_material = 0x7f080064
com.airdoc.mpd:id/exo_play = 0x7f0a00ee
com.airdoc.mpd:attr/closeIconTint = 0x7f0400ee
com.airdoc.mpd:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08005f
com.airdoc.mpd:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f080189
com.airdoc.mpd:drawable/abc_ratingbar_indicator_material = 0x7f08005b
com.airdoc.mpd:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f130260
com.airdoc.mpd:color/material_dynamic_secondary95 = 0x7f06025d
com.airdoc.mpd:drawable/abc_popup_background_mtrl_mult = 0x7f08005a
com.airdoc.mpd:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080059
com.airdoc.mpd:string/str_settings = 0x7f120126
com.airdoc.mpd:drawable/abc_list_selector_holo_light = 0x7f080058
com.airdoc.mpd:drawable/abc_list_selector_disabled_holo_light = 0x7f080056
com.airdoc.mpd:drawable/ic_view_report = 0x7f080149
com.airdoc.mpd:drawable/abc_list_pressed_holo_light = 0x7f080052
com.airdoc.mpd:drawable/abc_list_pressed_holo_dark = 0x7f080051
com.airdoc.mpd:layout/notification_media_cancel_action = 0x7f0d0087
com.airdoc.mpd:drawable/abc_list_longpressed_holo = 0x7f080050
com.airdoc.mpd:style/Widget.MaterialComponents.Tooltip = 0x7f130484
com.airdoc.mpd:color/m3_ref_palette_secondary70 = 0x7f060158
com.airdoc.mpd:drawable/abc_list_divider_mtrl_alpha = 0x7f08004e
com.airdoc.mpd:color/material_harmonized_color_on_error = 0x7f060275
com.airdoc.mpd:color/m3_sys_color_light_on_tertiary_container = 0x7f0601ed
com.airdoc.mpd:string/exo_controls_repeat_off_description = 0x7f120052
com.airdoc.mpd:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f13020b
com.airdoc.mpd:dimen/m3_comp_search_bar_avatar_size = 0x7f07018f
com.airdoc.mpd:id/all = 0x7f0a004c
com.airdoc.mpd:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f0701b1
com.airdoc.mpd:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f080047
com.airdoc.mpd:attr/materialDisplayDividerStyle = 0x7f040317
com.airdoc.mpd:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f070198
com.airdoc.mpd:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f080046
com.airdoc.mpd:style/TextAppearance.AppCompat.Button = 0x7f1301ba
com.airdoc.mpd:drawable/abc_ic_menu_overflow_material = 0x7f080045
com.airdoc.mpd:id/material_textinput_timepicker = 0x7f0a018a
com.airdoc.mpd:macro/m3_comp_suggestion_chip_container_shape = 0x7f0e0118
com.airdoc.mpd:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f08003f
com.airdoc.mpd:drawable/abc_ic_ab_back_material = 0x7f08003e
com.airdoc.mpd:id/dragRight = 0x7f0a00bb
com.airdoc.mpd:drawable/ic_language_settings = 0x7f08012a
com.airdoc.mpd:drawable/abc_edit_text_material = 0x7f08003d
com.airdoc.mpd:attr/boxStrokeWidthFocused = 0x7f040090
com.airdoc.mpd:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f080036
com.airdoc.mpd:drawable/abc_dialog_material_background = 0x7f08003c
com.airdoc.mpd:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f080035
com.airdoc.mpd:attr/motion_triggerOnCollision = 0x7f04036f
com.airdoc.mpd:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f080034
com.airdoc.mpd:style/Widget.MaterialComponents.Chip.Filter = 0x7f13042d
com.airdoc.mpd:style/TextAppearance.AppCompat.Body2 = 0x7f1301b9
com.airdoc.mpd:drawable/abc_btn_check_material_anim = 0x7f08002d
com.airdoc.mpd:drawable/abc_btn_check_material = 0x7f08002c
com.airdoc.mpd:anim/abc_fade_out = 0x7f010001
com.airdoc.mpd:attr/theme = 0x7f0404b4
com.airdoc.mpd:color/m3_timepicker_clock_text_color = 0x7f06021c
com.airdoc.mpd:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f080026
com.airdoc.mpd:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0701e4
com.airdoc.mpd:dimen/mtrl_navigation_rail_compact_width = 0x7f0702e8
com.airdoc.mpd:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f080024
com.airdoc.mpd:dimen/hint_pressed_alpha_material_light = 0x7f0700bc
com.airdoc.mpd:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f080023
com.airdoc.mpd:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f13005f
com.airdoc.mpd:attr/cornerFamilyTopLeft = 0x7f04015b
com.airdoc.mpd:id/beginOnFirstDraw = 0x7f0a005d
com.airdoc.mpd:attr/isMaterialTheme = 0x7f040253
com.airdoc.mpd:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f080021
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f08001d
com.airdoc.mpd:attr/spinnerStyle = 0x7f04041a
com.airdoc.mpd:drawable/exo_ic_pause_circle_filled = 0x7f0800c6
com.airdoc.mpd:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f13025c
com.airdoc.mpd:attr/actionProviderClass = 0x7f040023
com.airdoc.mpd:attr/paddingBottomNoButtons = 0x7f040387
com.airdoc.mpd:color/m3_textfield_stroke_color = 0x7f060218
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f080016
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f080014
com.airdoc.mpd:id/SHIFT = 0x7f0a0007
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f080013
com.airdoc.mpd:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f080020
com.airdoc.mpd:style/Animation.Material3.SideSheetDialog = 0x7f130007
com.airdoc.mpd:dimen/mtrl_calendar_text_input_padding_top = 0x7f0702af
com.airdoc.mpd:color/m3_fab_efab_foreground_color_selector = 0x7f0600b0
com.airdoc.mpd:layout/dialog_language_settings = 0x7f0d003a
com.airdoc.mpd:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010016
com.airdoc.mpd:attr/enforceTextAppearance = 0x7f0401b8
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f080012
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f080011
com.airdoc.mpd:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.airdoc.mpd:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f08000e
com.airdoc.mpd:integer/mtrl_view_gone = 0x7f0b0042
com.airdoc.mpd:drawable/$m3_avd_hide_password__0 = 0x7f080007
com.airdoc.mpd:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0e0035
com.airdoc.mpd:id/dropdown_menu = 0x7f0a00be
com.airdoc.mpd:attr/colorOnSecondaryContainer = 0x7f040111
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary20 = 0x7f0600fa
com.airdoc.mpd:drawable/$avd_show_password__0 = 0x7f080003
com.airdoc.mpd:layout/dialog_device_reminder = 0x7f0d0039
com.airdoc.mpd:dimen/tooltip_y_offset_touch = 0x7f07033d
com.airdoc.mpd:attr/font = 0x7f040208
com.airdoc.mpd:color/m3_default_color_primary_text = 0x7f0600a1
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f070181
com.airdoc.mpd:color/color_CDCDCD = 0x7f060040
com.airdoc.mpd:dimen/tooltip_precise_anchor_threshold = 0x7f07033a
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f130206
com.airdoc.mpd:dimen/tooltip_horizontal_padding = 0x7f070337
com.airdoc.mpd:dimen/notification_subtext_size = 0x7f070333
com.airdoc.mpd:attr/yearSelectedStyle = 0x7f040526
com.airdoc.mpd:attr/labelBehavior = 0x7f040277
com.airdoc.mpd:dimen/notification_right_side_padding_top = 0x7f070330
com.airdoc.mpd:id/iv_scanner_settings = 0x7f0a0153
com.airdoc.mpd:string/material_motion_easing_decelerated = 0x7f120097
com.airdoc.mpd:attr/chipEndPadding = 0x7f0400c9
com.airdoc.mpd:dimen/notification_large_icon_width = 0x7f07032c
com.airdoc.mpd:dimen/notification_big_circle_margin = 0x7f070329
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral4 = 0x7f0600ce
com.airdoc.mpd:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f070326
com.airdoc.mpd:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.airdoc.mpd:dimen/mtrl_tooltip_padding = 0x7f070325
com.airdoc.mpd:layout/design_navigation_menu = 0x7f0d0033
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0e0083
com.airdoc.mpd:dimen/mtrl_tooltip_minHeight = 0x7f070323
com.airdoc.mpd:color/color_4A90E2 = 0x7f06003d
com.airdoc.mpd:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f07031e
com.airdoc.mpd:string/str_request_timeout = 0x7f120121
com.airdoc.mpd:dimen/mtrl_textinput_end_icon_margin_start = 0x7f07031d
com.airdoc.mpd:color/design_dark_default_color_secondary_variant = 0x7f06005c
com.airdoc.mpd:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f070317
com.airdoc.mpd:color/m3_sys_color_dark_outline_variant = 0x7f060185
com.airdoc.mpd:attr/colorScheme = 0x7f040125
com.airdoc.mpd:dimen/mtrl_switch_track_height = 0x7f070315
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f130176
com.airdoc.mpd:attr/logoDescription = 0x7f0402de
com.airdoc.mpd:dimen/mtrl_snackbar_margin = 0x7f07030e
com.airdoc.mpd:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f07030b
com.airdoc.mpd:dimen/m3_bottom_sheet_modal_elevation = 0x7f0700e7
com.airdoc.mpd:color/material_dynamic_neutral95 = 0x7f060236
com.airdoc.mpd:dimen/mtrl_slider_track_height = 0x7f070308
com.airdoc.mpd:dimen/mtrl_slider_label_square_side = 0x7f070304
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f130445
com.airdoc.mpd:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1301c7
com.airdoc.mpd:dimen/mtrl_slider_label_radius = 0x7f070303
com.airdoc.mpd:style/Base.Widget.Material3.Chip = 0x7f130102
com.airdoc.mpd:dimen/mtrl_slider_halo_radius = 0x7f070301
com.airdoc.mpd:drawable/exo_ic_skip_next = 0x7f0800ca
com.airdoc.mpd:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0702fc
com.airdoc.mpd:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0702fb
com.airdoc.mpd:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f13015e
com.airdoc.mpd:attr/itemShapeInsetStart = 0x7f040267
com.airdoc.mpd:color/material_dynamic_primary99 = 0x7f060251
com.airdoc.mpd:dimen/mtrl_progress_circular_size = 0x7f0702f5
com.airdoc.mpd:anim/abc_slide_out_bottom = 0x7f010008
com.airdoc.mpd:drawable/common_google_signin_btn_text_dark_focused = 0x7f08009c
com.airdoc.mpd:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f13006e
com.airdoc.mpd:drawable/common_entity_button_bg = 0x7f08008e
com.airdoc.mpd:dimen/mtrl_progress_circular_radius = 0x7f0702f4
com.airdoc.mpd:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0701c2
com.airdoc.mpd:dimen/mtrl_navigation_rail_icon_size = 0x7f0702ec
com.airdoc.mpd:drawable/ic_device_info_dialog_bg = 0x7f08011f
com.airdoc.mpd:color/m3_sys_color_light_on_surface = 0x7f0601ea
com.airdoc.mpd:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0701c7
com.airdoc.mpd:color/material_dynamic_secondary99 = 0x7f06025e
com.airdoc.mpd:color/material_dynamic_tertiary30 = 0x7f060263
com.airdoc.mpd:dimen/mtrl_navigation_rail_default_width = 0x7f0702e9
com.airdoc.mpd:layout/design_navigation_item = 0x7f0d002f
com.airdoc.mpd:id/tv_valid_until = 0x7f0a02a7
com.airdoc.mpd:dimen/abc_action_bar_default_height_material = 0x7f070002
com.airdoc.mpd:drawable/abc_item_background_holo_dark = 0x7f08004b
com.airdoc.mpd:color/material_blue_grey_900 = 0x7f060225
com.airdoc.mpd:dimen/mtrl_navigation_rail_active_text_size = 0x7f0702e7
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light = 0x7f13006f
com.airdoc.mpd:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0702e6
com.airdoc.mpd:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0702e5
com.airdoc.mpd:color/material_personalized_color_control_highlight = 0x7f060285
com.airdoc.mpd:dimen/exo_settings_height = 0x7f07009d
com.airdoc.mpd:dimen/mtrl_navigation_item_icon_size = 0x7f0702e4
com.airdoc.mpd:attr/buttonIconTint = 0x7f04009c
com.airdoc.mpd:dimen/mtrl_navigation_elevation = 0x7f0702e1
com.airdoc.mpd:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0e0126
com.airdoc.mpd:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0702e0
com.airdoc.mpd:drawable/abc_vector_test = 0x7f080077
com.airdoc.mpd:color/m3_sys_color_dark_on_error = 0x7f06017a
com.airdoc.mpd:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0702df
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1300e0
com.airdoc.mpd:style/Widget.Material3.Button.Icon = 0x7f130378
com.airdoc.mpd:style/Widget.Material3.Snackbar.FullWidth = 0x7f1303f2
com.airdoc.mpd:dimen/mtrl_min_touch_target_size = 0x7f0702de
com.airdoc.mpd:style/Platform.V25.AppCompat = 0x7f130162
com.airdoc.mpd:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0702dc
com.airdoc.mpd:attr/cursorColor = 0x7f04016b
com.airdoc.mpd:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0702d4
com.airdoc.mpd:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0702d1
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1301e9
com.airdoc.mpd:dimen/mtrl_extended_fab_translation_z_base = 0x7f0702cf
com.airdoc.mpd:style/Theme.Material3.Light.SideSheetDialog = 0x7f13026b
com.airdoc.mpd:dimen/m3_badge_with_text_size = 0x7f0700dc
com.airdoc.mpd:dimen/mtrl_extended_fab_top_padding = 0x7f0702ce
com.airdoc.mpd:macro/m3_comp_snackbar_supporting_text_type = 0x7f0e0117
com.airdoc.mpd:attr/typeface = 0x7f040503
com.airdoc.mpd:attr/track = 0x7f0404ed
com.airdoc.mpd:dimen/mtrl_extended_fab_min_height = 0x7f0702ca
com.airdoc.mpd:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f13005d
com.airdoc.mpd:dimen/mtrl_extended_fab_icon_size = 0x7f0702c8
com.airdoc.mpd:attr/backgroundInsetStart = 0x7f040051
com.airdoc.mpd:string/exo_controls_time_placeholder = 0x7f12005b
com.airdoc.mpd:dimen/mtrl_extended_fab_elevation = 0x7f0702c5
com.airdoc.mpd:attr/time_bar_min_update_interval = 0x7f0404cc
com.airdoc.mpd:attr/colorTertiaryFixed = 0x7f040138
com.airdoc.mpd:id/text2 = 0x7f0a0261
com.airdoc.mpd:dimen/mtrl_extended_fab_bottom_padding = 0x7f0702c2
com.airdoc.mpd:macro/m3_comp_fab_tertiary_container_color = 0x7f0e0040
com.airdoc.mpd:attr/textAppearanceTitleSmall = 0x7f040495
com.airdoc.mpd:dimen/abc_control_corner_material = 0x7f070018
com.airdoc.mpd:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0702bf
com.airdoc.mpd:color/material_timepicker_clock_text_color = 0x7f0602bd
com.airdoc.mpd:dimen/mtrl_card_elevation = 0x7f0702bb
com.airdoc.mpd:dimen/mtrl_calendar_year_vertical_padding = 0x7f0702b5
com.airdoc.mpd:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f0702ad
com.airdoc.mpd:drawable/exo_legacy_controls_repeat_off = 0x7f0800e7
com.airdoc.mpd:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f0702aa
com.airdoc.mpd:id/position = 0x7f0a01ed
com.airdoc.mpd:dimen/mtrl_calendar_month_vertical_padding = 0x7f0702a6
com.airdoc.mpd:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f0702a4
com.airdoc.mpd:dimen/mtrl_calendar_landscape_header_width = 0x7f0702a3
com.airdoc.mpd:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f0702a1
com.airdoc.mpd:attr/subtitleCentered = 0x7f040440
com.airdoc.mpd:dimen/mtrl_calendar_header_selection_line_height = 0x7f07029f
com.airdoc.mpd:dimen/mtrl_calendar_header_height_fullscreen = 0x7f07029e
com.airdoc.mpd:id/search_close_btn = 0x7f0a0218
com.airdoc.mpd:dimen/m3_bottom_nav_min_height = 0x7f0700e4
com.airdoc.mpd:color/material_on_surface_emphasis_high_type = 0x7f06027e
com.airdoc.mpd:dimen/mtrl_calendar_header_height = 0x7f07029d
com.airdoc.mpd:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f130231
com.airdoc.mpd:style/Widget.Material3.Button.UnelevatedButton = 0x7f130387
com.airdoc.mpd:dimen/mtrl_calendar_header_divider_thickness = 0x7f07029c
com.airdoc.mpd:dimen/mtrl_calendar_header_content_padding = 0x7f07029a
com.airdoc.mpd:id/animateToStart = 0x7f0a0050
com.airdoc.mpd:color/bright_foreground_inverse_material_dark = 0x7f06002f
com.airdoc.mpd:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0e013e
com.airdoc.mpd:dimen/mtrl_high_ripple_default_alpha = 0x7f0702d6
com.airdoc.mpd:string/str_version_format = 0x7f12012f
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f080184
com.airdoc.mpd:dimen/mtrl_calendar_dialog_background_inset = 0x7f070299
com.airdoc.mpd:string/abc_search_hint = 0x7f120012
com.airdoc.mpd:drawable/abc_list_selector_background_transition_holo_light = 0x7f080054
com.airdoc.mpd:dimen/m3_btn_icon_only_default_padding = 0x7f0700f6
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f13027d
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f130024
com.airdoc.mpd:dimen/mtrl_calendar_days_of_week_height = 0x7f070298
com.airdoc.mpd:dimen/mtrl_calendar_day_width = 0x7f070297
com.airdoc.mpd:string/mtrl_picker_text_input_month_abbr = 0x7f1200d2
com.airdoc.mpd:dimen/m3_comp_badge_large_size = 0x7f070122
com.airdoc.mpd:dimen/mtrl_calendar_day_vertical_padding = 0x7f070296
com.airdoc.mpd:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080061
com.airdoc.mpd:dimen/m3_comp_search_view_docked_header_container_height = 0x7f070195
com.airdoc.mpd:dimen/mtrl_calendar_day_horizontal_padding = 0x7f070294
com.airdoc.mpd:color/material_personalized_primary_text_disable_only = 0x7f0602b4
com.airdoc.mpd:styleable/ViewBackgroundHelper = 0x7f14009f
com.airdoc.mpd:dimen/mtrl_calendar_action_padding = 0x7f07028f
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f130279
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Dialog = 0x7f130069
com.airdoc.mpd:dimen/m3_comp_outlined_text_field_outline_width = 0x7f07017e
com.airdoc.mpd:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f070150
com.airdoc.mpd:dimen/mtrl_calendar_action_height = 0x7f07028e
com.airdoc.mpd:color/material_personalized_color_on_tertiary_container = 0x7f060294
com.airdoc.mpd:color/m3_tabs_icon_color = 0x7f06020b
com.airdoc.mpd:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f07028d
com.airdoc.mpd:dimen/mtrl_btn_text_btn_icon_padding = 0x7f070288
com.airdoc.mpd:attr/dayInvalidStyle = 0x7f040178
com.airdoc.mpd:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f070286
com.airdoc.mpd:dimen/mtrl_btn_padding_top = 0x7f070284
com.airdoc.mpd:id/floating = 0x7f0a0119
com.airdoc.mpd:attr/layout_constraintRight_toRightOf = 0x7f0402a5
com.airdoc.mpd:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f130111
com.airdoc.mpd:dimen/mtrl_btn_padding_left = 0x7f070282
com.airdoc.mpd:dimen/mtrl_btn_padding_bottom = 0x7f070281
com.airdoc.mpd:dimen/mtrl_btn_letter_spacing = 0x7f07027f
com.airdoc.mpd:color/material_personalized_color_surface_container = 0x7f0602a2
com.airdoc.mpd:id/spherical_gl_surface_view = 0x7f0a0237
com.airdoc.mpd:style/ShapeAppearance.Material3.SmallComponent = 0x7f130199
com.airdoc.mpd:dimen/mtrl_btn_disabled_z = 0x7f070278
com.airdoc.mpd:dimen/mtrl_btn_disabled_elevation = 0x7f070277
com.airdoc.mpd:style/Widget.AppCompat.ListView = 0x7f130340
com.airdoc.mpd:attr/duration = 0x7f0401a4
com.airdoc.mpd:dimen/mtrl_btn_dialog_btn_min_width = 0x7f070276
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f130177
com.airdoc.mpd:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f070273
com.airdoc.mpd:attr/editTextStyle = 0x7f0401a8
com.airdoc.mpd:string/str_app_size_d = 0x7f1200f1
com.airdoc.mpd:attr/fontStyle = 0x7f040211
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1303da
com.airdoc.mpd:dimen/mtrl_progress_circular_inset_medium = 0x7f0702f2
com.airdoc.mpd:dimen/fastscroll_default_thickness = 0x7f0700b3
com.airdoc.mpd:layout/mtrl_calendar_days_of_week = 0x7f0d006d
com.airdoc.mpd:attr/nestedScrollFlags = 0x7f040378
com.airdoc.mpd:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f070272
com.airdoc.mpd:id/staticPostLayout = 0x7f0a0247
com.airdoc.mpd:color/m3_sys_color_dark_surface_variant = 0x7f060192
com.airdoc.mpd:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f070270
com.airdoc.mpd:color/material_dynamic_secondary40 = 0x7f060257
com.airdoc.mpd:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0e00af
com.airdoc.mpd:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0702cd
com.airdoc.mpd:dimen/mtrl_badge_with_text_size = 0x7f07026e
com.airdoc.mpd:dimen/m3_comp_navigation_bar_container_height = 0x7f07015e
com.airdoc.mpd:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f07026a
com.airdoc.mpd:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0e00a5
com.airdoc.mpd:layout/mtrl_picker_header_selection_text = 0x7f0d007d
com.airdoc.mpd:attr/windowFixedHeightMajor = 0x7f04051f
com.airdoc.mpd:attr/listPreferredItemPaddingEnd = 0x7f0402d8
com.airdoc.mpd:color/m3_sys_color_dark_secondary = 0x7f060188
com.airdoc.mpd:dimen/mtrl_alert_dialog_background_inset_top = 0x7f070265
com.airdoc.mpd:dimen/mtrl_alert_dialog_background_inset_start = 0x7f070264
com.airdoc.mpd:dimen/material_timepicker_dialog_buttons_margin_top = 0x7f070261
com.airdoc.mpd:layout/abc_activity_chooser_view_list_item = 0x7f0d0007
com.airdoc.mpd:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0c0003
com.airdoc.mpd:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f070259
com.airdoc.mpd:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f070257
com.airdoc.mpd:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f070255
com.airdoc.mpd:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f130465
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f130188
com.airdoc.mpd:dimen/material_emphasis_medium = 0x7f070251
com.airdoc.mpd:id/tv_open_date = 0x7f0a0298
com.airdoc.mpd:dimen/material_emphasis_disabled = 0x7f07024e
com.airdoc.mpd:dimen/material_divider_thickness = 0x7f07024d
com.airdoc.mpd:attr/colorSurfaceDim = 0x7f040132
com.airdoc.mpd:dimen/m3_navigation_rail_item_min_height = 0x7f0701eb
com.airdoc.mpd:drawable/abc_text_select_handle_right_mtrl = 0x7f080071
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Menu = 0x7f130028
com.airdoc.mpd:dimen/material_cursor_width = 0x7f07024c
com.airdoc.mpd:attr/textBackgroundRotate = 0x7f040499
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.RepeatToggle = 0x7f130134
com.airdoc.mpd:dimen/material_cursor_inset = 0x7f07024b
com.airdoc.mpd:color/material_personalized_color_error_container = 0x7f060288
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0601af
com.airdoc.mpd:color/error_color_material_light = 0x7f06007a
com.airdoc.mpd:drawable/abc_btn_colored_material = 0x7f080030
com.airdoc.mpd:dimen/material_clock_hand_padding = 0x7f070243
com.airdoc.mpd:dimen/material_clock_hand_center_dot_radius = 0x7f070242
com.airdoc.mpd:styleable/AppCompatTextView = 0x7f140011
com.airdoc.mpd:dimen/material_clock_display_width = 0x7f070240
com.airdoc.mpd:style/ThemeOverlay.Design.TextInputEditText = 0x7f1302a5
com.airdoc.mpd:attr/colorPrimary = 0x7f04011d
com.airdoc.mpd:macro/m3_comp_time_picker_clock_dial_color = 0x7f0e014d
com.airdoc.mpd:dimen/material_bottom_sheet_max_width = 0x7f07023d
com.airdoc.mpd:attr/drawableSize = 0x7f040199
com.airdoc.mpd:drawable/common_hollow_button_enable_bg = 0x7f0800a8
com.airdoc.mpd:dimen/m3_toolbar_text_size_title = 0x7f07023c
com.airdoc.mpd:style/TextAppearance.Material3.ActionBar.Title = 0x7f13020c
com.airdoc.mpd:attr/itemIconTint = 0x7f04025b
com.airdoc.mpd:dimen/m3_timepicker_window_elevation = 0x7f07023b
com.airdoc.mpd:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f08007c
com.airdoc.mpd:attr/materialCalendarStyle = 0x7f04030e
com.airdoc.mpd:attr/itemShapeAppearanceOverlay = 0x7f040263
com.airdoc.mpd:attr/floatingActionButtonSurfaceStyle = 0x7f0401f3
com.airdoc.mpd:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f070237
com.airdoc.mpd:string/exo_track_selection_none = 0x7f12006f
com.airdoc.mpd:attr/counterMaxLength = 0x7f040164
com.airdoc.mpd:color/m3_sys_color_dark_tertiary = 0x7f060193
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f070233
com.airdoc.mpd:attr/removeEmbeddedFabElevation = 0x7f0403c8
com.airdoc.mpd:color/m3_ref_palette_neutral98 = 0x7f060134
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f070232
com.airdoc.mpd:dimen/mtrl_btn_hovered_z = 0x7f07027b
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f13046b
com.airdoc.mpd:attr/triggerId = 0x7f0404ff
com.airdoc.mpd:color/m3_ref_palette_neutral24 = 0x7f060125
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f07022a
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f070230
com.airdoc.mpd:anim/abc_fade_in = 0x7f010000
com.airdoc.mpd:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0e014b
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f07021b
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f07022f
com.airdoc.mpd:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1300f8
com.airdoc.mpd:attr/iconStartPadding = 0x7f040239
com.airdoc.mpd:id/accessibility_custom_action_6 = 0x7f0a002c
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f07022b
com.airdoc.mpd:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0702ee
com.airdoc.mpd:color/m3_ref_palette_neutral_variant60 = 0x7f06013d
com.airdoc.mpd:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f070229
com.airdoc.mpd:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f070226
com.airdoc.mpd:dimen/design_navigation_separator_vertical_padding = 0x7f07007d
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f07021e
com.airdoc.mpd:drawable/selector_read_init_tab_bg = 0x7f0801be
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f07021d
com.airdoc.mpd:attr/lottie_renderMode = 0x7f0402ee
com.airdoc.mpd:drawable/common_d7dce9_round_bg = 0x7f08008a
com.airdoc.mpd:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0700c7
com.airdoc.mpd:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f070120
com.airdoc.mpd:attr/layout_collapseParallaxMultiplier = 0x7f040285
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f07021c
com.airdoc.mpd:attr/elevationOverlayEnabled = 0x7f0401ac
com.airdoc.mpd:attr/materialTimePickerTheme = 0x7f040326
com.airdoc.mpd:color/m3_sys_color_light_surface_variant = 0x7f0601fc
com.airdoc.mpd:attr/autoSizeMaxTextSize = 0x7f040044
com.airdoc.mpd:color/material_dynamic_neutral_variant10 = 0x7f060239
com.airdoc.mpd:dimen/abc_dialog_padding_material = 0x7f070024
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f070219
com.airdoc.mpd:attr/itemRippleColor = 0x7f040261
com.airdoc.mpd:attr/dialogTheme = 0x7f040187
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f070218
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0e00e2
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f070215
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f070212
com.airdoc.mpd:color/material_personalized_color_on_background = 0x7f060289
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0601bb
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f130423
com.airdoc.mpd:color/m3_sys_color_light_surface_container_high = 0x7f0601f7
com.airdoc.mpd:id/circle_center = 0x7f0a007b
com.airdoc.mpd:dimen/m3_sys_elevation_level5 = 0x7f070211
com.airdoc.mpd:id/mtrl_anchor_parent = 0x7f0a019f
com.airdoc.mpd:color/m3_sys_color_light_on_surface_variant = 0x7f0601eb
com.airdoc.mpd:dimen/m3_sys_elevation_level3 = 0x7f07020f
com.airdoc.mpd:dimen/m3_snackbar_margin = 0x7f07020b
com.airdoc.mpd:integer/exo_media_button_opacity_percentage_disabled = 0x7f0b0008
com.airdoc.mpd:attr/forceDefaultNavigationOnClickListener = 0x7f040215
com.airdoc.mpd:dimen/m3_simple_item_color_hovered_alpha = 0x7f070204
com.airdoc.mpd:string/str_data_cache = 0x7f1200fb
com.airdoc.mpd:attr/textSize = 0x7f0404ad
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f070180
com.airdoc.mpd:dimen/m3_comp_outlined_card_icon_size = 0x7f070177
com.airdoc.mpd:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f13009e
com.airdoc.mpd:attr/layout_constraintTop_creator = 0x7f0402a9
com.airdoc.mpd:dimen/m3_side_sheet_margin_detached = 0x7f070200
com.airdoc.mpd:attr/submitBackground = 0x7f04043e
com.airdoc.mpd:string/mtrl_switch_thumb_group_name = 0x7f1200d9
com.airdoc.mpd:dimen/m3_searchview_elevation = 0x7f0701fe
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f130308
com.airdoc.mpd:styleable/SwitchMaterial = 0x7f140090
com.airdoc.mpd:styleable/NavigationBarView = 0x7f140070
com.airdoc.mpd:attr/viewTransitionOnCross = 0x7f040510
com.airdoc.mpd:dimen/m3_datepicker_elevation = 0x7f0701ca
com.airdoc.mpd:dimen/m3_searchbar_padding_start = 0x7f0701fa
com.airdoc.mpd:drawable/$m3_avd_show_password__2 = 0x7f08000c
com.airdoc.mpd:color/material_slider_halo_color = 0x7f0602b7
com.airdoc.mpd:id/actionDownUp = 0x7f0a0032
com.airdoc.mpd:color/m3_ref_palette_error90 = 0x7f06011b
com.airdoc.mpd:dimen/m3_searchbar_outlined_stroke_width = 0x7f0701f9
com.airdoc.mpd:dimen/m3_searchbar_height = 0x7f0701f6
com.airdoc.mpd:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f07030f
com.airdoc.mpd:dimen/m3_btn_inset = 0x7f0700fa
com.airdoc.mpd:dimen/m3_searchbar_elevation = 0x7f0701f5
com.airdoc.mpd:dimen/m3_comp_fab_primary_small_icon_size = 0x7f070144
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f130071
com.airdoc.mpd:dimen/m3_ripple_hovered_alpha = 0x7f0701f2
com.airdoc.mpd:styleable/GradientColor = 0x7f14003e
com.airdoc.mpd:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0701ef
com.airdoc.mpd:dimen/m3_side_sheet_width = 0x7f070203
com.airdoc.mpd:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0701ed
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f13007c
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary100 = 0x7f060106
com.airdoc.mpd:string/str_invalid_parameters_required_calibration = 0x7f120111
com.airdoc.mpd:style/Base.V22.Theme.AppCompat.Light = 0x7f1300af
com.airdoc.mpd:style/Base.V23.Theme.AppCompat = 0x7f1300b0
com.airdoc.mpd:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0701ea
com.airdoc.mpd:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0701e8
com.airdoc.mpd:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f0702ae
com.airdoc.mpd:dimen/m3_navigation_rail_icon_size = 0x7f0701e7
com.airdoc.mpd:id/SYM = 0x7f0a000b
com.airdoc.mpd:dimen/m3_navigation_rail_elevation = 0x7f0701e6
com.airdoc.mpd:dimen/m3_navigation_rail_default_width = 0x7f0701e5
com.airdoc.mpd:attr/fontProviderSystemFontFamily = 0x7f040210
com.airdoc.mpd:dimen/mtrl_textinput_counter_margin_start = 0x7f07031c
com.airdoc.mpd:id/fixed_height = 0x7f0a0114
com.airdoc.mpd:style/Theme.Material3.Light.Dialog = 0x7f130266
com.airdoc.mpd:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0701e3
com.airdoc.mpd:drawable/common_google_signin_btn_icon_dark_focused = 0x7f080093
com.airdoc.mpd:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0601d3
com.airdoc.mpd:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f07026c
com.airdoc.mpd:attr/logoScaleType = 0x7f0402df
com.airdoc.mpd:dimen/m3_navigation_item_shape_inset_start = 0x7f0701e0
com.airdoc.mpd:dimen/m3_snackbar_action_text_color_alpha = 0x7f07020a
com.airdoc.mpd:attr/tabStyle = 0x7f040466
com.airdoc.mpd:dimen/m3_navigation_item_shape_inset_end = 0x7f0701df
com.airdoc.mpd:dimen/m3_navigation_item_icon_padding = 0x7f0701dd
com.airdoc.mpd:style/TextAppearance.AppCompat.Inverse = 0x7f1301c1
com.airdoc.mpd:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0701db
com.airdoc.mpd:attr/navigationViewStyle = 0x7f040377
com.airdoc.mpd:attr/largeFontVerticalOffsetAdjustment = 0x7f04027a
com.airdoc.mpd:dimen/m3_comp_input_chip_container_height = 0x7f070155
com.airdoc.mpd:animator/mtrl_card_state_list_anim = 0x7f020017
com.airdoc.mpd:dimen/m3_menu_elevation = 0x7f0701d9
com.airdoc.mpd:color/black_60 = 0x7f060029
com.airdoc.mpd:dimen/m3_large_fab_size = 0x7f0701d7
com.airdoc.mpd:attr/layout_keyline = 0x7f0402bf
com.airdoc.mpd:id/textinput_helper_text = 0x7f0a026c
com.airdoc.mpd:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.airdoc.mpd:drawable/$m3_avd_hide_password__2 = 0x7f080009
com.airdoc.mpd:dimen/m3_large_fab_max_image_size = 0x7f0701d6
com.airdoc.mpd:attr/roundProgressColor = 0x7f0403d1
com.airdoc.mpd:dimen/m3_fab_translation_z_pressed = 0x7f0701d5
com.airdoc.mpd:attr/startIconCheckable = 0x7f040424
com.airdoc.mpd:id/action_mode_bar = 0x7f0a0041
com.airdoc.mpd:dimen/m3_extended_fab_top_padding = 0x7f0701d1
com.airdoc.mpd:dimen/m3_navigation_item_horizontal_padding = 0x7f0701dc
com.airdoc.mpd:dimen/m3_extended_fab_start_padding = 0x7f0701d0
com.airdoc.mpd:id/special_effects_controller_view_tag = 0x7f0a0236
com.airdoc.mpd:drawable/common_google_signin_btn_icon_disabled = 0x7f080096
com.airdoc.mpd:dimen/m3_extended_fab_min_height = 0x7f0701cf
com.airdoc.mpd:dimen/m3_extended_fab_icon_padding = 0x7f0701ce
com.airdoc.mpd:dimen/m3_extended_fab_end_padding = 0x7f0701cd
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f130284
com.airdoc.mpd:dimen/m3_divider_heavy_thickness = 0x7f0701cb
com.airdoc.mpd:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0701c8
com.airdoc.mpd:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0701c6
com.airdoc.mpd:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0701c3
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f13002f
com.airdoc.mpd:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0701be
com.airdoc.mpd:dimen/m3_comp_time_picker_container_elevation = 0x7f0701bd
com.airdoc.mpd:attr/tabSecondaryStyle = 0x7f040463
com.airdoc.mpd:string/abc_menu_enter_shortcut_label = 0x7f12000b
com.airdoc.mpd:string/exo_controls_overflow_hide_description = 0x7f12004b
com.airdoc.mpd:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f07018a
com.airdoc.mpd:integer/m3_sys_motion_duration_short2 = 0x7f0b001f
com.airdoc.mpd:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f0701b8
com.airdoc.mpd:drawable/exo_legacy_controls_pause = 0x7f0800e3
com.airdoc.mpd:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f0701b6
com.airdoc.mpd:attr/colorAccent = 0x7f0400fd
com.airdoc.mpd:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f13040e
com.airdoc.mpd:color/m3_ref_palette_neutral_variant50 = 0x7f06013c
com.airdoc.mpd:style/ExoStyledControls = 0x7f13012b
com.airdoc.mpd:color/m3_ref_palette_primary90 = 0x7f06014d
com.airdoc.mpd:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f0701b2
com.airdoc.mpd:macro/m3_comp_slider_disabled_active_track_color = 0x7f0e010d
com.airdoc.mpd:dimen/design_bottom_navigation_active_text_size = 0x7f070061
com.airdoc.mpd:attr/layout_constraintVertical_chainStyle = 0x7f0402ad
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral90 = 0x7f0600d6
com.airdoc.mpd:id/mtrl_child_content_container = 0x7f0a01a9
com.airdoc.mpd:color/material_dynamic_neutral_variant50 = 0x7f06023e
com.airdoc.mpd:attr/textColorAlertDialogListItem = 0x7f04049c
com.airdoc.mpd:attr/tabIndicatorFullWidth = 0x7f040456
com.airdoc.mpd:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f0701b0
com.airdoc.mpd:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f0701af
com.airdoc.mpd:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f130431
com.airdoc.mpd:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0401f0
com.airdoc.mpd:color/black_50 = 0x7f060028
com.airdoc.mpd:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f13045f
com.airdoc.mpd:attr/snackbarTextViewStyle = 0x7f040416
com.airdoc.mpd:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f0701a8
com.airdoc.mpd:style/Theme.Material3.DayNight.Dialog = 0x7f13025b
com.airdoc.mpd:id/fillCenter = 0x7f0a0107
com.airdoc.mpd:dimen/m3_comp_suggestion_chip_container_height = 0x7f0701a7
com.airdoc.mpd:style/Widget.AppCompat.Toolbar = 0x7f130355
com.airdoc.mpd:attr/backgroundOverlayColorAlpha = 0x7f040053
com.airdoc.mpd:dimen/m3_comp_snackbar_container_elevation = 0x7f0701a6
com.airdoc.mpd:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f0701a2
com.airdoc.mpd:attr/buttonBarButtonStyle = 0x7f040093
com.airdoc.mpd:color/material_dynamic_secondary90 = 0x7f06025c
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary90 = 0x7f06010e
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Title = 0x7f1301ef
com.airdoc.mpd:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f07019d
com.airdoc.mpd:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f07019c
com.airdoc.mpd:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f07019a
com.airdoc.mpd:attr/hintAnimationEnabled = 0x7f04022b
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0e015d
com.airdoc.mpd:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f130481
com.airdoc.mpd:dimen/m3_comp_search_view_container_elevation = 0x7f070194
com.airdoc.mpd:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f040358
com.airdoc.mpd:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0e011d
com.airdoc.mpd:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f08001e
com.airdoc.mpd:dimen/m3_comp_search_bar_container_height = 0x7f070191
com.airdoc.mpd:dimen/m3_comp_search_bar_container_elevation = 0x7f070190
com.airdoc.mpd:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f07018d
com.airdoc.mpd:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f070189
com.airdoc.mpd:string/abc_action_menu_overflow_description = 0x7f120002
com.airdoc.mpd:id/loading_text = 0x7f0a0176
com.airdoc.mpd:dimen/abc_list_item_height_material = 0x7f070031
com.airdoc.mpd:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f070188
com.airdoc.mpd:styleable/ClockFaceView = 0x7f140022
com.airdoc.mpd:dimen/abc_text_size_display_4_material = 0x7f070046
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f130450
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f130473
com.airdoc.mpd:attr/prefixText = 0x7f0403ac
com.airdoc.mpd:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f070187
com.airdoc.mpd:dimen/m3_comp_outlined_card_outline_width = 0x7f070178
com.airdoc.mpd:attr/trackThickness = 0x7f0404f6
com.airdoc.mpd:id/bounceEnd = 0x7f0a0065
com.airdoc.mpd:dimen/m3_comp_outlined_card_container_elevation = 0x7f070175
com.airdoc.mpd:dimen/abc_config_prefDialogWidth = 0x7f070017
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f06019b
com.airdoc.mpd:attr/homeLayout = 0x7f040230
com.airdoc.mpd:attr/navigationIcon = 0x7f040373
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f130200
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary30 = 0x7f0600fb
com.airdoc.mpd:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f0701a1
com.airdoc.mpd:style/Widget.Material3.SideSheet.Modal = 0x7f1303ed
com.airdoc.mpd:id/tv_mqtt_state = 0x7f0a0296
com.airdoc.mpd:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f13041e
com.airdoc.mpd:color/m3_ref_palette_primary70 = 0x7f06014b
com.airdoc.mpd:attr/carousel_touchUp_dampeningFactor = 0x7f0400b4
com.airdoc.mpd:dimen/m3_comp_navigation_rail_container_elevation = 0x7f07016c
com.airdoc.mpd:string/exo_controls_rewind_description = 0x7f120054
com.airdoc.mpd:attr/textAppearanceLineHeightEnabled = 0x7f040488
com.airdoc.mpd:dimen/mtrl_fab_min_touch_target = 0x7f0702d3
com.airdoc.mpd:id/tv_i_know = 0x7f0a0292
com.airdoc.mpd:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f070173
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f070221
com.airdoc.mpd:id/restart = 0x7f0a01fd
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f070167
com.airdoc.mpd:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010015
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_icon_size = 0x7f070166
com.airdoc.mpd:attr/subheaderTextAppearance = 0x7f04043d
com.airdoc.mpd:dimen/m3_comp_outlined_button_outline_width = 0x7f070174
com.airdoc.mpd:style/TextAppearance.AppCompat.Menu = 0x7f1301ca
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f070165
com.airdoc.mpd:macro/m3_comp_slider_disabled_handle_color = 0x7f0e010e
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_container_width = 0x7f070163
com.airdoc.mpd:integer/m3_sys_motion_duration_medium4 = 0x7f0b001d
com.airdoc.mpd:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f070162
com.airdoc.mpd:layout/mtrl_calendar_vertical = 0x7f0d0073
com.airdoc.mpd:id/iv_more_settings = 0x7f0a014d
com.airdoc.mpd:attr/thumbTintMode = 0x7f0404c2
com.airdoc.mpd:color/material_grey_50 = 0x7f06026e
com.airdoc.mpd:styleable/TextInputEditText = 0x7f140095
com.airdoc.mpd:style/Widget.Material3.Chip.Input.Icon = 0x7f130392
com.airdoc.mpd:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f07015f
com.airdoc.mpd:attr/backgroundInsetEnd = 0x7f040050
com.airdoc.mpd:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f13010d
com.airdoc.mpd:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0e0022
com.airdoc.mpd:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f07015c
com.airdoc.mpd:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f07015b
com.airdoc.mpd:id/accessibility_custom_action_1 = 0x7f0a0011
com.airdoc.mpd:attr/materialButtonOutlinedStyle = 0x7f0402ff
com.airdoc.mpd:style/Widget.AppCompat.AutoCompleteTextView = 0x7f130318
com.airdoc.mpd:dimen/m3_comp_linear_progress_indicator_active_indicator_height = 0x7f070159
com.airdoc.mpd:string/m3_ref_typeface_plain_regular = 0x7f120083
com.airdoc.mpd:dimen/abc_list_item_height_small_material = 0x7f070032
com.airdoc.mpd:id/surface_view = 0x7f0a024e
com.airdoc.mpd:dimen/m3_comp_input_chip_container_elevation = 0x7f070154
com.airdoc.mpd:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f070153
com.airdoc.mpd:styleable/MenuItem = 0x7f140065
com.airdoc.mpd:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0c000c
com.airdoc.mpd:bool/mtrl_btn_textappearance_all_caps = 0x7f050005
com.airdoc.mpd:dimen/material_time_picker_minimum_screen_height = 0x7f07025f
com.airdoc.mpd:style/ExoMediaButton = 0x7f130123
com.airdoc.mpd:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f070151
com.airdoc.mpd:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1302c6
com.airdoc.mpd:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f07014d
com.airdoc.mpd:attr/helperTextEnabled = 0x7f040221
com.airdoc.mpd:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f130393
com.airdoc.mpd:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f07014b
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f13044e
com.airdoc.mpd:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f07014a
com.airdoc.mpd:style/Widget.Material3.BottomSheet.DragHandle = 0x7f130373
com.airdoc.mpd:drawable/btn_radio_on_mtrl = 0x7f080081
com.airdoc.mpd:attr/flow_lastVerticalStyle = 0x7f040200
com.airdoc.mpd:style/Widget.Material3.BottomAppBar.Legacy = 0x7f13036f
com.airdoc.mpd:dimen/design_fab_translation_z_hovered_focused = 0x7f070073
com.airdoc.mpd:attr/cornerRadius = 0x7f04015d
com.airdoc.mpd:dimen/m3_chip_disabled_translation_z = 0x7f070118
com.airdoc.mpd:attr/animateCircleAngleTo = 0x7f040033
com.airdoc.mpd:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f0702b1
com.airdoc.mpd:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f070147
com.airdoc.mpd:dimen/exo_media_button_width = 0x7f07009b
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f130099
com.airdoc.mpd:dimen/m3_comp_fab_primary_large_icon_size = 0x7f070140
com.airdoc.mpd:id/vertical_only = 0x7f0a02b0
com.airdoc.mpd:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1302b5
com.airdoc.mpd:id/ll_language = 0x7f0a016f
com.airdoc.mpd:attr/gestureInsetBottomIgnored = 0x7f040219
com.airdoc.mpd:raw/whatsapp = 0x7f110016
com.airdoc.mpd:attr/activityChooserViewStyle = 0x7f040027
com.airdoc.mpd:color/m3_ref_palette_neutral6 = 0x7f06012a
com.airdoc.mpd:dimen/m3_comp_fab_primary_large_container_height = 0x7f07013f
com.airdoc.mpd:color/m3_chip_ripple_color = 0x7f060099
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.Shuffle = 0x7f130136
com.airdoc.mpd:dimen/m3_comp_sheet_side_docked_container_width = 0x7f07019f
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f070136
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f070135
com.airdoc.mpd:style/shape_image_round_20dp = 0x7f130488
com.airdoc.mpd:attr/methodName = 0x7f040337
com.airdoc.mpd:attr/shapeAppearanceCornerExtraSmall = 0x7f0403eb
com.airdoc.mpd:attr/coordinatorLayoutStyle = 0x7f040156
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_container_height = 0x7f070131
com.airdoc.mpd:dimen/m3_comp_elevated_card_container_elevation = 0x7f07012e
com.airdoc.mpd:dimen/mtrl_calendar_bottom_padding = 0x7f070290
com.airdoc.mpd:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f07012d
com.airdoc.mpd:dimen/m3_comp_circular_progress_indicator_active_indicator_width = 0x7f070127
com.airdoc.mpd:attr/buttonBarNeutralButtonStyle = 0x7f040095
com.airdoc.mpd:dimen/m3_sys_elevation_level0 = 0x7f07020c
com.airdoc.mpd:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f070121
com.airdoc.mpd:attr/emojiCompatEnabled = 0x7f0401ad
com.airdoc.mpd:string/m3_exceed_max_badge_text_suffix = 0x7f12007f
com.airdoc.mpd:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f07011f
com.airdoc.mpd:attr/motionEffect_move = 0x7f040362
com.airdoc.mpd:dimen/notification_small_icon_size_as_large = 0x7f070332
com.airdoc.mpd:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f07011e
com.airdoc.mpd:dimen/m3_comp_assist_chip_container_height = 0x7f07011d
com.airdoc.mpd:id/ll_more_settings = 0x7f0a0171
com.airdoc.mpd:layout/material_clock_display = 0x7f0d0057
com.airdoc.mpd:attr/lottie_colorFilter = 0x7f0402e4
com.airdoc.mpd:id/tv_fingertip_collection = 0x7f0a0291
com.airdoc.mpd:color/color_333333 = 0x7f06003b
com.airdoc.mpd:dimen/m3_chip_corner_size = 0x7f070117
com.airdoc.mpd:dimen/m3_chip_checked_hovered_translation_z = 0x7f070116
com.airdoc.mpd:drawable/ic_main_bg = 0x7f080132
com.airdoc.mpd:dimen/m3_card_elevated_elevation = 0x7f07010b
com.airdoc.mpd:dimen/material_time_picker_minimum_screen_width = 0x7f070260
com.airdoc.mpd:macro/m3_comp_navigation_drawer_headline_color = 0x7f0e0087
com.airdoc.mpd:color/m3_ref_palette_neutral80 = 0x7f06012d
com.airdoc.mpd:attr/paddingEnd = 0x7f040389
com.airdoc.mpd:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080060
com.airdoc.mpd:dimen/m3_card_elevated_dragged_z = 0x7f07010a
com.airdoc.mpd:layout/abc_action_menu_layout = 0x7f0d0003
com.airdoc.mpd:color/material_dynamic_tertiary0 = 0x7f06025f
com.airdoc.mpd:dimen/m3_card_elevated_disabled_z = 0x7f070109
com.airdoc.mpd:attr/layout_goneMarginTop = 0x7f0402bd
com.airdoc.mpd:id/blocking = 0x7f0a0060
com.airdoc.mpd:color/m3_ref_palette_secondary100 = 0x7f060152
com.airdoc.mpd:attr/shapeAppearance = 0x7f0403e9
com.airdoc.mpd:dimen/m3_card_dragged_z = 0x7f070108
com.airdoc.mpd:attr/actionMenuTextAppearance = 0x7f040010
com.airdoc.mpd:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f13023b
com.airdoc.mpd:dimen/m3_card_disabled_z = 0x7f070107
com.airdoc.mpd:dimen/m3_btn_translation_z_hovered = 0x7f070106
com.airdoc.mpd:string/material_timepicker_hour = 0x7f1200a0
com.airdoc.mpd:dimen/m3_btn_text_btn_padding_left = 0x7f070103
com.airdoc.mpd:id/wb_detection = 0x7f0a02bc
com.airdoc.mpd:dimen/m3_btn_text_btn_icon_padding_left = 0x7f070101
com.airdoc.mpd:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0e0056
com.airdoc.mpd:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f07016f
com.airdoc.mpd:dimen/m3_btn_padding_top = 0x7f0700ff
com.airdoc.mpd:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f13037e
com.airdoc.mpd:attr/cardPreventCornerOverlap = 0x7f0400a9
com.airdoc.mpd:dimen/m3_btn_padding_bottom = 0x7f0700fc
com.airdoc.mpd:attr/chipMinTouchTargetSize = 0x7f0400d1
com.airdoc.mpd:style/Theme.AppCompat.Light = 0x7f130242
com.airdoc.mpd:string/str_customer_service_hotline = 0x7f1200fa
com.airdoc.mpd:id/mtrl_picker_header_selection_text = 0x7f0a01ae
com.airdoc.mpd:dimen/mtrl_slider_widget_height = 0x7f07030a
com.airdoc.mpd:style/Platform.Widget.AppCompat.Spinner = 0x7f130164
com.airdoc.mpd:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.airdoc.mpd:dimen/m3_btn_max_width = 0x7f0700fb
com.airdoc.mpd:drawable/exo_ic_skip_previous = 0x7f0800cb
com.airdoc.mpd:dimen/m3_btn_icon_only_icon_padding = 0x7f0700f8
com.airdoc.mpd:style/Widget.Material3.PopupMenu.Overflow = 0x7f1303e3
com.airdoc.mpd:style/Widget.AppCompat.RatingBar.Indicator = 0x7f130349
com.airdoc.mpd:id/tv_user_gender = 0x7f0a02a4
com.airdoc.mpd:dimen/m3_btn_icon_btn_padding_right = 0x7f0700f5
com.airdoc.mpd:macro/m3_comp_filled_icon_button_container_color = 0x7f0e0049
com.airdoc.mpd:dimen/mtrl_card_checked_icon_margin = 0x7f0702b7
com.airdoc.mpd:color/m3_ref_palette_primary50 = 0x7f060149
com.airdoc.mpd:dimen/m3_btn_icon_btn_padding_left = 0x7f0700f4
com.airdoc.mpd:integer/m3_sys_motion_duration_extra_long3 = 0x7f0b0014
com.airdoc.mpd:attr/showAnimationBehavior = 0x7f0403f6
com.airdoc.mpd:styleable/RangeSlider = 0x7f14007c
com.airdoc.mpd:dimen/m3_btn_elevated_btn_elevation = 0x7f0700f2
com.airdoc.mpd:attr/actionBarWidgetTheme = 0x7f04000c
com.airdoc.mpd:dimen/mtrl_tooltip_minWidth = 0x7f070324
com.airdoc.mpd:style/Base.Widget.Material3.CardView = 0x7f130101
com.airdoc.mpd:dimen/m3_bottomappbar_fab_end_margin = 0x7f0700eb
com.airdoc.mpd:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.airdoc.mpd:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0700e9
com.airdoc.mpd:attr/played_ad_marker_color = 0x7f0403a4
com.airdoc.mpd:color/material_personalized_color_control_normal = 0x7f060286
com.airdoc.mpd:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0700e8
com.airdoc.mpd:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0700e5
com.airdoc.mpd:string/bottom_sheet_behavior = 0x7f12001e
com.airdoc.mpd:attr/startIconTintMode = 0x7f04042a
com.airdoc.mpd:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0e00b4
com.airdoc.mpd:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0700e1
com.airdoc.mpd:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f07016e
com.airdoc.mpd:attr/listPreferredItemHeightSmall = 0x7f0402d7
com.airdoc.mpd:dimen/design_bottom_navigation_shadow_height = 0x7f070069
com.airdoc.mpd:color/m3_sys_color_light_on_secondary_container = 0x7f0601e9
com.airdoc.mpd:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0700e0
com.airdoc.mpd:attr/layout_constraintVertical_bias = 0x7f0402ac
com.airdoc.mpd:dimen/notification_right_icon_size = 0x7f07032f
com.airdoc.mpd:attr/chipSpacingHorizontal = 0x7f0400d3
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1303d4
com.airdoc.mpd:id/customPanel = 0x7f0a009f
com.airdoc.mpd:attr/quantizeMotionPhase = 0x7f0403b5
com.airdoc.mpd:attr/onCross = 0x7f04037e
com.airdoc.mpd:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0700df
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1303cb
com.airdoc.mpd:layout/abc_screen_simple = 0x7f0d0015
com.airdoc.mpd:dimen/m3_badge_with_text_vertical_padding = 0x7f0700de
com.airdoc.mpd:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1302c8
com.airdoc.mpd:layout/mtrl_alert_select_dialog_item = 0x7f0d0067
com.airdoc.mpd:id/title_template = 0x7f0a0274
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1302d2
com.airdoc.mpd:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.airdoc.mpd:attr/sliderStyle = 0x7f040413
com.airdoc.mpd:dimen/m3_badge_with_text_horizontal_offset = 0x7f0700da
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f070213
com.airdoc.mpd:color/mtrl_error = 0x7f0602d3
com.airdoc.mpd:id/textinput_prefix_text = 0x7f0a026e
com.airdoc.mpd:layout/notification_template_part_chronometer = 0x7f0d0091
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0e0092
com.airdoc.mpd:string/str_device_info = 0x7f120100
com.airdoc.mpd:attr/horizontalOffset = 0x7f040231
com.airdoc.mpd:attr/materialCalendarHeaderCancelButton = 0x7f040305
com.airdoc.mpd:dimen/m3_badge_horizontal_offset = 0x7f0700d6
com.airdoc.mpd:dimen/m3_appbar_size_large = 0x7f0700cd
com.airdoc.mpd:drawable/exo_styled_controls_fullscreen_exit = 0x7f0800fa
com.airdoc.mpd:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0700cb
com.airdoc.mpd:string/abc_action_bar_up_description = 0x7f120001
com.airdoc.mpd:attr/cornerFamilyTopRight = 0x7f04015c
com.airdoc.mpd:string/error_icon_content_description = 0x7f120042
com.airdoc.mpd:id/autoCompleteToEnd = 0x7f0a0058
com.airdoc.mpd:id/exo_error_message = 0x7f0a00df
com.airdoc.mpd:dimen/mtrl_textinput_box_corner_radius_small = 0x7f070318
com.airdoc.mpd:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f0701ab
com.airdoc.mpd:attr/itemTextAppearanceActive = 0x7f04026d
com.airdoc.mpd:color/m3_ref_palette_error20 = 0x7f060114
com.airdoc.mpd:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0700c6
com.airdoc.mpd:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f130377
com.airdoc.mpd:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1200d0
com.airdoc.mpd:dimen/m3_alert_dialog_icon_size = 0x7f0700c5
com.airdoc.mpd:dimen/tooltip_precise_anchor_extra_offset = 0x7f070339
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f130041
com.airdoc.mpd:color/m3_sys_color_light_on_error_container = 0x7f0601e5
com.airdoc.mpd:dimen/m3_alert_dialog_elevation = 0x7f0700c3
com.airdoc.mpd:attr/shapeAppearanceMediumComponent = 0x7f0403f0
com.airdoc.mpd:dimen/mtrl_calendar_header_text_padding = 0x7f0702a0
com.airdoc.mpd:dimen/material_clock_period_toggle_width = 0x7f070249
com.airdoc.mpd:dimen/m3_alert_dialog_corner_size = 0x7f0700c2
com.airdoc.mpd:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1302cc
com.airdoc.mpd:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0701c0
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline5 = 0x7f13022a
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Body1 = 0x7f130221
com.airdoc.mpd:attr/textInputOutlinedStyle = 0x7f0404a6
com.airdoc.mpd:string/str_number_available_times_0 = 0x7f120118
com.airdoc.mpd:color/m3_sys_color_primary_fixed = 0x7f060205
com.airdoc.mpd:attr/panelMenuListWidth = 0x7f040392
com.airdoc.mpd:attr/allowStacking = 0x7f04002f
com.airdoc.mpd:id/tag_accessibility_actions = 0x7f0a0253
com.airdoc.mpd:attr/textInputStyle = 0x7f0404a7
com.airdoc.mpd:color/material_personalized_color_on_secondary_container = 0x7f06028f
com.airdoc.mpd:dimen/hint_alpha_material_light = 0x7f0700ba
com.airdoc.mpd:attr/tickRadiusInactive = 0x7f0404ca
com.airdoc.mpd:string/str_get_download_resource_exception = 0x7f12010b
com.airdoc.mpd:dimen/design_fab_elevation = 0x7f07006f
com.airdoc.mpd:id/iv_view_report = 0x7f0a015a
com.airdoc.mpd:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0702d8
com.airdoc.mpd:dimen/highlight_alpha_material_colored = 0x7f0700b6
com.airdoc.mpd:dimen/fastscroll_margin = 0x7f0700b4
com.airdoc.mpd:attr/snackbarButtonStyle = 0x7f040414
com.airdoc.mpd:dimen/exo_styled_progress_touch_target_height = 0x7f0700b2
com.airdoc.mpd:attr/roundPercent = 0x7f0403d0
com.airdoc.mpd:dimen/exo_styled_progress_margin_bottom = 0x7f0700b1
com.airdoc.mpd:color/m3_sys_color_dynamic_primary_fixed = 0x7f0601d7
com.airdoc.mpd:dimen/m3_chip_dragged_translation_z = 0x7f070119
com.airdoc.mpd:dimen/exo_styled_progress_layout_height = 0x7f0700b0
com.airdoc.mpd:attr/layout_constraintStart_toStartOf = 0x7f0402a7
com.airdoc.mpd:dimen/exo_styled_progress_enabled_thumb_size = 0x7f0700af
com.airdoc.mpd:dimen/exo_styled_progress_bar_height = 0x7f0700ad
com.airdoc.mpd:dimen/mtrl_navigation_rail_text_size = 0x7f0702ef
com.airdoc.mpd:color/primary_dark_material_dark = 0x7f0602fa
com.airdoc.mpd:dimen/exo_styled_minimal_controls_margin_bottom = 0x7f0700ac
com.airdoc.mpd:dimen/exo_styled_bottom_bar_margin_top = 0x7f0700a9
com.airdoc.mpd:id/switch_fingertip_collection = 0x7f0a0250
com.airdoc.mpd:dimen/exo_styled_bottom_bar_height = 0x7f0700a8
com.airdoc.mpd:dimen/exo_small_icon_padding_vertical = 0x7f0700a6
com.airdoc.mpd:dimen/exo_small_icon_height = 0x7f0700a3
com.airdoc.mpd:dimen/exo_settings_sub_text_size = 0x7f0700a1
com.airdoc.mpd:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0601d1
com.airdoc.mpd:dimen/exo_settings_icon_size = 0x7f07009e
com.airdoc.mpd:attr/layout_constraintEnd_toEndOf = 0x7f040293
com.airdoc.mpd:dimen/mtrl_btn_max_width = 0x7f070280
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f130186
com.airdoc.mpd:dimen/exo_media_button_height = 0x7f07009a
com.airdoc.mpd:attr/scrubber_enabled_size = 0x7f0403de
com.airdoc.mpd:attr/checkMarkTint = 0x7f0400b9
com.airdoc.mpd:dimen/exo_icon_text_size = 0x7f070099
com.airdoc.mpd:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0602af
com.airdoc.mpd:drawable/exo_ic_subtitle_off = 0x7f0800cd
com.airdoc.mpd:attr/currentState = 0x7f04016a
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1301ee
com.airdoc.mpd:attr/counterTextColor = 0x7f040168
com.airdoc.mpd:attr/height = 0x7f04021f
com.airdoc.mpd:attr/round = 0x7f0403ce
com.airdoc.mpd:dimen/exo_error_message_text_padding_horizontal = 0x7f070092
com.airdoc.mpd:drawable/ic_input_phone = 0x7f080127
com.airdoc.mpd:dimen/disabled_alpha_material_light = 0x7f07008f
com.airdoc.mpd:style/Widget.Compat.NotificationActionText = 0x7f130358
com.airdoc.mpd:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0e00e5
com.airdoc.mpd:dimen/mtrl_btn_stroke_size = 0x7f070287
com.airdoc.mpd:string/hide_bottom_view_on_scroll_behavior = 0x7f12007c
com.airdoc.mpd:id/iv_exception = 0x7f0a0145
com.airdoc.mpd:attr/flow_verticalBias = 0x7f040204
com.airdoc.mpd:dimen/design_tab_scrollable_min_width = 0x7f07008a
com.airdoc.mpd:id/action_context_bar = 0x7f0a003c
com.airdoc.mpd:color/m3_selection_control_ripple_color_selector = 0x7f06016b
com.airdoc.mpd:dimen/design_snackbar_padding_horizontal = 0x7f070085
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0e0065
com.airdoc.mpd:drawable/ic_main_logo = 0x7f080134
com.airdoc.mpd:attr/state_liftable = 0x7f040431
com.airdoc.mpd:dimen/design_snackbar_min_width = 0x7f070084
com.airdoc.mpd:attr/deriveConstraintsFrom = 0x7f040184
com.airdoc.mpd:dimen/design_snackbar_background_corner_radius = 0x7f070080
com.airdoc.mpd:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0700cf
com.airdoc.mpd:dimen/design_snackbar_action_inline_max_width = 0x7f07007e
com.airdoc.mpd:string/str_device_service_expired = 0x7f120101
com.airdoc.mpd:id/accessibility_custom_action_11 = 0x7f0a0013
com.airdoc.mpd:dimen/design_navigation_item_vertical_padding = 0x7f07007a
com.airdoc.mpd:color/design_default_color_surface = 0x7f06006a
com.airdoc.mpd:dimen/design_navigation_item_icon_padding = 0x7f070079
com.airdoc.mpd:color/m3_ref_palette_white = 0x7f06016a
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0600e2
com.airdoc.mpd:dimen/design_navigation_elevation = 0x7f070075
com.airdoc.mpd:layout/mtrl_picker_text_input_date_range = 0x7f0d0081
com.airdoc.mpd:dimen/design_fab_translation_z_pressed = 0x7f070074
com.airdoc.mpd:id/skipCollapsed = 0x7f0a022a
com.airdoc.mpd:dimen/design_fab_size_mini = 0x7f070071
com.airdoc.mpd:dimen/mtrl_btn_pressed_z = 0x7f070285
com.airdoc.mpd:dimen/design_fab_image_size = 0x7f070070
com.airdoc.mpd:id/skipped = 0x7f0a022b
com.airdoc.mpd:dimen/design_bottom_sheet_peek_height_min = 0x7f07006d
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f130448
com.airdoc.mpd:string/abc_action_bar_home_description = 0x7f120000
com.airdoc.mpd:attr/applyMotionScene = 0x7f04003a
com.airdoc.mpd:anim/design_bottom_sheet_slide_out = 0x7f01001a
com.airdoc.mpd:string/str_fingertip_data_collection = 0x7f120107
com.airdoc.mpd:id/parent_matrix = 0x7f0a01e4
com.airdoc.mpd:id/material_clock_hand = 0x7f0a0180
com.airdoc.mpd:dimen/design_bottom_navigation_margin = 0x7f070068
com.airdoc.mpd:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f070266
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1301d4
com.airdoc.mpd:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f07017a
com.airdoc.mpd:dimen/design_bottom_navigation_label_padding = 0x7f070067
com.airdoc.mpd:dimen/design_bottom_navigation_height = 0x7f070063
com.airdoc.mpd:attr/minHideDelay = 0x7f040339
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary20 = 0x7f060107
com.airdoc.mpd:dimen/mtrl_slider_thumb_elevation = 0x7f070305
com.airdoc.mpd:dimen/design_bottom_navigation_elevation = 0x7f070062
com.airdoc.mpd:string/mtrl_checkbox_button_path_unchecked = 0x7f1200ad
com.airdoc.mpd:dimen/design_bottom_navigation_active_item_min_width = 0x7f070060
com.airdoc.mpd:dimen/design_appbar_elevation = 0x7f07005e
com.airdoc.mpd:dimen/def_drawer_elevation = 0x7f07005d
com.airdoc.mpd:dimen/compat_button_inset_horizontal_material = 0x7f070056
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0e0155
com.airdoc.mpd:drawable/config_switch_mask_therapy_selected = 0x7f0800b6
com.airdoc.mpd:dimen/clock_face_margin_start = 0x7f070055
com.airdoc.mpd:dimen/cardview_default_radius = 0x7f070054
com.airdoc.mpd:style/Theme.AppCompat.Light.Dialog = 0x7f130244
com.airdoc.mpd:string/m3_sys_motion_easing_legacy_accelerate = 0x7f120089
com.airdoc.mpd:dimen/cardview_default_elevation = 0x7f070053
com.airdoc.mpd:style/Widget.Support.CoordinatorLayout = 0x7f130485
com.airdoc.mpd:attr/dividerInsetEnd = 0x7f04018c
com.airdoc.mpd:attr/controlBackground = 0x7f040154
com.airdoc.mpd:dimen/cardview_compat_inset_shadow = 0x7f070052
com.airdoc.mpd:style/Widget.Material3.Chip.Filter = 0x7f13038e
com.airdoc.mpd:id/fitXY = 0x7f0a0112
com.airdoc.mpd:dimen/appcompat_dialog_background_inset = 0x7f070051
com.airdoc.mpd:attr/drawableTint = 0x7f04019b
com.airdoc.mpd:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08005e
com.airdoc.mpd:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.airdoc.mpd:id/mtrl_picker_header = 0x7f0a01ad
com.airdoc.mpd:dimen/abc_text_size_title_material = 0x7f07004f
com.airdoc.mpd:dimen/abc_text_size_subhead_material = 0x7f07004d
com.airdoc.mpd:drawable/notification_bg_low = 0x7f0801a7
com.airdoc.mpd:style/Theme.AppCompat = 0x7f130234
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0601a5
com.airdoc.mpd:dimen/abc_text_size_small_material = 0x7f07004c
com.airdoc.mpd:dimen/abc_text_size_menu_material = 0x7f07004b
com.airdoc.mpd:attr/constraints = 0x7f040142
com.airdoc.mpd:dimen/abc_text_size_large_material = 0x7f070048
com.airdoc.mpd:drawable/abc_ic_go_search_api_material = 0x7f080042
com.airdoc.mpd:styleable/ScrollingViewBehavior_Layout = 0x7f140080
com.airdoc.mpd:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f070271
com.airdoc.mpd:dimen/abc_text_size_display_3_material = 0x7f070045
com.airdoc.mpd:dimen/abc_text_size_caption_material = 0x7f070042
com.airdoc.mpd:integer/m3_sys_motion_duration_short1 = 0x7f0b001e
com.airdoc.mpd:dimen/abc_text_size_body_2_material = 0x7f070040
com.airdoc.mpd:color/m3_timepicker_button_text_color = 0x7f06021b
com.airdoc.mpd:dimen/abc_switch_padding = 0x7f07003e
com.airdoc.mpd:id/material_clock_display_and_toggle = 0x7f0a017e
com.airdoc.mpd:dimen/abc_star_small = 0x7f07003d
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f130305
com.airdoc.mpd:color/material_dynamic_secondary0 = 0x7f060252
com.airdoc.mpd:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.airdoc.mpd:attr/statusBarBackground = 0x7f040434
com.airdoc.mpd:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.airdoc.mpd:attr/textAppearanceHeadline1 = 0x7f04047b
com.airdoc.mpd:attr/tickMark = 0x7f0404c6
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f130293
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f070132
com.airdoc.mpd:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f080018
com.airdoc.mpd:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.airdoc.mpd:attr/expandedTitleMarginEnd = 0x7f0401ca
com.airdoc.mpd:dimen/abc_list_item_height_large_material = 0x7f070030
com.airdoc.mpd:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f130296
com.airdoc.mpd:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.airdoc.mpd:attr/constraint_referenced_ids = 0x7f040140
com.airdoc.mpd:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.airdoc.mpd:string/abc_menu_ctrl_shortcut_label = 0x7f120009
com.airdoc.mpd:color/mtrl_switch_thumb_tint = 0x7f0602e9
com.airdoc.mpd:attr/waveDecay = 0x7f040516
com.airdoc.mpd:color/m3_sys_color_dark_inverse_primary = 0x7f060177
com.airdoc.mpd:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.airdoc.mpd:attr/singleLine = 0x7f040410
com.airdoc.mpd:attr/deltaPolarRadius = 0x7f040183
com.airdoc.mpd:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.airdoc.mpd:attr/switchMinWidth = 0x7f040449
com.airdoc.mpd:attr/motionEffect_start = 0x7f040363
com.airdoc.mpd:drawable/abc_textfield_search_material = 0x7f080076
com.airdoc.mpd:id/endToStart = 0x7f0a00c9
com.airdoc.mpd:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f070196
com.airdoc.mpd:attr/imageRotate = 0x7f040244
com.airdoc.mpd:integer/m3_sys_shape_corner_large_corner_family = 0x7f0b0026
com.airdoc.mpd:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.airdoc.mpd:id/monospace = 0x7f0a0197
com.airdoc.mpd:color/abc_search_url_text_normal = 0x7f06000e
com.airdoc.mpd:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f070129
com.airdoc.mpd:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.airdoc.mpd:id/accessibility_custom_action_21 = 0x7f0a001e
com.airdoc.mpd:color/exo_white = 0x7f060081
com.airdoc.mpd:dimen/abc_control_padding_material = 0x7f07001a
com.airdoc.mpd:attr/showMotionSpec = 0x7f0403fa
com.airdoc.mpd:attr/boxBackgroundColor = 0x7f040086
com.airdoc.mpd:drawable/abc_tab_indicator_material = 0x7f08006c
com.airdoc.mpd:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.airdoc.mpd:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f0702a2
com.airdoc.mpd:styleable/BottomSheetBehavior_Layout = 0x7f140018
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f130202
com.airdoc.mpd:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0700c0
com.airdoc.mpd:attr/closeIconStartPadding = 0x7f0400ed
com.airdoc.mpd:color/m3_slider_thumb_color = 0x7f060170
com.airdoc.mpd:attr/colorSurfaceBright = 0x7f04012c
com.airdoc.mpd:color/m3_ref_palette_neutral100 = 0x7f060120
com.airdoc.mpd:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.airdoc.mpd:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f070130
com.airdoc.mpd:color/m3_sys_color_on_secondary_fixed_variant = 0x7f060202
com.airdoc.mpd:attr/show_vr_button = 0x7f040406
com.airdoc.mpd:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.airdoc.mpd:attr/colorSurfaceContainerHigh = 0x7f04012e
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.Button = 0x7f1301dd
com.airdoc.mpd:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Display3 = 0x7f13001e
com.airdoc.mpd:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.airdoc.mpd:id/design_navigation_view = 0x7f0a00ad
com.airdoc.mpd:dimen/abc_action_button_min_width_material = 0x7f07000e
com.airdoc.mpd:color/material_dynamic_neutral_variant100 = 0x7f06023a
com.airdoc.mpd:dimen/abc_action_button_min_height_material = 0x7f07000d
com.airdoc.mpd:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.airdoc.mpd:anim/fragment_fast_out_extra_slow_in = 0x7f01001d
com.airdoc.mpd:dimen/abc_action_bar_elevation_material = 0x7f070005
com.airdoc.mpd:color/switch_thumb_material_light = 0x7f060312
com.airdoc.mpd:attr/scrubber_color = 0x7f0403da
com.airdoc.mpd:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f13027e
com.airdoc.mpd:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.airdoc.mpd:color/white_70 = 0x7f060322
com.airdoc.mpd:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f08009a
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f130025
com.airdoc.mpd:dimen/m3_slider_inactive_track_height = 0x7f070206
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1303c2
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Day = 0x7f1303bb
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f130023
com.airdoc.mpd:color/m3_textfield_indicator_text_color = 0x7f060215
com.airdoc.mpd:dimen/m3_comp_navigation_rail_container_width = 0x7f07016d
com.airdoc.mpd:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000d
com.airdoc.mpd:color/white_50 = 0x7f060320
com.airdoc.mpd:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1302e3
com.airdoc.mpd:id/rectangles = 0x7f0a01fb
com.airdoc.mpd:color/white_40 = 0x7f06031e
com.airdoc.mpd:color/white_30 = 0x7f06031d
com.airdoc.mpd:color/white_10 = 0x7f06031b
com.airdoc.mpd:color/mtrl_textinput_filled_box_default_background_color = 0x7f0602f4
com.airdoc.mpd:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0701d8
com.airdoc.mpd:styleable/FloatingActionButton = 0x7f140036
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1303a8
com.airdoc.mpd:color/tooltip_background_light = 0x7f060318
com.airdoc.mpd:style/Theme.Design.Light.NoActionBar = 0x7f13024f
com.airdoc.mpd:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f07018e
com.airdoc.mpd:color/cardview_shadow_end_color = 0x7f060039
com.airdoc.mpd:color/teal_700 = 0x7f060316
com.airdoc.mpd:color/switch_thumb_normal_material_dark = 0x7f060313
com.airdoc.mpd:color/foreground_material_light = 0x7f060084
com.airdoc.mpd:color/switch_thumb_material_dark = 0x7f060311
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f13002b
com.airdoc.mpd:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f130430
com.airdoc.mpd:color/switch_thumb_disabled_material_light = 0x7f060310
com.airdoc.mpd:attr/shapeAppearanceLargeComponent = 0x7f0403ef
com.airdoc.mpd:drawable/common_entity_button_disabled_bg = 0x7f08008f
com.airdoc.mpd:id/match_parent = 0x7f0a017c
com.airdoc.mpd:id/mtrl_calendar_year_selector_frame = 0x7f0a01a7
com.airdoc.mpd:style/TextAppearance.AppCompat.Display3 = 0x7f1301be
com.airdoc.mpd:id/accessibility_custom_action_12 = 0x7f0a0014
com.airdoc.mpd:color/selector_input_gender_radio_button_tint = 0x7f06030d
com.airdoc.mpd:id/design_menu_item_text = 0x7f0a00ac
com.airdoc.mpd:attr/state_above_anchor = 0x7f04042b
com.airdoc.mpd:attr/itemMaxLines = 0x7f04025c
com.airdoc.mpd:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f13033b
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f130400
com.airdoc.mpd:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f130410
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f130093
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f070234
com.airdoc.mpd:color/selector_common_radio_button_tint = 0x7f06030b
com.airdoc.mpd:color/secondary_text_default_material_dark = 0x7f060307
com.airdoc.mpd:integer/hide_password_duration = 0x7f0b000b
com.airdoc.mpd:color/purple_700 = 0x7f060304
com.airdoc.mpd:color/primary_text_disabled_material_light = 0x7f060301
com.airdoc.mpd:id/design_menu_item_action_area = 0x7f0a00aa
com.airdoc.mpd:color/primary_text_disabled_material_dark = 0x7f060300
com.airdoc.mpd:color/primary_text_default_material_light = 0x7f0602ff
com.airdoc.mpd:color/primary_text_default_material_dark = 0x7f0602fe
com.airdoc.mpd:color/mtrl_btn_bg_color_selector = 0x7f0602c0
com.airdoc.mpd:color/m3_chip_assist_text_color = 0x7f060097
com.airdoc.mpd:animator/fragment_fade_exit = 0x7f020006
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0e0156
com.airdoc.mpd:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0e00d8
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0e0093
com.airdoc.mpd:color/notification_material_background_media_default_color = 0x7f0602f9
com.airdoc.mpd:color/notification_icon_bg_color = 0x7f0602f8
com.airdoc.mpd:color/white_80 = 0x7f060323
com.airdoc.mpd:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f07029b
com.airdoc.mpd:macro/m3_comp_fab_secondary_container_color = 0x7f0e003c
com.airdoc.mpd:dimen/m3_comp_filter_chip_container_height = 0x7f07014f
com.airdoc.mpd:dimen/m3_carousel_debug_keyline_width = 0x7f070110
com.airdoc.mpd:attr/drawableLeftCompat = 0x7f040197
com.airdoc.mpd:color/notification_action_color_filter = 0x7f0602f7
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f130451
com.airdoc.mpd:attr/behavior_saveFlags = 0x7f040078
com.airdoc.mpd:attr/buttonSize = 0x7f04009f
com.airdoc.mpd:color/mtrl_textinput_focused_box_stroke_color = 0x7f0602f5
com.airdoc.mpd:color/mtrl_textinput_default_box_stroke_color = 0x7f0602f2
com.airdoc.mpd:string/path_password_eye_mask_visible = 0x7f1200e6
com.airdoc.mpd:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0602b0
com.airdoc.mpd:color/mtrl_text_btn_text_color_selector = 0x7f0602f1
com.airdoc.mpd:id/message = 0x7f0a0194
com.airdoc.mpd:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0601d8
com.airdoc.mpd:dimen/design_snackbar_padding_vertical = 0x7f070086
com.airdoc.mpd:color/mtrl_tabs_colored_ripple_color = 0x7f0602ec
com.airdoc.mpd:style/CardView.Light = 0x7f130122
com.airdoc.mpd:id/dragUp = 0x7f0a00bd
com.airdoc.mpd:color/mtrl_switch_track_decoration_tint = 0x7f0602ea
com.airdoc.mpd:drawable/abc_ratingbar_material = 0x7f08005c
com.airdoc.mpd:color/mtrl_popupmenu_overlay_color = 0x7f0602e6
com.airdoc.mpd:color/mtrl_outlined_icon_tint = 0x7f0602e4
com.airdoc.mpd:layout/abc_alert_dialog_title_material = 0x7f0d000a
com.airdoc.mpd:attr/textInputFilledDenseStyle = 0x7f0404a0
com.airdoc.mpd:color/mtrl_navigation_item_icon_tint = 0x7f0602e0
com.airdoc.mpd:id/scrollIndicatorDown = 0x7f0a0211
com.airdoc.mpd:color/mtrl_navigation_bar_ripple_color = 0x7f0602de
com.airdoc.mpd:attr/arrowShaftLength = 0x7f04003d
com.airdoc.mpd:attr/counterOverflowTextAppearance = 0x7f040165
com.airdoc.mpd:attr/chipStrokeWidth = 0x7f0400d8
com.airdoc.mpd:attr/touchRegionId = 0x7f0404eb
com.airdoc.mpd:color/mtrl_navigation_bar_item_tint = 0x7f0602dd
com.airdoc.mpd:attr/badgeTextAppearance = 0x7f04005f
com.airdoc.mpd:color/material_timepicker_button_stroke = 0x7f0602bc
com.airdoc.mpd:attr/layoutDuringTransition = 0x7f04027f
com.airdoc.mpd:color/material_dynamic_neutral_variant80 = 0x7f060241
com.airdoc.mpd:color/m3_sys_color_light_surface_bright = 0x7f0601f5
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0601ab
com.airdoc.mpd:color/mtrl_indicator_text_color = 0x7f0602da
com.airdoc.mpd:string/call_notification_answer_video_action = 0x7f120025
com.airdoc.mpd:color/mtrl_filled_background_color = 0x7f0602d7
com.airdoc.mpd:color/mtrl_fab_ripple_color = 0x7f0602d6
com.airdoc.mpd:color/mtrl_fab_icon_text_color_selector = 0x7f0602d5
com.airdoc.mpd:color/mtrl_fab_bg_color_selector = 0x7f0602d4
com.airdoc.mpd:styleable/MotionHelper = 0x7f14006a
com.airdoc.mpd:dimen/mtrl_extended_fab_min_width = 0x7f0702cb
com.airdoc.mpd:drawable/common_white_20_round_20_bg = 0x7f0800ad
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0601cd
com.airdoc.mpd:color/mtrl_choice_chip_text_color = 0x7f0602d2
com.airdoc.mpd:string/exo_controls_previous_description = 0x7f120050
com.airdoc.mpd:color/mtrl_choice_chip_background_color = 0x7f0602d0
com.airdoc.mpd:color/mtrl_chip_text_color = 0x7f0602cf
com.airdoc.mpd:color/mtrl_chip_background_color = 0x7f0602cc
com.airdoc.mpd:color/mtrl_card_view_ripple = 0x7f0602cb
com.airdoc.mpd:style/Theme.AppCompat.NoActionBar = 0x7f130249
com.airdoc.mpd:color/m3_sys_color_light_on_background = 0x7f0601e3
com.airdoc.mpd:dimen/m3_small_fab_size = 0x7f070209
com.airdoc.mpd:attr/verticalOffsetWithText = 0x7f04050d
com.airdoc.mpd:color/mtrl_calendar_item_stroke_color = 0x7f0602c8
com.airdoc.mpd:attr/menuGravity = 0x7f040336
com.airdoc.mpd:color/mtrl_btn_transparent_bg_color = 0x7f0602c7
com.airdoc.mpd:color/mtrl_btn_text_color_selector = 0x7f0602c6
com.airdoc.mpd:color/mtrl_btn_text_btn_ripple_color = 0x7f0602c4
com.airdoc.mpd:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0602c3
com.airdoc.mpd:color/mtrl_btn_stroke_color_selector = 0x7f0602c2
com.airdoc.mpd:color/material_timepicker_modebutton_tint = 0x7f0602bf
com.airdoc.mpd:id/open_search_view_clear_button = 0x7f0a01d0
com.airdoc.mpd:color/m3_ref_palette_secondary99 = 0x7f06015c
com.airdoc.mpd:styleable/NavigationBarActiveIndicator = 0x7f14006f
com.airdoc.mpd:styleable/AppCompatEmojiHelper = 0x7f14000d
com.airdoc.mpd:dimen/mtrl_btn_text_btn_padding_left = 0x7f070289
com.airdoc.mpd:dimen/mtrl_low_ripple_focused_alpha = 0x7f0702db
com.airdoc.mpd:attr/behavior_skipCollapsed = 0x7f04007a
com.airdoc.mpd:color/material_timepicker_clockface = 0x7f0602be
com.airdoc.mpd:attr/cardElevation = 0x7f0400a6
com.airdoc.mpd:color/m3_ref_palette_neutral_variant90 = 0x7f060140
com.airdoc.mpd:color/material_timepicker_button_background = 0x7f0602bb
com.airdoc.mpd:dimen/mtrl_btn_padding_right = 0x7f070283
com.airdoc.mpd:color/material_slider_thumb_color = 0x7f0602ba
com.airdoc.mpd:string/abc_prepend_shortcut_label = 0x7f120011
com.airdoc.mpd:attr/customColorValue = 0x7f040170
com.airdoc.mpd:attr/elevation = 0x7f0401a9
com.airdoc.mpd:color/material_slider_inactive_track_color = 0x7f0602b9
com.airdoc.mpd:attr/transitionFlags = 0x7f0404fc
com.airdoc.mpd:color/material_slider_active_track_color = 0x7f0602b6
com.airdoc.mpd:attr/viewTransitionOnNegativeCross = 0x7f040511
com.airdoc.mpd:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f07017c
com.airdoc.mpd:color/material_personalized_hint_foreground = 0x7f0602b1
com.airdoc.mpd:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0602ac
com.airdoc.mpd:color/material_personalized_color_tertiary_container = 0x7f0602ab
com.airdoc.mpd:color/material_personalized_color_tertiary = 0x7f0602aa
com.airdoc.mpd:dimen/notification_small_icon_background_padding = 0x7f070331
com.airdoc.mpd:dimen/abc_text_size_display_1_material = 0x7f070043
com.airdoc.mpd:color/material_personalized_color_surface_inverse = 0x7f0602a8
com.airdoc.mpd:style/Widget.MaterialComponents.Toolbar = 0x7f130480
com.airdoc.mpd:style/Widget.AppCompat.ActionBar.TabView = 0x7f130312
com.airdoc.mpd:color/material_personalized_color_surface_dim = 0x7f0602a7
com.airdoc.mpd:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f13008b
com.airdoc.mpd:attr/motionDurationMedium3 = 0x7f04034e
com.airdoc.mpd:color/material_personalized_color_surface_container_highest = 0x7f0602a4
com.airdoc.mpd:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.airdoc.mpd:attr/minHeight = 0x7f040338
com.airdoc.mpd:color/material_personalized_color_surface = 0x7f0602a0
com.airdoc.mpd:dimen/m3_comp_filled_button_container_elevation = 0x7f070146
com.airdoc.mpd:color/material_personalized_color_secondary_text_inverse = 0x7f06029f
com.airdoc.mpd:attr/badgeGravity = 0x7f040058
com.airdoc.mpd:anim/abc_slide_out_top = 0x7f010009
com.airdoc.mpd:dimen/notification_top_pad_large_text = 0x7f070335
com.airdoc.mpd:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0702d0
com.airdoc.mpd:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f0701a3
com.airdoc.mpd:id/exo_rew = 0x7f0a00f6
com.airdoc.mpd:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1301f1
com.airdoc.mpd:macro/m3_comp_dialog_container_shape = 0x7f0e0024
com.airdoc.mpd:color/material_personalized_color_primary_container = 0x7f060298
com.airdoc.mpd:attr/actionOverflowMenuStyle = 0x7f040022
com.airdoc.mpd:dimen/mtrl_extended_fab_start_padding = 0x7f0702cc
com.airdoc.mpd:color/material_personalized_color_on_surface_variant = 0x7f060292
com.airdoc.mpd:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.airdoc.mpd:dimen/m3_comp_navigation_bar_container_elevation = 0x7f07015d
com.airdoc.mpd:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1303fa
com.airdoc.mpd:color/material_personalized_color_on_surface_inverse = 0x7f060291
com.airdoc.mpd:drawable/switch_mask_therapy_thumb = 0x7f0801c5
com.airdoc.mpd:color/material_personalized_color_on_surface = 0x7f060290
com.airdoc.mpd:attr/itemTextAppearanceActiveBoldEnabled = 0x7f04026e
com.airdoc.mpd:dimen/mtrl_card_corner_radius = 0x7f0702b9
com.airdoc.mpd:color/material_personalized_color_on_primary_container = 0x7f06028d
com.airdoc.mpd:drawable/common_white_round_20_bg = 0x7f0800b0
com.airdoc.mpd:dimen/design_snackbar_elevation = 0x7f070081
com.airdoc.mpd:color/material_personalized_color_on_error_container = 0x7f06028b
com.airdoc.mpd:style/Widget.Design.FloatingActionButton = 0x7f13035d
com.airdoc.mpd:dimen/mtrl_navigation_item_icon_padding = 0x7f0702e3
com.airdoc.mpd:color/material_personalized_color_error = 0x7f060287
com.airdoc.mpd:color/material_personalized_color_background = 0x7f060283
com.airdoc.mpd:id/topPanel = 0x7f0a0277
com.airdoc.mpd:style/Base.Widget.AppCompat.PopupMenu = 0x7f1300eb
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.OverflowShow = 0x7f130132
com.airdoc.mpd:attr/show_fastforward_button = 0x7f0403ff
com.airdoc.mpd:color/mtrl_on_surface_ripple_color = 0x7f0602e3
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f1301a6
com.airdoc.mpd:attr/layout_optimizationLevel = 0x7f0402c1
com.airdoc.mpd:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f070171
com.airdoc.mpd:color/material_on_primary_emphasis_medium = 0x7f06027c
com.airdoc.mpd:id/action_container = 0x7f0a003b
com.airdoc.mpd:color/material_on_primary_emphasis_high_type = 0x7f06027b
com.airdoc.mpd:dimen/m3_extended_fab_bottom_padding = 0x7f0701cc
com.airdoc.mpd:color/m3_ref_palette_secondary20 = 0x7f060153
com.airdoc.mpd:color/material_on_primary_disabled = 0x7f06027a
com.airdoc.mpd:attr/windowNoTitle = 0x7f040525
com.airdoc.mpd:color/material_on_background_emphasis_medium = 0x7f060279
com.airdoc.mpd:color/material_harmonized_color_error_container = 0x7f060274
com.airdoc.mpd:color/mtrl_chip_surface_color = 0x7f0602ce
com.airdoc.mpd:color/material_harmonized_color_error = 0x7f060273
com.airdoc.mpd:color/material_grey_800 = 0x7f060270
com.airdoc.mpd:attr/textAppearanceListItemSecondary = 0x7f04048a
com.airdoc.mpd:attr/recyclerViewStyle = 0x7f0403c3
com.airdoc.mpd:color/material_grey_600 = 0x7f06026f
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0e007b
com.airdoc.mpd:color/material_grey_300 = 0x7f06026d
com.airdoc.mpd:color/material_personalized_color_secondary = 0x7f06029c
com.airdoc.mpd:color/material_dynamic_tertiary99 = 0x7f06026b
com.airdoc.mpd:color/material_dynamic_tertiary90 = 0x7f060269
com.airdoc.mpd:dimen/abc_text_size_medium_material = 0x7f070049
com.airdoc.mpd:color/material_dynamic_tertiary80 = 0x7f060268
com.airdoc.mpd:attr/goIcon = 0x7f04021a
com.airdoc.mpd:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0701de
com.airdoc.mpd:drawable/btn_radio_off_mtrl = 0x7f08007f
com.airdoc.mpd:id/deltaRelative = 0x7f0a00a7
com.airdoc.mpd:color/material_dynamic_tertiary60 = 0x7f060266
com.airdoc.mpd:color/material_dynamic_tertiary50 = 0x7f060265
com.airdoc.mpd:attr/layout_constraintBottom_toTopOf = 0x7f04028e
com.airdoc.mpd:attr/fontFamily = 0x7f040209
com.airdoc.mpd:attr/textAppearanceHeadline4 = 0x7f04047e
com.airdoc.mpd:color/material_dynamic_tertiary40 = 0x7f060264
com.airdoc.mpd:id/exo_controller_placeholder = 0x7f0a00dc
com.airdoc.mpd:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0702f9
com.airdoc.mpd:attr/textAllCaps = 0x7f04046f
com.airdoc.mpd:color/mtrl_tabs_legacy_text_color_selector = 0x7f0602ef
com.airdoc.mpd:color/mtrl_scrim_color = 0x7f0602e7
com.airdoc.mpd:attr/indeterminateAnimationType = 0x7f040247
com.airdoc.mpd:color/common_google_signin_btn_text_dark_disabled = 0x7f060046
com.airdoc.mpd:attr/springStiffness = 0x7f04041f
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline6 = 0x7f13022b
com.airdoc.mpd:attr/constraintSet = 0x7f04013d
com.airdoc.mpd:color/material_dynamic_secondary80 = 0x7f06025b
com.airdoc.mpd:attr/constraintSetEnd = 0x7f04013e
com.airdoc.mpd:color/material_dynamic_secondary70 = 0x7f06025a
com.airdoc.mpd:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0701ec
com.airdoc.mpd:dimen/m3_bottom_nav_item_padding_top = 0x7f0700e3
com.airdoc.mpd:style/TextAppearance.Material3.HeadlineMedium = 0x7f130214
com.airdoc.mpd:color/material_dynamic_secondary30 = 0x7f060256
com.airdoc.mpd:attr/coplanarSiblingViewId = 0x7f040157
com.airdoc.mpd:color/material_dynamic_primary90 = 0x7f06024f
com.airdoc.mpd:attr/tickColorInactive = 0x7f0404c5
com.airdoc.mpd:attr/unplayed_color = 0x7f040504
com.airdoc.mpd:color/material_dynamic_primary50 = 0x7f06024b
com.airdoc.mpd:id/startHorizontal = 0x7f0a0243
com.airdoc.mpd:color/m3_textfield_filled_background_color = 0x7f060214
com.airdoc.mpd:color/material_dynamic_primary40 = 0x7f06024a
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1301fd
com.airdoc.mpd:color/material_dynamic_neutral70 = 0x7f060233
com.airdoc.mpd:dimen/abc_dialog_min_width_major = 0x7f070022
com.airdoc.mpd:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f070236
com.airdoc.mpd:attr/scopeUris = 0x7f0403d6
com.airdoc.mpd:color/material_dynamic_primary30 = 0x7f060249
com.airdoc.mpd:attr/scrubber_dragged_size = 0x7f0403dc
com.airdoc.mpd:color/material_dynamic_primary20 = 0x7f060248
com.airdoc.mpd:color/material_dynamic_primary100 = 0x7f060247
com.airdoc.mpd:color/material_dynamic_primary10 = 0x7f060246
com.airdoc.mpd:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f070252
com.airdoc.mpd:style/Base.Widget.MaterialComponents.TextView = 0x7f13011f
com.airdoc.mpd:color/m3_ref_palette_tertiary60 = 0x7f060164
com.airdoc.mpd:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080075
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f13007a
com.airdoc.mpd:color/material_dynamic_neutral_variant99 = 0x7f060244
com.airdoc.mpd:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0700d3
com.airdoc.mpd:color/material_dynamic_neutral_variant40 = 0x7f06023d
com.airdoc.mpd:id/material_timepicker_ok_button = 0x7f0a018e
com.airdoc.mpd:dimen/mtrl_switch_thumb_elevation = 0x7f070312
com.airdoc.mpd:attr/paddingTopNoTitle = 0x7f04038e
com.airdoc.mpd:style/Widget.MaterialComponents.Button = 0x7f13041c
com.airdoc.mpd:color/material_dynamic_neutral_variant30 = 0x7f06023c
com.airdoc.mpd:color/material_personalized_color_on_tertiary = 0x7f060293
com.airdoc.mpd:dimen/m3_comp_switch_track_height = 0x7f0701b4
com.airdoc.mpd:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f13036a
com.airdoc.mpd:attr/stackFromEnd = 0x7f040422
com.airdoc.mpd:dimen/mtrl_fab_translation_z_pressed = 0x7f0702d5
com.airdoc.mpd:color/m3_ref_palette_error95 = 0x7f06011c
com.airdoc.mpd:string/mtrl_picker_navigate_to_current_year_description = 0x7f1200c4
com.airdoc.mpd:color/material_dynamic_neutral_variant0 = 0x7f060238
com.airdoc.mpd:id/exo_ad_overlay = 0x7f0a00d2
com.airdoc.mpd:color/material_dynamic_neutral80 = 0x7f060234
com.airdoc.mpd:style/Platform.AppCompat = 0x7f130157
com.airdoc.mpd:color/m3_dark_highlighted_text = 0x7f06009e
com.airdoc.mpd:drawable/$m3_avd_hide_password__1 = 0x7f080008
com.airdoc.mpd:color/material_dynamic_neutral50 = 0x7f060231
com.airdoc.mpd:color/mtrl_tabs_icon_color_selector_colored = 0x7f0602ee
com.airdoc.mpd:macro/m3_comp_outlined_text_field_container_shape = 0x7f0e00b3
com.airdoc.mpd:color/material_dynamic_neutral30 = 0x7f06022f
com.airdoc.mpd:attr/colorContainer = 0x7f040100
com.airdoc.mpd:integer/m3_sys_shape_corner_full_corner_family = 0x7f0b0025
com.airdoc.mpd:color/material_dynamic_neutral20 = 0x7f06022e
com.airdoc.mpd:drawable/abc_seekbar_thumb_material = 0x7f080063
com.airdoc.mpd:color/m3_dark_hint_foreground = 0x7f06009f
com.airdoc.mpd:color/m3_sys_color_dark_inverse_on_surface = 0x7f060176
com.airdoc.mpd:color/material_dynamic_neutral0 = 0x7f06022b
com.airdoc.mpd:attr/cornerSize = 0x7f04015e
com.airdoc.mpd:dimen/exo_error_message_text_padding_vertical = 0x7f070093
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f070220
com.airdoc.mpd:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f070192
com.airdoc.mpd:attr/layoutDescription = 0x7f04027e
com.airdoc.mpd:color/m3_text_button_background_color_selector = 0x7f060211
com.airdoc.mpd:color/material_divider_color = 0x7f06022a
com.airdoc.mpd:string/str_wechat_scan_code = 0x7f120132
com.airdoc.mpd:attr/motionEffect_end = 0x7f040361
com.airdoc.mpd:color/material_deep_teal_500 = 0x7f060229
com.airdoc.mpd:color/material_cursor_color = 0x7f060227
com.airdoc.mpd:color/material_blue_grey_950 = 0x7f060226
com.airdoc.mpd:animator/design_fab_show_motion_spec = 0x7f020002
com.airdoc.mpd:color/m3_timepicker_time_input_stroke_color = 0x7f060222
com.airdoc.mpd:layout/mtrl_calendar_day_of_week = 0x7f0d006c
com.airdoc.mpd:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f070254
com.airdoc.mpd:color/m3_timepicker_display_text_color = 0x7f06021f
com.airdoc.mpd:layout/notification_template_lines_media = 0x7f0d008e
com.airdoc.mpd:color/m3_timepicker_display_background_color = 0x7f06021d
com.airdoc.mpd:attr/placeholder_emptyVisibility = 0x7f0403a3
com.airdoc.mpd:attr/fabAlignmentMode = 0x7f0401d7
com.airdoc.mpd:color/m3_filled_icon_button_container_color_selector = 0x7f0600b2
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f070186
com.airdoc.mpd:color/m3_timepicker_button_ripple_color = 0x7f06021a
com.airdoc.mpd:dimen/m3_chip_elevated_elevation = 0x7f07011a
com.airdoc.mpd:color/m3_text_button_ripple_color_selector = 0x7f060213
com.airdoc.mpd:dimen/design_fab_border_width = 0x7f07006e
com.airdoc.mpd:color/m3_tabs_text_color = 0x7f06020f
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary0 = 0x7f0600ea
com.airdoc.mpd:id/coordinator = 0x7f0a0099
com.airdoc.mpd:attr/colorControlActivated = 0x7f040101
com.airdoc.mpd:color/m3_tabs_ripple_color_secondary = 0x7f06020e
com.airdoc.mpd:color/m3_tabs_ripple_color = 0x7f06020d
com.airdoc.mpd:style/Base.ThemeOverlay.AppCompat = 0x7f130079
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0e0010
com.airdoc.mpd:color/m3_tabs_icon_color_secondary = 0x7f06020c
com.airdoc.mpd:id/transition_position = 0x7f0a027d
com.airdoc.mpd:attr/textureEffect = 0x7f0404b1
com.airdoc.mpd:color/m3_sys_color_tertiary_fixed_dim = 0x7f06020a
com.airdoc.mpd:id/hardware = 0x7f0a0127
com.airdoc.mpd:id/startToEnd = 0x7f0a0244
com.airdoc.mpd:attr/use_controller = 0x7f04050a
com.airdoc.mpd:color/m3_sys_color_secondary_fixed = 0x7f060207
com.airdoc.mpd:attr/voiceIcon = 0x7f040514
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f130094
com.airdoc.mpd:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f060204
com.airdoc.mpd:attr/badgeWithTextShapeAppearanceOverlay = 0x7f040067
com.airdoc.mpd:drawable/mtrl_popupmenu_background = 0x7f080195
com.airdoc.mpd:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0702c4
com.airdoc.mpd:drawable/exo_styled_controls_overflow_hide = 0x7f0800fc
com.airdoc.mpd:attr/lineHeight = 0x7f0402ca
com.airdoc.mpd:drawable/common_full_open_on_phone = 0x7f080091
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0e0076
com.airdoc.mpd:drawable/exo_edit_mode_logo = 0x7f0800bd
com.airdoc.mpd:id/spread_inside = 0x7f0a023b
com.airdoc.mpd:id/path = 0x7f0a01e6
com.airdoc.mpd:color/m3_sys_color_light_surface_container_lowest = 0x7f0601fa
com.airdoc.mpd:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0e0014
com.airdoc.mpd:attr/quantizeMotionInterpolator = 0x7f0403b4
com.airdoc.mpd:color/m3_sys_color_light_surface_container_low = 0x7f0601f9
com.airdoc.mpd:drawable/abc_list_selector_holo_dark = 0x7f080057
com.airdoc.mpd:styleable/ConstraintSet = 0x7f14002d
com.airdoc.mpd:id/exo_minimal_controls = 0x7f0a00e7
com.airdoc.mpd:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0601d4
com.airdoc.mpd:attr/show_timeout = 0x7f040405
com.airdoc.mpd:attr/colorSurfaceContainerHighest = 0x7f04012f
com.airdoc.mpd:dimen/m3_side_sheet_standard_elevation = 0x7f070202
com.airdoc.mpd:color/m3_sys_color_light_secondary_container = 0x7f0601f3
com.airdoc.mpd:color/m3_sys_color_light_surface_container = 0x7f0601f6
com.airdoc.mpd:id/tag_accessibility_heading = 0x7f0a0255
com.airdoc.mpd:attr/textAppearanceHeadline3 = 0x7f04047d
com.airdoc.mpd:color/m3_sys_color_light_surface = 0x7f0601f4
com.airdoc.mpd:attr/chipIconTint = 0x7f0400ce
com.airdoc.mpd:style/Base.TextAppearance.AppCompat = 0x7f130017
com.airdoc.mpd:drawable/selector_input_gender_radio_button = 0x7f0801ba
com.airdoc.mpd:id/fill = 0x7f0a0106
com.airdoc.mpd:color/m3_sys_color_light_primary_container = 0x7f0601f1
com.airdoc.mpd:color/m3_sys_color_light_primary = 0x7f0601f0
com.airdoc.mpd:id/buttonPanel = 0x7f0a006a
com.airdoc.mpd:color/m3_sys_color_light_outline_variant = 0x7f0601ef
com.airdoc.mpd:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f070141
com.airdoc.mpd:style/Widget.MaterialComponents.CardView = 0x7f130428
com.airdoc.mpd:color/m3_sys_color_light_outline = 0x7f0601ee
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Display1 = 0x7f13001c
com.airdoc.mpd:attr/motionEasingStandard = 0x7f04035c
com.airdoc.mpd:drawable/abc_text_select_handle_left_mtrl = 0x7f08006f
com.airdoc.mpd:attr/percentHeight = 0x7f04039a
com.airdoc.mpd:color/m3_switch_thumb_tint = 0x7f060171
com.airdoc.mpd:color/m3_sys_color_light_on_secondary = 0x7f0601e8
com.airdoc.mpd:color/m3_sys_color_light_on_error = 0x7f0601e4
com.airdoc.mpd:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f130298
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f070134
com.airdoc.mpd:color/m3_sys_color_light_inverse_surface = 0x7f0601e2
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f080015
com.airdoc.mpd:color/m3_sys_color_light_error_container = 0x7f0601df
com.airdoc.mpd:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0601dc
com.airdoc.mpd:id/showTitle = 0x7f0a0228
com.airdoc.mpd:id/ALT = 0x7f0a0000
com.airdoc.mpd:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0601db
com.airdoc.mpd:style/Widget.AppCompat.Button.Colored = 0x7f13031d
com.airdoc.mpd:style/TextAppearance.AppCompat.Display4 = 0x7f1301bf
com.airdoc.mpd:attr/onHide = 0x7f04037f
com.airdoc.mpd:attr/layout_behavior = 0x7f040283
com.airdoc.mpd:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f080043
com.airdoc.mpd:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0601d9
com.airdoc.mpd:attr/materialIconButtonFilledStyle = 0x7f04031a
com.airdoc.mpd:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0e005b
com.airdoc.mpd:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0e0054
com.airdoc.mpd:id/tv_scanner_settings = 0x7f0a029d
com.airdoc.mpd:id/dragEnd = 0x7f0a00b9
com.airdoc.mpd:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0601d5
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0601cc
com.airdoc.mpd:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f13004b
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0601c9
com.airdoc.mpd:attr/itemPaddingBottom = 0x7f04025f
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_container = 0x7f0601c8
com.airdoc.mpd:attr/fontProviderQuery = 0x7f04020f
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface = 0x7f0601c6
com.airdoc.mpd:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f0701aa
com.airdoc.mpd:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f130411
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1302fe
com.airdoc.mpd:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f070126
com.airdoc.mpd:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0601c5
com.airdoc.mpd:string/mtrl_picker_cancel = 0x7f1200b9
com.airdoc.mpd:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0702fa
com.airdoc.mpd:color/black_20 = 0x7f060024
com.airdoc.mpd:color/m3_sys_color_dynamic_light_secondary = 0x7f0601c4
com.airdoc.mpd:attr/suffixText = 0x7f040444
com.airdoc.mpd:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f130119
com.airdoc.mpd:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f080179
com.airdoc.mpd:attr/floatingActionButtonSmallStyle = 0x7f0401ef
com.airdoc.mpd:attr/trackHeight = 0x7f0404f5
com.airdoc.mpd:color/m3_sys_color_dynamic_light_primary_container = 0x7f0601c3
com.airdoc.mpd:color/m3_navigation_bar_ripple_color_selector = 0x7f0600b8
com.airdoc.mpd:color/m3_sys_color_dynamic_light_primary = 0x7f0601c2
com.airdoc.mpd:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0601c1
com.airdoc.mpd:color/abc_secondary_text_material_light = 0x7f060012
com.airdoc.mpd:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0701bf
com.airdoc.mpd:color/m3_sys_color_dynamic_light_outline = 0x7f0601c0
com.airdoc.mpd:style/ExoMediaButton.Pause = 0x7f130126
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0601bf
com.airdoc.mpd:dimen/mtrl_slider_label_padding = 0x7f070302
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0601bd
com.airdoc.mpd:drawable/icon_settings_unselect = 0x7f080161
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0601ba
com.airdoc.mpd:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0601b6
com.airdoc.mpd:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0601b5
com.airdoc.mpd:style/ExoMediaButton.VR = 0x7f13012a
com.airdoc.mpd:macro/m3_comp_switch_unselected_track_color = 0x7f0e0141
com.airdoc.mpd:color/m3_sys_color_dynamic_light_background = 0x7f0601b3
com.airdoc.mpd:id/sp_domain_name = 0x7f0a0233
com.airdoc.mpd:color/exo_black_opacity_70 = 0x7f06007c
com.airdoc.mpd:drawable/exo_styled_controls_previous = 0x7f080100
com.airdoc.mpd:dimen/mtrl_card_spacing = 0x7f0702bc
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Chip = 0x7f130225
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0601ae
com.airdoc.mpd:dimen/exo_error_message_margin_bottom = 0x7f070091
com.airdoc.mpd:color/mtrl_choice_chip_ripple_color = 0x7f0602d1
com.airdoc.mpd:anim/design_snackbar_in = 0x7f01001b
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0601ad
com.airdoc.mpd:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1301d2
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0601a9
com.airdoc.mpd:attr/windowFixedHeightMinor = 0x7f040520
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0601a7
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_secondary = 0x7f0601a6
com.airdoc.mpd:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1303e5
com.airdoc.mpd:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0c000d
com.airdoc.mpd:attr/textAppearanceLabelSmall = 0x7f040486
com.airdoc.mpd:dimen/design_tab_max_width = 0x7f070089
com.airdoc.mpd:layout/design_layout_snackbar_include = 0x7f0d002b
com.airdoc.mpd:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0e0018
com.airdoc.mpd:drawable/icon_ai_train_guide_instructions = 0x7f08014b
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_outline = 0x7f0601a2
com.airdoc.mpd:attr/menuAlignmentMode = 0x7f040335
com.airdoc.mpd:dimen/m3_btn_elevation = 0x7f0700f3
com.airdoc.mpd:attr/startIconMinSize = 0x7f040427
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f06019d
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1300e5
com.airdoc.mpd:id/slide = 0x7f0a022c
com.airdoc.mpd:dimen/abc_search_view_preferred_width = 0x7f070037
com.airdoc.mpd:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f130371
com.airdoc.mpd:attr/iconGravity = 0x7f040236
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_primary = 0x7f06019a
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f060197
com.airdoc.mpd:macro/m3_comp_search_view_divider_color = 0x7f0e00f4
com.airdoc.mpd:color/purple_500 = 0x7f060303
com.airdoc.mpd:attr/gapBetweenBars = 0x7f040218
com.airdoc.mpd:color/m3_dark_default_color_primary_text = 0x7f06009c
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_background = 0x7f060195
com.airdoc.mpd:color/m3_sys_color_dark_tertiary_container = 0x7f060194
com.airdoc.mpd:style/Widget.AppCompat.Button.Borderless = 0x7f13031a
com.airdoc.mpd:attr/shapeAppearanceCornerLarge = 0x7f0403ec
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f07022e
com.airdoc.mpd:color/m3_sys_color_dark_surface_container_low = 0x7f06018f
com.airdoc.mpd:attr/listChoiceBackgroundIndicator = 0x7f0402cd
com.airdoc.mpd:color/material_personalized_color_primary_text_inverse = 0x7f06029b
com.airdoc.mpd:color/m3_ref_palette_tertiary95 = 0x7f060168
com.airdoc.mpd:color/m3_timepicker_display_ripple_color = 0x7f06021e
com.airdoc.mpd:color/m3_sys_color_dark_surface_container_highest = 0x7f06018e
com.airdoc.mpd:id/rb_whatsapp_qr_code = 0x7f0a01fa
com.airdoc.mpd:dimen/design_snackbar_max_width = 0x7f070083
com.airdoc.mpd:color/m3_sys_color_dark_surface_container_high = 0x7f06018d
com.airdoc.mpd:color/m3_sys_color_dark_surface_container = 0x7f06018c
com.airdoc.mpd:color/m3_sys_color_dark_surface_bright = 0x7f06018b
com.airdoc.mpd:attr/motionDurationShort4 = 0x7f040353
com.airdoc.mpd:color/m3_sys_color_dark_secondary_container = 0x7f060189
com.airdoc.mpd:attr/statusBarScrim = 0x7f040436
com.airdoc.mpd:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f080095
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f13017e
com.airdoc.mpd:color/m3_sys_color_dark_outline = 0x7f060184
com.airdoc.mpd:animator/fragment_close_enter = 0x7f020003
com.airdoc.mpd:attr/placeholderTextColor = 0x7f0403a2
com.airdoc.mpd:dimen/compat_button_inset_vertical_material = 0x7f070057
com.airdoc.mpd:color/m3_sys_color_dark_on_tertiary_container = 0x7f060183
com.airdoc.mpd:color/m3_sys_color_dark_on_tertiary = 0x7f060182
com.airdoc.mpd:color/m3_sys_color_dark_on_surface_variant = 0x7f060181
com.airdoc.mpd:color/m3_sys_color_dark_inverse_surface = 0x7f060178
com.airdoc.mpd:dimen/design_bottom_navigation_item_min_width = 0x7f070066
com.airdoc.mpd:styleable/CollapsingToolbarLayout = 0x7f140024
com.airdoc.mpd:color/m3_sys_color_dark_error_container = 0x7f060175
com.airdoc.mpd:color/m3_sys_color_dark_error = 0x7f060174
com.airdoc.mpd:color/m3_switch_track_tint = 0x7f060172
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0601ca
com.airdoc.mpd:attr/actionModeCloseDrawable = 0x7f040015
com.airdoc.mpd:color/m3_slider_active_track_color = 0x7f06016d
com.airdoc.mpd:color/dim_foreground_material_dark = 0x7f060077
com.airdoc.mpd:attr/subtitle = 0x7f04043f
com.airdoc.mpd:color/m3_simple_item_ripple_color = 0x7f06016c
com.airdoc.mpd:dimen/notification_large_icon_height = 0x7f07032b
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1303a5
com.airdoc.mpd:color/m3_ref_palette_tertiary99 = 0x7f060169
com.airdoc.mpd:style/ShapeableImage_Rounded_80 = 0x7f1301b6
com.airdoc.mpd:color/m3_ref_palette_tertiary70 = 0x7f060165
com.airdoc.mpd:styleable/MaterialDivider = 0x7f14005c
com.airdoc.mpd:color/m3_ref_palette_tertiary80 = 0x7f060166
com.airdoc.mpd:dimen/m3_fab_corner_size = 0x7f0701d3
com.airdoc.mpd:dimen/hint_pressed_alpha_material_dark = 0x7f0700bb
com.airdoc.mpd:id/accelerate = 0x7f0a000e
com.airdoc.mpd:id/accessibility_custom_action_17 = 0x7f0a0019
com.airdoc.mpd:color/m3_ref_palette_tertiary30 = 0x7f060161
com.airdoc.mpd:attr/behavior_autoHide = 0x7f04006f
com.airdoc.mpd:attr/textAppearanceListItemSmall = 0x7f04048b
com.airdoc.mpd:color/m3_ref_palette_tertiary100 = 0x7f06015f
com.airdoc.mpd:color/m3_ref_palette_tertiary0 = 0x7f06015d
com.airdoc.mpd:attr/actionBarTabTextStyle = 0x7f04000a
com.airdoc.mpd:color/material_slider_inactive_tick_marks_color = 0x7f0602b8
com.airdoc.mpd:attr/contentPaddingRight = 0x7f04014f
com.airdoc.mpd:color/material_blue_grey_800 = 0x7f060224
com.airdoc.mpd:attr/tickRadiusActive = 0x7f0404c9
com.airdoc.mpd:id/filled = 0x7f0a010c
com.airdoc.mpd:attr/fontWeight = 0x7f040213
com.airdoc.mpd:attr/floatingActionButtonLargeStyle = 0x7f0401e8
com.airdoc.mpd:color/m3_ref_palette_secondary50 = 0x7f060156
com.airdoc.mpd:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0401d1
com.airdoc.mpd:dimen/m3_btn_padding_left = 0x7f0700fd
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f1301aa
com.airdoc.mpd:attr/contentScrim = 0x7f040152
com.airdoc.mpd:color/m3_ref_palette_secondary30 = 0x7f060154
com.airdoc.mpd:color/m3_ref_palette_secondary0 = 0x7f060150
com.airdoc.mpd:color/material_personalized_color_on_secondary = 0x7f06028e
com.airdoc.mpd:styleable/FontFamily = 0x7f140039
com.airdoc.mpd:id/cl_input_phone = 0x7f0a0081
com.airdoc.mpd:color/m3_ref_palette_primary60 = 0x7f06014a
com.airdoc.mpd:attr/textPanX = 0x7f0404ab
com.airdoc.mpd:attr/tabTextColor = 0x7f040468
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0601a3
com.airdoc.mpd:attr/singleSelection = 0x7f040411
com.airdoc.mpd:attr/materialCalendarMonthNavigationButton = 0x7f04030d
com.airdoc.mpd:dimen/m3_comp_menu_container_elevation = 0x7f07015a
com.airdoc.mpd:attr/transitionEasing = 0x7f0404fb
com.airdoc.mpd:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f080022
com.airdoc.mpd:color/m3_ref_palette_primary40 = 0x7f060148
com.airdoc.mpd:style/TextAppearance.Design.Counter.Overflow = 0x7f1301f3
com.airdoc.mpd:color/m3_ref_palette_primary20 = 0x7f060146
com.airdoc.mpd:attr/layoutManager = 0x7f040280
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1301eb
com.airdoc.mpd:dimen/material_clock_display_padding = 0x7f07023f
com.airdoc.mpd:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f08002f
com.airdoc.mpd:color/m3_ref_palette_primary10 = 0x7f060144
com.airdoc.mpd:drawable/abc_btn_radio_material = 0x7f080032
com.airdoc.mpd:color/material_on_surface_stroke = 0x7f060280
com.airdoc.mpd:attr/lottie_enableMergePathsForKitKatAndAbove = 0x7f0402e6
com.airdoc.mpd:color/m3_ref_palette_neutral_variant99 = 0x7f060142
com.airdoc.mpd:attr/tabIconTintMode = 0x7f040451
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1301d6
com.airdoc.mpd:dimen/m3_alert_dialog_icon_margin = 0x7f0700c4
com.airdoc.mpd:attr/indicatorColor = 0x7f040249
com.airdoc.mpd:attr/actionMenuTextColor = 0x7f040011
com.airdoc.mpd:color/m3_ref_palette_neutral_variant95 = 0x7f060141
com.airdoc.mpd:dimen/m3_appbar_size_medium = 0x7f0700ce
com.airdoc.mpd:dimen/mtrl_btn_focused_z = 0x7f07027a
com.airdoc.mpd:drawable/abc_control_background_material = 0x7f08003b
com.airdoc.mpd:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f130080
com.airdoc.mpd:attr/actionBarPopupTheme = 0x7f040004
com.airdoc.mpd:color/m3_ref_palette_neutral_variant30 = 0x7f06013a
com.airdoc.mpd:dimen/mtrl_btn_elevation = 0x7f070279
com.airdoc.mpd:attr/actionViewClass = 0x7f040025
com.airdoc.mpd:id/one = 0x7f0a01cd
com.airdoc.mpd:color/m3_ref_palette_neutral_variant100 = 0x7f060138
com.airdoc.mpd:color/m3_ref_palette_neutral_variant10 = 0x7f060137
com.airdoc.mpd:attr/colorSwitchThumbNormal = 0x7f040135
com.airdoc.mpd:drawable/icon_login_account_number = 0x7f080157
com.airdoc.mpd:attr/layout_constraintLeft_creator = 0x7f0402a0
com.airdoc.mpd:color/m3_ref_palette_neutral99 = 0x7f060135
com.airdoc.mpd:color/m3_ref_palette_neutral_variant70 = 0x7f06013e
com.airdoc.mpd:anim/abc_tooltip_enter = 0x7f01000a
com.airdoc.mpd:dimen/mtrl_textinput_box_stroke_width_default = 0x7f07031a
com.airdoc.mpd:drawable/exo_notification_previous = 0x7f0800f1
com.airdoc.mpd:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f0702ac
com.airdoc.mpd:id/default_activity_button = 0x7f0a00a6
com.airdoc.mpd:color/m3_ref_palette_neutral90 = 0x7f06012f
com.airdoc.mpd:attr/materialSwitchStyle = 0x7f040323
com.airdoc.mpd:attr/closeIconVisible = 0x7f0400ef
com.airdoc.mpd:color/m3_ref_palette_neutral87 = 0x7f06012e
com.airdoc.mpd:id/header_title = 0x7f0a0128
com.airdoc.mpd:attr/extendedFloatingActionButtonStyle = 0x7f0401d3
com.airdoc.mpd:dimen/mtrl_navigation_rail_elevation = 0x7f0702ea
com.airdoc.mpd:color/m3_ref_palette_neutral70 = 0x7f06012c
com.airdoc.mpd:color/m3_ref_palette_neutral40 = 0x7f060128
com.airdoc.mpd:attr/materialSearchViewStyle = 0x7f040320
com.airdoc.mpd:string/mtrl_switch_thumb_path_checked = 0x7f1200da
com.airdoc.mpd:color/m3_ref_palette_neutral30 = 0x7f060126
com.airdoc.mpd:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f130434
com.airdoc.mpd:color/material_grey_850 = 0x7f060271
com.airdoc.mpd:color/m3_ref_palette_neutral22 = 0x7f060124
com.airdoc.mpd:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f13025a
com.airdoc.mpd:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f08009e
com.airdoc.mpd:color/m3_ref_palette_neutral20 = 0x7f060123
com.airdoc.mpd:attr/layout_goneMarginRight = 0x7f0402bb
com.airdoc.mpd:anim/mtrl_bottom_sheet_slide_out = 0x7f01002b
com.airdoc.mpd:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080080
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0600de
com.airdoc.mpd:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f070142
com.airdoc.mpd:attr/marginTopSystemWindowInsets = 0x7f0402f7
com.airdoc.mpd:color/m3_ref_palette_neutral12 = 0x7f060121
com.airdoc.mpd:id/tag_transition_group = 0x7f0a025c
com.airdoc.mpd:drawable/icon_version = 0x7f080163
com.airdoc.mpd:color/switch_thumb_normal_material_light = 0x7f060314
com.airdoc.mpd:id/exo_ffwd = 0x7f0a00e2
com.airdoc.mpd:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f07017d
com.airdoc.mpd:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f07016a
com.airdoc.mpd:drawable/exo_icon_pause = 0x7f0800d4
com.airdoc.mpd:color/m3_ref_palette_neutral10 = 0x7f06011f
com.airdoc.mpd:attr/materialCardViewStyle = 0x7f040314
com.airdoc.mpd:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0700d5
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0e008b
com.airdoc.mpd:color/m3_ref_palette_error99 = 0x7f06011d
com.airdoc.mpd:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f08001f
com.airdoc.mpd:dimen/item_touch_helper_swipe_escape_velocity = 0x7f0700bf
com.airdoc.mpd:attr/touchAnchorSide = 0x7f0404ea
com.airdoc.mpd:color/m3_sys_color_light_on_tertiary = 0x7f0601ec
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_surface = 0x7f06019e
com.airdoc.mpd:style/Widget.Material3.LinearProgressIndicator = 0x7f1303b8
com.airdoc.mpd:attr/lottie_fileName = 0x7f0402e8
com.airdoc.mpd:color/m3_ref_palette_primary30 = 0x7f060147
com.airdoc.mpd:id/tag_accessibility_pane_title = 0x7f0a0256
com.airdoc.mpd:dimen/m3_btn_dialog_btn_spacing = 0x7f0700ef
com.airdoc.mpd:color/common_google_signin_btn_text_light_focused = 0x7f06004c
com.airdoc.mpd:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.airdoc.mpd:attr/minWidth = 0x7f04033c
com.airdoc.mpd:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f130339
com.airdoc.mpd:color/m3_ref_palette_error80 = 0x7f06011a
com.airdoc.mpd:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0e002e
com.airdoc.mpd:attr/listPreferredItemPaddingRight = 0x7f0402da
com.airdoc.mpd:id/view_tree_lifecycle_owner = 0x7f0a02b6
com.airdoc.mpd:color/m3_ref_palette_error70 = 0x7f060119
com.airdoc.mpd:color/m3_ref_palette_error30 = 0x7f060115
com.airdoc.mpd:color/black_40 = 0x7f060026
com.airdoc.mpd:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f0701ad
com.airdoc.mpd:attr/layout_constraintBaseline_creator = 0x7f040288
com.airdoc.mpd:color/m3_ref_palette_error100 = 0x7f060113
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0600e3
com.airdoc.mpd:attr/showDividers = 0x7f0403f9
com.airdoc.mpd:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f07017b
com.airdoc.mpd:dimen/m3_comp_switch_disabled_track_opacity = 0x7f0701ae
com.airdoc.mpd:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.airdoc.mpd:color/background_floating_material_dark = 0x7f06001d
com.airdoc.mpd:color/secondary_text_disabled_material_light = 0x7f06030a
com.airdoc.mpd:attr/materialSearchViewPrefixStyle = 0x7f04031f
com.airdoc.mpd:color/design_default_color_on_secondary = 0x7f060063
com.airdoc.mpd:drawable/ic_scan_anim = 0x7f080141
com.airdoc.mpd:attr/colorOnPrimary = 0x7f04010b
com.airdoc.mpd:attr/cornerSizeBottomLeft = 0x7f04015f
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary95 = 0x7f06010f
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary80 = 0x7f06010d
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f130277
com.airdoc.mpd:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f070156
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary30 = 0x7f060108
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary10 = 0x7f060105
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary95 = 0x7f060102
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary80 = 0x7f060100
com.airdoc.mpd:dimen/mtrl_badge_horizontal_edge_offset = 0x7f070267
com.airdoc.mpd:attr/titleMargin = 0x7f0404d4
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary60 = 0x7f0600fe
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f13014a
com.airdoc.mpd:id/on = 0x7f0a01cb
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary50 = 0x7f0600fd
com.airdoc.mpd:dimen/exo_error_message_text_size = 0x7f070094
com.airdoc.mpd:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1302da
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary100 = 0x7f0600f9
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary10 = 0x7f0600f8
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary0 = 0x7f0600f7
com.airdoc.mpd:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1303fc
com.airdoc.mpd:attr/buttonBarStyle = 0x7f040097
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary95 = 0x7f0600f5
com.airdoc.mpd:macro/m3_comp_extended_fab_primary_container_color = 0x7f0e002d
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary60 = 0x7f0600f1
com.airdoc.mpd:style/Theme.Material3.Light.BottomSheetDialog = 0x7f130265
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary50 = 0x7f0600f0
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary30 = 0x7f0600ee
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary10 = 0x7f0600eb
com.airdoc.mpd:layout/fragment_detection_code_detection = 0x7f0d0048
com.airdoc.mpd:attr/contentPaddingEnd = 0x7f04014d
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0600e7
com.airdoc.mpd:attr/contentPaddingBottom = 0x7f04014c
com.airdoc.mpd:attr/chipIcon = 0x7f0400cb
com.airdoc.mpd:attr/textAppearanceDisplayMedium = 0x7f040479
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral99 = 0x7f0600dc
com.airdoc.mpd:dimen/mtrl_card_dragged_z = 0x7f0702ba
com.airdoc.mpd:attr/borderRoundPercent = 0x7f04007d
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0600e6
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0600e4
com.airdoc.mpd:attr/motionDurationExtraLong1 = 0x7f040344
com.airdoc.mpd:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0601b4
com.airdoc.mpd:color/mtrl_tabs_ripple_color = 0x7f0602f0
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0600e1
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0600df
com.airdoc.mpd:id/invisible = 0x7f0a013a
com.airdoc.mpd:attr/clickAction = 0x7f0400e4
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral98 = 0x7f0600db
com.airdoc.mpd:attr/layout_constraintBaseline_toBaselineOf = 0x7f040289
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral96 = 0x7f0600da
com.airdoc.mpd:attr/clockFaceBackgroundColor = 0x7f0400e5
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral95 = 0x7f0600d9
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral94 = 0x7f0600d8
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral87 = 0x7f0600d5
com.airdoc.mpd:id/snackbar_text = 0x7f0a022e
com.airdoc.mpd:attr/roundWidth = 0x7f0403d2
com.airdoc.mpd:color/background_material_dark = 0x7f06001f
com.airdoc.mpd:drawable/common_black_50_round_60_bg = 0x7f080086
com.airdoc.mpd:dimen/material_clock_number_text_size = 0x7f070245
com.airdoc.mpd:style/Widget.Material3.Button.IconButton.Filled = 0x7f13037a
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral60 = 0x7f0600d2
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral6 = 0x7f0600d1
com.airdoc.mpd:attr/motionInterpolator = 0x7f040368
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral40 = 0x7f0600cf
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_primary = 0x7f0601a4
com.airdoc.mpd:raw/version = 0x7f110013
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral24 = 0x7f0600cc
com.airdoc.mpd:layout/select_dialog_multichoice_material = 0x7f0d0094
com.airdoc.mpd:attr/initialActivityCount = 0x7f04024e
com.airdoc.mpd:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f08007e
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral22 = 0x7f0600cb
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral20 = 0x7f0600ca
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog.Alert = 0x7f130281
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0e0090
com.airdoc.mpd:dimen/mtrl_progress_track_thickness = 0x7f0702fd
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral17 = 0x7f0600c9
com.airdoc.mpd:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080082
com.airdoc.mpd:attr/reverseLayout = 0x7f0403cb
com.airdoc.mpd:dimen/mtrl_snackbar_background_corner_radius = 0x7f07030c
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral100 = 0x7f0600c7
com.airdoc.mpd:id/is_pooling_container_tag = 0x7f0a013c
com.airdoc.mpd:string/str_whatsapp_scan = 0x7f120134
com.airdoc.mpd:dimen/mtrl_btn_corner_radius = 0x7f070275
com.airdoc.mpd:color/m3_popupmenu_overlay_color = 0x7f0600c0
com.airdoc.mpd:attr/titleTextEllipsize = 0x7f0404dd
com.airdoc.mpd:color/m3_navigation_rail_ripple_color_selector = 0x7f0600bf
com.airdoc.mpd:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0600be
com.airdoc.mpd:attr/onTouchUp = 0x7f040384
com.airdoc.mpd:dimen/compat_button_padding_horizontal_material = 0x7f070058
com.airdoc.mpd:attr/expandedTitleTextAppearance = 0x7f0401cd
com.airdoc.mpd:id/fill_horizontal = 0x7f0a010a
com.airdoc.mpd:interpolator/mtrl_fast_out_linear_in = 0x7f0c000e
com.airdoc.mpd:color/m3_tabs_text_color_secondary = 0x7f060210
com.airdoc.mpd:color/dim_foreground_disabled_material_dark = 0x7f060075
com.airdoc.mpd:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0e00da
com.airdoc.mpd:color/m3_hint_foreground = 0x7f0600b4
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary50 = 0x7f06010a
com.airdoc.mpd:color/abc_tint_spinner = 0x7f060017
com.airdoc.mpd:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0700e2
com.airdoc.mpd:attr/lottie_autoPlay = 0x7f0402e1
com.airdoc.mpd:color/m3_fab_ripple_color_selector = 0x7f0600b1
com.airdoc.mpd:attr/shapeAppearanceSmallComponent = 0x7f0403f2
com.airdoc.mpd:color/m3_fab_efab_background_color_selector = 0x7f0600af
com.airdoc.mpd:dimen/material_clock_period_toggle_horizontal_gap = 0x7f070247
com.airdoc.mpd:id/ignore = 0x7f0a0133
com.airdoc.mpd:attr/endIconContentDescription = 0x7f0401b0
com.airdoc.mpd:color/m3_dynamic_hint_foreground = 0x7f0600ab
com.airdoc.mpd:color/m3_dynamic_dark_hint_foreground = 0x7f0600a6
com.airdoc.mpd:color/design_dark_default_color_error = 0x7f060052
com.airdoc.mpd:color/m3_dynamic_dark_highlighted_text = 0x7f0600a5
com.airdoc.mpd:attr/textInputFilledExposedDropdownMenuStyle = 0x7f0404a1
com.airdoc.mpd:color/m3_dynamic_dark_default_color_primary_text = 0x7f0600a3
com.airdoc.mpd:attr/itemShapeInsetTop = 0x7f040268
com.airdoc.mpd:drawable/ic_detection_hrv_bg = 0x7f08011d
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1300e4
com.airdoc.mpd:color/m3_dark_default_color_secondary_text = 0x7f06009d
com.airdoc.mpd:string/material_motion_easing_emphasized = 0x7f120098
com.airdoc.mpd:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f070256
com.airdoc.mpd:drawable/mtrl_dialog_background = 0x7f08018a
com.airdoc.mpd:color/m3_radiobutton_button_tint = 0x7f0600c2
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0601cb
com.airdoc.mpd:dimen/design_snackbar_action_text_color_alpha = 0x7f07007f
com.airdoc.mpd:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.airdoc.mpd:color/tooltip_background_dark = 0x7f060317
com.airdoc.mpd:attr/fastScrollHorizontalTrackDrawable = 0x7f0401e2
com.airdoc.mpd:dimen/abc_dialog_title_divider_material = 0x7f070026
com.airdoc.mpd:color/m3_chip_text_color = 0x7f06009b
com.airdoc.mpd:attr/percentWidth = 0x7f04039b
com.airdoc.mpd:layout/notification_template_custom_big = 0x7f0d008c
com.airdoc.mpd:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f0701b9
com.airdoc.mpd:attr/layout_goneMarginLeft = 0x7f0402ba
com.airdoc.mpd:drawable/exo_ic_fullscreen_exit = 0x7f0800c5
com.airdoc.mpd:color/m3_chip_stroke_color = 0x7f06009a
com.airdoc.mpd:color/m3_card_ripple_color = 0x7f060093
com.airdoc.mpd:macro/m3_comp_date_picker_modal_container_shape = 0x7f0e000f
com.airdoc.mpd:color/m3_card_foreground_color = 0x7f060092
com.airdoc.mpd:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0e004a
com.airdoc.mpd:color/m3_button_ripple_color = 0x7f06008e
com.airdoc.mpd:color/m3_button_outline_color_selector = 0x7f06008d
com.airdoc.mpd:attr/ad_marker_color = 0x7f040028
com.airdoc.mpd:drawable/googleg_disabled_color_18 = 0x7f08010d
com.airdoc.mpd:drawable/exo_ic_play_circle_filled = 0x7f0800c7
com.airdoc.mpd:attr/checkedButton = 0x7f0400bc
com.airdoc.mpd:attr/windowMinWidthMajor = 0x7f040523
com.airdoc.mpd:color/m3_assist_chip_stroke_color = 0x7f060089
com.airdoc.mpd:color/m3_ref_palette_neutral_variant40 = 0x7f06013b
com.airdoc.mpd:color/highlighted_text_material_dark = 0x7f060085
com.airdoc.mpd:dimen/m3_sys_elevation_level1 = 0x7f07020d
com.airdoc.mpd:attr/barrierDirection = 0x7f04006d
com.airdoc.mpd:attr/implementationMode = 0x7f040246
com.airdoc.mpd:attr/flow_verticalStyle = 0x7f040206
com.airdoc.mpd:color/foreground_material_dark = 0x7f060083
com.airdoc.mpd:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f130398
com.airdoc.mpd:color/exo_styled_error_message_background = 0x7f060080
com.airdoc.mpd:attr/floatingActionButtonTertiaryStyle = 0x7f0401f4
com.airdoc.mpd:color/exo_error_message_background_color = 0x7f06007f
com.airdoc.mpd:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0e00b7
com.airdoc.mpd:attr/tickMarkTintMode = 0x7f0404c8
com.airdoc.mpd:layout/material_clock_period_toggle = 0x7f0d0059
com.airdoc.mpd:attr/hintTextColor = 0x7f04022e
com.airdoc.mpd:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f130483
com.airdoc.mpd:attr/windowMinWidthMinor = 0x7f040524
com.airdoc.mpd:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f1301ad
com.airdoc.mpd:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0e00bf
com.airdoc.mpd:attr/percentY = 0x7f04039d
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0e015e
com.airdoc.mpd:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0e015a
com.airdoc.mpd:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0601d6
com.airdoc.mpd:attr/layout_constraintBottom_toBottomOf = 0x7f04028d
com.airdoc.mpd:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0e011f
com.airdoc.mpd:id/allStates = 0x7f0a004d
com.airdoc.mpd:attr/cardBackgroundColor = 0x7f0400a4
com.airdoc.mpd:style/Widget.Material3.BottomSheet.Modal = 0x7f130374
com.airdoc.mpd:string/str_startup_detection_failed_check_network_and_retry = 0x7f120128
com.airdoc.mpd:color/m3_dynamic_dark_default_color_secondary_text = 0x7f0600a4
com.airdoc.mpd:integer/mtrl_view_visible = 0x7f0b0044
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0600e8
com.airdoc.mpd:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.airdoc.mpd:string/exo_controls_play_description = 0x7f12004e
com.airdoc.mpd:attr/collapsingToolbarLayoutLargeStyle = 0x7f0400f8
com.airdoc.mpd:color/design_fab_stroke_top_outer_color = 0x7f060072
com.airdoc.mpd:drawable/common_google_signin_btn_icon_light = 0x7f080097
com.airdoc.mpd:attr/materialButtonToggleGroupStyle = 0x7f040301
com.airdoc.mpd:dimen/mtrl_high_ripple_focused_alpha = 0x7f0702d7
com.airdoc.mpd:attr/autoCompleteMode = 0x7f040041
com.airdoc.mpd:attr/layout_constrainedHeight = 0x7f040286
com.airdoc.mpd:color/design_fab_shadow_start_color = 0x7f06006e
com.airdoc.mpd:attr/springDamping = 0x7f04041d
com.airdoc.mpd:color/design_error = 0x7f06006b
com.airdoc.mpd:attr/number = 0x7f04037b
com.airdoc.mpd:attr/toolbarSurfaceStyle = 0x7f0404e3
com.airdoc.mpd:color/design_default_color_secondary = 0x7f060068
com.airdoc.mpd:id/tv_language = 0x7f0a0294
com.airdoc.mpd:layout/mtrl_picker_header_title_text = 0x7f0d007e
com.airdoc.mpd:attr/dragDirection = 0x7f040191
com.airdoc.mpd:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f07018b
com.airdoc.mpd:color/design_default_color_primary_variant = 0x7f060067
com.airdoc.mpd:attr/materialCalendarHeaderLayout = 0x7f040308
com.airdoc.mpd:dimen/mtrl_badge_size = 0x7f070269
com.airdoc.mpd:color/design_default_color_primary = 0x7f060065
com.airdoc.mpd:dimen/m3_btn_icon_only_default_size = 0x7f0700f7
com.airdoc.mpd:color/m3_dynamic_default_color_primary_text = 0x7f0600a8
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f070169
com.airdoc.mpd:style/ExoMediaButton.FastForward = 0x7f130124
com.airdoc.mpd:color/material_personalized_color_control_activated = 0x7f060284
com.airdoc.mpd:drawable/m3_popupmenu_background_overlay = 0x7f08016c
com.airdoc.mpd:attr/chipCornerRadius = 0x7f0400c8
com.airdoc.mpd:attr/layout_constraintStart_toEndOf = 0x7f0402a6
com.airdoc.mpd:dimen/exo_settings_text_height = 0x7f0700a2
com.airdoc.mpd:attr/layout_constraintWidth = 0x7f0402af
com.airdoc.mpd:attr/actionLayout = 0x7f04000f
com.airdoc.mpd:color/design_default_color_on_primary = 0x7f060062
com.airdoc.mpd:color/primary_material_dark = 0x7f0602fc
com.airdoc.mpd:dimen/material_helper_text_font_1_3_padding_top = 0x7f07025a
com.airdoc.mpd:color/design_default_color_background = 0x7f06005e
com.airdoc.mpd:attr/textAppearanceBodySmall = 0x7f040475
com.airdoc.mpd:color/design_dark_default_color_on_error = 0x7f060054
com.airdoc.mpd:attr/flow_horizontalGap = 0x7f0401fb
com.airdoc.mpd:attr/actionModeCopyDrawable = 0x7f040016
com.airdoc.mpd:color/design_dark_default_color_background = 0x7f060051
com.airdoc.mpd:layout/custom_dialog = 0x7f0d0027
com.airdoc.mpd:color/common_google_signin_btn_text_dark_pressed = 0x7f060048
com.airdoc.mpd:string/common_open_on_phone = 0x7f12003e
com.airdoc.mpd:color/common_google_signin_btn_text_dark_default = 0x7f060045
com.airdoc.mpd:attr/carousel_infinite = 0x7f0400b0
com.airdoc.mpd:attr/visibilityMode = 0x7f040513
com.airdoc.mpd:color/color_cccccc = 0x7f060041
com.airdoc.mpd:color/color_666666 = 0x7f06003e
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f130425
com.airdoc.mpd:color/button_material_dark = 0x7f060033
com.airdoc.mpd:style/Widget.Material3.BottomSheet = 0x7f130372
com.airdoc.mpd:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0e00fe
com.airdoc.mpd:attr/flow_firstVerticalBias = 0x7f0401f7
com.airdoc.mpd:color/bright_foreground_disabled_material_light = 0x7f06002e
com.airdoc.mpd:attr/multiChoiceItemLayout = 0x7f040371
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1302ef
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0e0097
com.airdoc.mpd:drawable/mtrl_ic_arrow_drop_up = 0x7f08018d
com.airdoc.mpd:color/black_80 = 0x7f06002b
com.airdoc.mpd:attr/collapseIcon = 0x7f0400f2
com.airdoc.mpd:color/black_70 = 0x7f06002a
com.airdoc.mpd:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0701e9
com.airdoc.mpd:attr/titleTextAppearance = 0x7f0404db
com.airdoc.mpd:color/cardview_dark_background = 0x7f060037
com.airdoc.mpd:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1302c0
com.airdoc.mpd:attr/animationMode = 0x7f040037
com.airdoc.mpd:drawable/mtrl_switch_thumb_checked_pressed = 0x7f080199
com.airdoc.mpd:attr/placeholderText = 0x7f0403a0
com.airdoc.mpd:id/et_name = 0x7f0a00cf
com.airdoc.mpd:attr/autoStartMarquee = 0x7f040049
com.airdoc.mpd:dimen/m3_comp_slider_inactive_track_height = 0x7f0701a5
com.airdoc.mpd:style/TextAppearance.Design.Placeholder = 0x7f1301f7
com.airdoc.mpd:color/background_floating_material_light = 0x7f06001e
com.airdoc.mpd:attr/errorTextColor = 0x7f0401c3
com.airdoc.mpd:attr/simpleItemSelectedColor = 0x7f04040c
com.airdoc.mpd:attr/actionModeStyle = 0x7f04001e
com.airdoc.mpd:color/common_google_signin_btn_text_light = 0x7f060049
com.airdoc.mpd:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.airdoc.mpd:color/m3_checkbox_button_icon_tint = 0x7f060095
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0600e9
com.airdoc.mpd:color/design_default_color_on_surface = 0x7f060064
com.airdoc.mpd:attr/colorSecondaryVariant = 0x7f04012a
com.airdoc.mpd:color/material_personalized_color_surface_bright = 0x7f0602a1
com.airdoc.mpd:color/androidx_core_ripple_material_light = 0x7f06001b
com.airdoc.mpd:anim/m3_motion_fade_enter = 0x7f010024
com.airdoc.mpd:color/abc_tint_seek_thumb = 0x7f060016
com.airdoc.mpd:color/material_on_background_emphasis_high_type = 0x7f060278
com.airdoc.mpd:id/np_select_age = 0x7f0a01c9
com.airdoc.mpd:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.airdoc.mpd:drawable/notification_bg = 0x7f0801a6
com.airdoc.mpd:attr/thumbIconTint = 0x7f0404bb
com.airdoc.mpd:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f080028
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1301e3
com.airdoc.mpd:macro/m3_comp_dialog_container_color = 0x7f0e0023
com.airdoc.mpd:animator/design_fab_hide_motion_spec = 0x7f020001
com.airdoc.mpd:color/mtrl_filled_stroke_color = 0x7f0602d9
com.airdoc.mpd:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.airdoc.mpd:color/abc_hint_foreground_material_dark = 0x7f060007
com.airdoc.mpd:attr/motionPathRotate = 0x7f04036a
com.airdoc.mpd:dimen/material_clock_hand_stroke_width = 0x7f070244
com.airdoc.mpd:color/abc_decor_view_status_guard = 0x7f060005
com.airdoc.mpd:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f080025
com.airdoc.mpd:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f070176
com.airdoc.mpd:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f130061
com.airdoc.mpd:attr/titleMarginStart = 0x7f0404d7
com.airdoc.mpd:id/rv_language = 0x7f0a0209
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1303d0
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f070217
com.airdoc.mpd:attr/alpha = 0x7f040030
com.airdoc.mpd:bool/enable_system_alarm_service_default = 0x7f050002
com.airdoc.mpd:attr/chipStrokeColor = 0x7f0400d7
com.airdoc.mpd:attr/materialDividerStyle = 0x7f040319
com.airdoc.mpd:attr/viewTransitionOnPositiveCross = 0x7f040512
com.airdoc.mpd:color/common_google_signin_btn_text_dark = 0x7f060044
com.airdoc.mpd:style/TextAppearance.Compat.Notification.Line2 = 0x7f1301ea
com.airdoc.mpd:id/ll_collector_number = 0x7f0a0169
com.airdoc.mpd:color/m3_ref_palette_primary0 = 0x7f060143
com.airdoc.mpd:attr/windowFixedWidthMinor = 0x7f040522
com.airdoc.mpd:layout/mtrl_picker_actions = 0x7f0d0078
com.airdoc.mpd:id/navigation_header_container = 0x7f0a01bd
com.airdoc.mpd:animator/fragment_open_exit = 0x7f020008
com.airdoc.mpd:color/black = 0x7f060021
com.airdoc.mpd:color/color_999999 = 0x7f06003f
com.airdoc.mpd:color/material_dynamic_tertiary70 = 0x7f060267
com.airdoc.mpd:attr/motionEasingDecelerated = 0x7f040355
com.airdoc.mpd:color/white = 0x7f060319
com.airdoc.mpd:attr/isMaterial3DynamicColorApplied = 0x7f040251
com.airdoc.mpd:color/design_default_color_on_error = 0x7f060061
com.airdoc.mpd:style/Theme.MaterialComponents.Dialog = 0x7f130280
com.airdoc.mpd:attr/shapeAppearanceOverlay = 0x7f0403f1
com.airdoc.mpd:attr/brightness = 0x7f040091
com.airdoc.mpd:attr/actionBarSize = 0x7f040005
com.airdoc.mpd:attr/waveVariesBy = 0x7f04051b
com.airdoc.mpd:attr/textPanY = 0x7f0404ac
com.airdoc.mpd:attr/ifTagNotSet = 0x7f04023d
com.airdoc.mpd:attr/shrinkMotionSpec = 0x7f040407
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f130183
com.airdoc.mpd:color/design_dark_default_color_secondary = 0x7f06005b
com.airdoc.mpd:attr/tabInlineLabel = 0x7f040459
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0e00ce
com.airdoc.mpd:color/material_dynamic_secondary20 = 0x7f060255
com.airdoc.mpd:attr/wavePhase = 0x7f040519
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0e0167
com.airdoc.mpd:drawable/ic_m3_chip_checked_circle = 0x7f080130
com.airdoc.mpd:attr/cardUseCompatPadding = 0x7f0400aa
com.airdoc.mpd:id/view_code = 0x7f0a02b2
com.airdoc.mpd:id/tv_scan_method = 0x7f0a029c
com.airdoc.mpd:dimen/design_bottom_navigation_item_max_width = 0x7f070065
com.airdoc.mpd:string/str_please_enter_phone = 0x7f12011f
com.airdoc.mpd:layout/fragment_scan_code_detection = 0x7f0d004b
com.airdoc.mpd:attr/fastScrollEnabled = 0x7f0401e0
com.airdoc.mpd:attr/errorAccessibilityLiveRegion = 0x7f0401bb
com.airdoc.mpd:color/design_default_color_on_background = 0x7f060060
com.airdoc.mpd:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.airdoc.mpd:attr/useDrawerArrowDrawable = 0x7f040507
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0601b1
com.airdoc.mpd:attr/motionDurationShort2 = 0x7f040351
com.airdoc.mpd:id/switch_display_viewpoint = 0x7f0a024f
com.airdoc.mpd:attr/ttcIndex = 0x7f040502
com.airdoc.mpd:attr/checkedIconTint = 0x7f0400c3
com.airdoc.mpd:color/abc_tint_btn_checkable = 0x7f060013
com.airdoc.mpd:attr/textAppearanceBody1 = 0x7f040471
com.airdoc.mpd:attr/triggerSlack = 0x7f040501
com.airdoc.mpd:layout/material_radial_view_group = 0x7f0d005d
com.airdoc.mpd:attr/layout_constraintWidth_max = 0x7f0402b1
com.airdoc.mpd:attr/cursorErrorColor = 0x7f04016c
com.airdoc.mpd:drawable/common_black_20_round_bg = 0x7f080085
com.airdoc.mpd:attr/tabSelectedTextAppearance = 0x7f040464
com.airdoc.mpd:attr/useCompatPadding = 0x7f040506
com.airdoc.mpd:attr/layout_constrainedWidth = 0x7f040287
com.airdoc.mpd:color/material_dynamic_secondary60 = 0x7f060259
com.airdoc.mpd:attr/transformPivotTarget = 0x7f0404f9
com.airdoc.mpd:attr/constraintRotate = 0x7f04013c
com.airdoc.mpd:attr/trackCornerRadius = 0x7f0404f1
com.airdoc.mpd:attr/layout_collapseMode = 0x7f040284
com.airdoc.mpd:attr/dropDownListViewStyle = 0x7f0401a2
com.airdoc.mpd:color/call_notification_answer_color = 0x7f060035
com.airdoc.mpd:attr/colorBackgroundFloating = 0x7f0400fe
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral70 = 0x7f0600d3
com.airdoc.mpd:attr/trackColorActive = 0x7f0404ef
com.airdoc.mpd:attr/carousel_nextState = 0x7f0400b1
com.airdoc.mpd:drawable/abc_list_selector_disabled_holo_dark = 0x7f080055
com.airdoc.mpd:attr/trackColor = 0x7f0404ee
com.airdoc.mpd:id/action_bar_activity_content = 0x7f0a0035
com.airdoc.mpd:attr/logo = 0x7f0402dc
com.airdoc.mpd:attr/textAppearanceBodyMedium = 0x7f040474
com.airdoc.mpd:macro/m3_comp_radio_button_selected_icon_color = 0x7f0e00dc
com.airdoc.mpd:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0702e2
com.airdoc.mpd:string/common_google_play_services_notification_ticker = 0x7f120036
com.airdoc.mpd:dimen/abc_text_size_display_2_material = 0x7f070044
com.airdoc.mpd:attr/touchAnchorId = 0x7f0404e9
com.airdoc.mpd:attr/materialAlertDialogTheme = 0x7f0402fb
com.airdoc.mpd:attr/tooltipText = 0x7f0404e7
com.airdoc.mpd:color/m3_sys_color_dark_surface_dim = 0x7f060191
com.airdoc.mpd:bool/workmanager_test_configuration = 0x7f050006
com.airdoc.mpd:attr/firstBaselineToTopHeight = 0x7f0401e5
com.airdoc.mpd:string/str_view_report = 0x7f120131
com.airdoc.mpd:attr/imageZoom = 0x7f040245
com.airdoc.mpd:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0e00c1
com.airdoc.mpd:dimen/design_navigation_max_width = 0x7f07007b
com.airdoc.mpd:macro/m3_comp_switch_selected_icon_color = 0x7f0e012a
com.airdoc.mpd:color/design_fab_shadow_end_color = 0x7f06006c
com.airdoc.mpd:attr/tooltipForegroundColor = 0x7f0404e4
com.airdoc.mpd:id/cl_loading_root = 0x7f0a0083
com.airdoc.mpd:dimen/abc_button_inset_vertical_material = 0x7f070013
com.airdoc.mpd:attr/toolbarNavigationButtonStyle = 0x7f0404e1
com.airdoc.mpd:attr/endIconMinSize = 0x7f0401b2
com.airdoc.mpd:attr/toolbarId = 0x7f0404e0
com.airdoc.mpd:style/Base.Theme.AppCompat = 0x7f13004c
com.airdoc.mpd:dimen/notification_action_text_size = 0x7f070328
com.airdoc.mpd:attr/flow_horizontalBias = 0x7f0401fa
com.airdoc.mpd:attr/keyPositionType = 0x7f040273
com.airdoc.mpd:attr/titleTextStyle = 0x7f0404de
com.airdoc.mpd:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f07019e
com.airdoc.mpd:attr/show_next_button = 0x7f040400
com.airdoc.mpd:attr/titleMargins = 0x7f0404d9
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Item = 0x7f1303ca
com.airdoc.mpd:attr/titleEnabled = 0x7f0404d3
com.airdoc.mpd:attr/overlapAnchor = 0x7f040385
com.airdoc.mpd:dimen/m3_timepicker_display_stroke_width = 0x7f07023a
com.airdoc.mpd:color/material_deep_teal_200 = 0x7f060228
com.airdoc.mpd:attr/viewInflaterClass = 0x7f04050e
com.airdoc.mpd:attr/titleCollapseMode = 0x7f0404d2
com.airdoc.mpd:attr/titleCentered = 0x7f0404d1
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f13027f
com.airdoc.mpd:attr/customNavigationLayout = 0x7f040174
com.airdoc.mpd:attr/tintMode = 0x7f0404ce
com.airdoc.mpd:string/path_password_eye_mask_strike_through = 0x7f1200e5
com.airdoc.mpd:color/m3_ref_palette_neutral_variant0 = 0x7f060136
com.airdoc.mpd:attr/tint = 0x7f0404cd
com.airdoc.mpd:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.airdoc.mpd:attr/listPreferredItemPaddingStart = 0x7f0402db
com.airdoc.mpd:drawable/common_white_round_bg = 0x7f0800b5
com.airdoc.mpd:attr/iconSize = 0x7f040238
com.airdoc.mpd:attr/tickVisible = 0x7f0404cb
com.airdoc.mpd:dimen/m3_badge_with_text_offset = 0x7f0700db
com.airdoc.mpd:string/material_timepicker_minute = 0x7f1200a1
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f130170
com.airdoc.mpd:attr/buttonPanelSideLayout = 0x7f04009e
com.airdoc.mpd:attr/triggerReceiver = 0x7f040500
com.airdoc.mpd:color/m3_dynamic_primary_text_disable_only = 0x7f0600ac
com.airdoc.mpd:attr/thumbStrokeWidth = 0x7f0404bf
com.airdoc.mpd:anim/abc_tooltip_exit = 0x7f01000b
com.airdoc.mpd:macro/m3_comp_switch_selected_track_color = 0x7f0e012f
com.airdoc.mpd:attr/textAppearanceDisplaySmall = 0x7f04047a
com.airdoc.mpd:attr/thumbIconTintMode = 0x7f0404bc
com.airdoc.mpd:attr/thumbIcon = 0x7f0404b9
com.airdoc.mpd:dimen/exo_styled_bottom_bar_time_padding = 0x7f0700aa
com.airdoc.mpd:attr/layout_constraintLeft_toRightOf = 0x7f0402a2
com.airdoc.mpd:attr/thumbElevation = 0x7f0404b8
com.airdoc.mpd:attr/lastBaselineToBottomHeight = 0x7f04027b
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Subhead = 0x7f13002e
com.airdoc.mpd:attr/colorControlNormal = 0x7f040103
com.airdoc.mpd:attr/customDimension = 0x7f040171
com.airdoc.mpd:style/Widget.Material3.ChipGroup = 0x7f130396
com.airdoc.mpd:color/material_personalized_color_surface_variant = 0x7f0602a9
com.airdoc.mpd:dimen/highlight_alpha_material_dark = 0x7f0700b7
com.airdoc.mpd:id/noScroll = 0x7f0a01c1
com.airdoc.mpd:attr/expandActivityOverflowButtonDrawable = 0x7f0401c4
com.airdoc.mpd:attr/textStyle = 0x7f0404af
com.airdoc.mpd:attr/shapeAppearanceCornerMedium = 0x7f0403ed
com.airdoc.mpd:id/media_controller_compat_view_tag = 0x7f0a0193
com.airdoc.mpd:dimen/abc_progress_bar_height_material = 0x7f070035
com.airdoc.mpd:drawable/common_red_round_bg = 0x7f0800aa
com.airdoc.mpd:color/design_dark_default_color_primary = 0x7f060058
com.airdoc.mpd:color/m3_card_stroke_color = 0x7f060094
com.airdoc.mpd:attr/textStartPadding = 0x7f0404ae
com.airdoc.mpd:attr/keep_content_on_player_reset = 0x7f040272
com.airdoc.mpd:dimen/m3_btn_stroke_size = 0x7f070100
com.airdoc.mpd:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f0404a5
com.airdoc.mpd:attr/counterOverflowTextColor = 0x7f040166
com.airdoc.mpd:string/str_more_settings = 0x7f120115
com.airdoc.mpd:attr/textInputLayoutFocusedRectEnabled = 0x7f0404a3
com.airdoc.mpd:attr/textFillColor = 0x7f04049f
com.airdoc.mpd:layout/abc_select_dialog_material = 0x7f0d001a
com.airdoc.mpd:attr/textInputOutlinedDenseStyle = 0x7f0404a4
com.airdoc.mpd:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f07013c
com.airdoc.mpd:attr/region_heightMoreThan = 0x7f0403c5
com.airdoc.mpd:id/frost = 0x7f0a011c
com.airdoc.mpd:color/accent_material_dark = 0x7f060019
com.airdoc.mpd:dimen/mtrl_btn_text_size = 0x7f07028b
com.airdoc.mpd:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1300f4
com.airdoc.mpd:attr/tickColorActive = 0x7f0404c4
com.airdoc.mpd:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f08000d
com.airdoc.mpd:color/cardview_light_background = 0x7f060038
com.airdoc.mpd:attr/textColor = 0x7f04049b
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f13019e
com.airdoc.mpd:attr/textBackground = 0x7f040496
com.airdoc.mpd:attr/motionDurationLong3 = 0x7f04034a
com.airdoc.mpd:style/Widget.AppCompat.ActionBar.Solid = 0x7f13030f
com.airdoc.mpd:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0e0169
com.airdoc.mpd:attr/maxActionInlineWidth = 0x7f04032a
com.airdoc.mpd:dimen/mtrl_calendar_title_baseline_to_top = 0x7f0702b0
com.airdoc.mpd:integer/mtrl_calendar_year_selector_span = 0x7f0b0035
com.airdoc.mpd:color/mtrl_navigation_item_background_color = 0x7f0602df
com.airdoc.mpd:style/Widget.AppCompat.TextView = 0x7f130353
com.airdoc.mpd:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0e00d6
com.airdoc.mpd:attr/textAppearanceTitleMedium = 0x7f040494
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f06019f
com.airdoc.mpd:attr/motionEasingLinearInterpolator = 0x7f04035b
com.airdoc.mpd:attr/switchPadding = 0x7f04044a
com.airdoc.mpd:style/Platform.V25.AppCompat.Light = 0x7f130163
com.airdoc.mpd:layout/activity_more_settings = 0x7f0d0023
com.airdoc.mpd:id/textStart = 0x7f0a0265
com.airdoc.mpd:attr/textAppearanceSmallPopupMenu = 0x7f040490
com.airdoc.mpd:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010013
com.airdoc.mpd:color/primary_material_light = 0x7f0602fd
com.airdoc.mpd:attr/textAppearanceSearchResultSubtitle = 0x7f04048e
com.airdoc.mpd:integer/cancel_button_image_alpha = 0x7f0b0004
com.airdoc.mpd:color/m3_dynamic_dark_primary_text_disable_only = 0x7f0600a7
com.airdoc.mpd:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0e009c
com.airdoc.mpd:attr/motionDurationLong1 = 0x7f040348
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f130440
com.airdoc.mpd:attr/textureWidth = 0x7f0404b3
com.airdoc.mpd:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0e016c
com.airdoc.mpd:id/transition_layout_save = 0x7f0a027c
com.airdoc.mpd:attr/textAppearancePopupMenuHeader = 0x7f04048d
com.airdoc.mpd:attr/buttonIconTintMode = 0x7f04009d
com.airdoc.mpd:dimen/compat_button_padding_vertical_material = 0x7f070059
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral30 = 0x7f0600cd
com.airdoc.mpd:attr/buttonStyle = 0x7f0400a0
com.airdoc.mpd:anim/m3_side_sheet_enter_from_left = 0x7f010026
com.airdoc.mpd:attr/backgroundStacked = 0x7f040055
com.airdoc.mpd:attr/windowActionBarOverlay = 0x7f04051d
com.airdoc.mpd:color/design_dark_default_color_primary_dark = 0x7f060059
com.airdoc.mpd:string/call_notification_hang_up_action = 0x7f120027
com.airdoc.mpd:bool/enable_system_job_service_default = 0x7f050004
com.airdoc.mpd:id/date_picker_actions = 0x7f0a00a2
com.airdoc.mpd:attr/textAppearanceLabelLarge = 0x7f040484
com.airdoc.mpd:color/mtrl_switch_track_tint = 0x7f0602eb
com.airdoc.mpd:attr/subheaderInsetEnd = 0x7f04043b
com.airdoc.mpd:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.airdoc.mpd:styleable/ViewStubCompat = 0x7f1400a1
com.airdoc.mpd:color/abc_tint_default = 0x7f060014
com.airdoc.mpd:color/m3_textfield_label_color = 0x7f060217
com.airdoc.mpd:style/Widget.MaterialComponents.Button.Icon = 0x7f13041d
com.airdoc.mpd:id/text_input_end_icon = 0x7f0a0267
com.airdoc.mpd:attr/checkMarkTintMode = 0x7f0400ba
com.airdoc.mpd:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0e0142
com.airdoc.mpd:id/navigation_bar_item_active_indicator_view = 0x7f0a01b7
com.airdoc.mpd:attr/chipMinHeight = 0x7f0400d0
com.airdoc.mpd:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f080027
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f13028f
com.airdoc.mpd:dimen/m3_searchbar_text_size = 0x7f0701fc
com.airdoc.mpd:id/material_clock_period_toggle = 0x7f0a0184
com.airdoc.mpd:attr/curveFit = 0x7f04016d
com.airdoc.mpd:color/color_primary = 0x7f060042
com.airdoc.mpd:attr/dayStyle = 0x7f04017a
com.airdoc.mpd:attr/textAppearanceHeadline2 = 0x7f04047c
com.airdoc.mpd:layout/mtrl_calendar_month = 0x7f0d006f
com.airdoc.mpd:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1300d6
com.airdoc.mpd:attr/textAppearance = 0x7f040470
com.airdoc.mpd:attr/bottomInsetScrimEnabled = 0x7f040081
com.airdoc.mpd:attr/verticalOffset = 0x7f04050c
com.airdoc.mpd:dimen/exo_small_icon_width = 0x7f0700a7
com.airdoc.mpd:dimen/mtrl_slider_thumb_radius = 0x7f070306
com.airdoc.mpd:color/material_personalized_color_primary_inverse = 0x7f060299
com.airdoc.mpd:color/design_fab_stroke_end_outer_color = 0x7f060070
com.airdoc.mpd:attr/fontProviderPackage = 0x7f04020e
com.airdoc.mpd:style/Widget.Material3.Chip.Assist.Elevated = 0x7f13038d
com.airdoc.mpd:style/ThemeOverlay.AppCompat = 0x7f13029c
com.airdoc.mpd:styleable/RecycleListView = 0x7f14007d
com.airdoc.mpd:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080072
com.airdoc.mpd:dimen/exo_small_icon_horizontal_margin = 0x7f0700a4
com.airdoc.mpd:attr/telltales_velocityMode = 0x7f04046d
com.airdoc.mpd:attr/altSrc = 0x7f040032
com.airdoc.mpd:color/white_90 = 0x7f060324
com.airdoc.mpd:attr/telltales_tailScale = 0x7f04046c
com.airdoc.mpd:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0e0073
com.airdoc.mpd:attr/switchTextAppearance = 0x7f04044c
com.airdoc.mpd:color/m3_bottom_sheet_drag_handle_color = 0x7f06008a
com.airdoc.mpd:attr/textInputFilledStyle = 0x7f0404a2
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary90 = 0x7f060101
com.airdoc.mpd:attr/telltales_tailColor = 0x7f04046b
com.airdoc.mpd:attr/motionEasingStandardDecelerateInterpolator = 0x7f04035e
com.airdoc.mpd:dimen/mtrl_low_ripple_default_alpha = 0x7f0702da
com.airdoc.mpd:drawable/abc_list_focused_holo = 0x7f08004f
com.airdoc.mpd:dimen/compat_notification_large_icon_max_width = 0x7f07005c
com.airdoc.mpd:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0e010f
com.airdoc.mpd:color/abc_hint_foreground_material_light = 0x7f060008
com.airdoc.mpd:attr/defaultDuration = 0x7f04017c
com.airdoc.mpd:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f07014e
com.airdoc.mpd:attr/content = 0x7f040143
com.airdoc.mpd:layout/exo_player_control_view = 0x7f0d0042
com.airdoc.mpd:attr/tabTextAppearance = 0x7f040467
com.airdoc.mpd:attr/targetId = 0x7f04046a
com.airdoc.mpd:color/mtrl_btn_text_color_disabled = 0x7f0602c5
com.airdoc.mpd:attr/tabSelectedTextColor = 0x7f040465
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0e007a
com.airdoc.mpd:attr/thumbRadius = 0x7f0404bd
com.airdoc.mpd:attr/thumbTint = 0x7f0404c1
com.airdoc.mpd:color/m3_navigation_item_text_color = 0x7f0600bc
com.airdoc.mpd:style/MaterialAlertDialog.Material3 = 0x7f130143
com.airdoc.mpd:attr/tabIndicatorHeight = 0x7f040458
com.airdoc.mpd:dimen/exo_settings_main_text_size = 0x7f07009f
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0600dd
com.airdoc.mpd:attr/tabPaddingBottom = 0x7f04045e
com.airdoc.mpd:color/m3_ref_palette_neutral4 = 0x7f060127
com.airdoc.mpd:macro/m3_comp_navigation_drawer_headline_type = 0x7f0e0088
com.airdoc.mpd:id/rb_female = 0x7f0a01f7
com.airdoc.mpd:attr/tabIndicatorColor = 0x7f040455
com.airdoc.mpd:attr/tabIndicatorAnimationDuration = 0x7f040453
com.airdoc.mpd:attr/tabGravity = 0x7f04044f
com.airdoc.mpd:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f130068
com.airdoc.mpd:drawable/notification_bg_normal = 0x7f0801aa
com.airdoc.mpd:color/design_snackbar_background_color = 0x7f060074
com.airdoc.mpd:attr/tabContentStart = 0x7f04044e
com.airdoc.mpd:attr/buttonBarPositiveButtonStyle = 0x7f040096
com.airdoc.mpd:attr/trackDecoration = 0x7f0404f2
com.airdoc.mpd:attr/ifTagSet = 0x7f04023e
com.airdoc.mpd:attr/marginHorizontal = 0x7f0402f4
com.airdoc.mpd:attr/tabBackground = 0x7f04044d
com.airdoc.mpd:style/Base.Widget.AppCompat.ActionButton = 0x7f1300c8
com.airdoc.mpd:macro/m3_comp_snackbar_container_shape = 0x7f0e0115
com.airdoc.mpd:attr/switchStyle = 0x7f04044b
com.airdoc.mpd:dimen/m3_card_hovered_z = 0x7f07010e
com.airdoc.mpd:attr/elevationOverlayAccentColor = 0x7f0401aa
com.airdoc.mpd:attr/upDuration = 0x7f040505
com.airdoc.mpd:attr/paddingLeftSystemWindowInsets = 0x7f04038a
com.airdoc.mpd:color/m3_navigation_item_icon_tint = 0x7f0600ba
com.airdoc.mpd:attr/layout_wrapBehaviorInParent = 0x7f0402c5
com.airdoc.mpd:color/m3_ref_palette_error40 = 0x7f060116
com.airdoc.mpd:attr/surface_type = 0x7f040448
com.airdoc.mpd:string/mtrl_checkbox_button_path_name = 0x7f1200ac
com.airdoc.mpd:id/counterclockwise = 0x7f0a009b
com.airdoc.mpd:attr/flow_wrapMode = 0x7f040207
com.airdoc.mpd:attr/thumbDrawable = 0x7f0404b7
com.airdoc.mpd:drawable/material_ic_edit_black_24dp = 0x7f080178
com.airdoc.mpd:attr/suffixTextColor = 0x7f040446
com.airdoc.mpd:attr/closeIconSize = 0x7f0400ec
com.airdoc.mpd:attr/tabPadding = 0x7f04045d
com.airdoc.mpd:attr/subtitleTextAppearance = 0x7f040441
com.airdoc.mpd:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f070238
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary0 = 0x7f060104
com.airdoc.mpd:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f0701b3
com.airdoc.mpd:attr/materialCalendarYearNavigationButton = 0x7f040310
com.airdoc.mpd:attr/subheaderInsetStart = 0x7f04043c
com.airdoc.mpd:color/material_dynamic_neutral_variant20 = 0x7f06023b
com.airdoc.mpd:attr/textAppearanceButton = 0x7f040476
com.airdoc.mpd:attr/strokeWidth = 0x7f040438
com.airdoc.mpd:style/Base.V22.Theme.AppCompat = 0x7f1300ae
com.airdoc.mpd:attr/strokeColor = 0x7f040437
com.airdoc.mpd:id/titleDividerNoCustom = 0x7f0a0273
com.airdoc.mpd:id/mtrl_picker_header_toggle = 0x7f0a01b0
com.airdoc.mpd:color/m3_sys_color_light_surface_dim = 0x7f0601fb
com.airdoc.mpd:dimen/m3_carousel_small_item_size_max = 0x7f070114
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1301d5
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f13006b
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f130273
com.airdoc.mpd:layout/m3_alert_dialog_title = 0x7f0d0053
com.airdoc.mpd:color/design_dark_default_color_on_secondary = 0x7f060056
com.airdoc.mpd:dimen/m3_bottomappbar_height = 0x7f0700ec
com.airdoc.mpd:attr/state_collapsible = 0x7f04042d
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f13014c
com.airdoc.mpd:styleable/PropertySet = 0x7f14007a
com.airdoc.mpd:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1300bf
com.airdoc.mpd:attr/state_collapsed = 0x7f04042c
com.airdoc.mpd:attr/startIconTint = 0x7f040429
com.airdoc.mpd:color/design_default_color_error = 0x7f06005f
com.airdoc.mpd:style/Widget.AppCompat.ActionButton.Overflow = 0x7f130315
com.airdoc.mpd:attr/motionDurationExtraLong2 = 0x7f040345
com.airdoc.mpd:attr/passwordToggleTintMode = 0x7f040397
com.airdoc.mpd:attr/preserveIconSpacing = 0x7f0403af
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f08001c
com.airdoc.mpd:attr/checkedIconEnabled = 0x7f0400bf
com.airdoc.mpd:attr/colorOnSurfaceInverse = 0x7f040115
com.airdoc.mpd:attr/waveOffset = 0x7f040517
com.airdoc.mpd:string/mtrl_picker_range_header_only_end_selected = 0x7f1200c7
com.airdoc.mpd:drawable/ic_main_bg_1 = 0x7f080133
com.airdoc.mpd:style/Theme.Material3.DynamicColors.Dark = 0x7f130261
com.airdoc.mpd:string/abc_searchview_description_search = 0x7f120015
com.airdoc.mpd:style/Theme.AppCompat.DayNight = 0x7f130236
com.airdoc.mpd:attr/endIconDrawable = 0x7f0401b1
com.airdoc.mpd:attr/tabRippleColor = 0x7f040462
com.airdoc.mpd:drawable/exo_ic_fullscreen_enter = 0x7f0800c4
com.airdoc.mpd:attr/staggered = 0x7f040423
com.airdoc.mpd:attr/colorSecondaryFixedDim = 0x7f040129
com.airdoc.mpd:attr/srcCompat = 0x7f040421
com.airdoc.mpd:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1302d4
com.airdoc.mpd:dimen/mtrl_calendar_navigation_height = 0x7f0702a8
com.airdoc.mpd:attr/hide_during_ads = 0x7f040229
com.airdoc.mpd:drawable/design_ic_visibility_off = 0x7f0800ba
com.airdoc.mpd:attr/behavior_halfExpandedRatio = 0x7f040074
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f13009a
com.airdoc.mpd:attr/springStopThreshold = 0x7f040420
com.airdoc.mpd:attr/springMass = 0x7f04041e
com.airdoc.mpd:id/snap = 0x7f0a022f
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_surface = 0x7f0601bc
com.airdoc.mpd:color/color_3D3D3D = 0x7f06003c
com.airdoc.mpd:id/iv_setting = 0x7f0a0155
com.airdoc.mpd:color/black_10 = 0x7f060023
com.airdoc.mpd:attr/layout_constraintBottom_creator = 0x7f04028c
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0601ce
com.airdoc.mpd:attr/simpleItems = 0x7f04040e
com.airdoc.mpd:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1300e2
com.airdoc.mpd:attr/snackbarStyle = 0x7f040415
com.airdoc.mpd:attr/subheaderColor = 0x7f04043a
com.airdoc.mpd:string/status_bar_notification_info_overflow = 0x7f1200ee
com.airdoc.mpd:attr/behavior_hideable = 0x7f040075
com.airdoc.mpd:attr/boxCornerRadiusBottomStart = 0x7f04008a
com.airdoc.mpd:attr/materialCircleRadius = 0x7f040315
com.airdoc.mpd:attr/commitIcon = 0x7f04013a
com.airdoc.mpd:attr/badgeWithTextShapeAppearance = 0x7f040066
com.airdoc.mpd:style/Widget.MaterialComponents.Snackbar = 0x7f130463
com.airdoc.mpd:id/contiguous = 0x7f0a0097
com.airdoc.mpd:attr/singleChoiceItemLayout = 0x7f04040f
com.airdoc.mpd:attr/simpleItemSelectedRippleColor = 0x7f04040d
com.airdoc.mpd:color/teal_200 = 0x7f060315
com.airdoc.mpd:color/material_personalized_color_primary = 0x7f060297
com.airdoc.mpd:attr/motionEffect_alpha = 0x7f040360
com.airdoc.mpd:string/mtrl_picker_range_header_unselected = 0x7f1200cb
com.airdoc.mpd:color/design_fab_shadow_mid_color = 0x7f06006d
com.airdoc.mpd:attr/layout_constraintHeight_percent = 0x7f04029c
com.airdoc.mpd:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f130356
com.airdoc.mpd:integer/mtrl_chip_anim_duration = 0x7f0b0038
com.airdoc.mpd:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f07019b
com.airdoc.mpd:attr/simpleItemLayout = 0x7f04040b
com.airdoc.mpd:attr/sideSheetDialogTheme = 0x7f040409
com.airdoc.mpd:attr/shutter_background_color = 0x7f040408
com.airdoc.mpd:id/actions = 0x7f0a0045
com.airdoc.mpd:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f0701a9
com.airdoc.mpd:attr/show_subtitle_button = 0x7f040404
com.airdoc.mpd:attr/show_shuffle_button = 0x7f040403
com.airdoc.mpd:attr/show_previous_button = 0x7f040401
com.airdoc.mpd:attr/show_buffering = 0x7f0403fe
com.airdoc.mpd:attr/materialCardViewFilledStyle = 0x7f040312
com.airdoc.mpd:attr/colorOutline = 0x7f04011b
com.airdoc.mpd:attr/showPaths = 0x7f0403fb
com.airdoc.mpd:style/Theme.AppCompat.Dialog.Alert = 0x7f13023e
com.airdoc.mpd:attr/textBackgroundPanY = 0x7f040498
com.airdoc.mpd:attr/compatShadowEnabled = 0x7f04013b
com.airdoc.mpd:drawable/exo_ic_speed = 0x7f0800cc
com.airdoc.mpd:attr/paddingStartSystemWindowInsets = 0x7f04038d
com.airdoc.mpd:dimen/mtrl_tooltip_arrowSize = 0x7f070321
com.airdoc.mpd:attr/shouldRemoveExpandedCorners = 0x7f0403f5
com.airdoc.mpd:string/abc_action_mode_done = 0x7f120003
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f070224
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f070184
com.airdoc.mpd:style/shape_image_circle = 0x7f130486
com.airdoc.mpd:string/mtrl_picker_invalid_range = 0x7f1200c3
com.airdoc.mpd:dimen/design_bottom_navigation_icon_size = 0x7f070064
com.airdoc.mpd:attr/shapeAppearanceCornerSmall = 0x7f0403ee
com.airdoc.mpd:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1303a4
com.airdoc.mpd:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f070160
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f080187
com.airdoc.mpd:color/white_5 = 0x7f06031f
com.airdoc.mpd:attr/alertDialogButtonGroupStyle = 0x7f04002b
com.airdoc.mpd:attr/drawableEndCompat = 0x7f040196
com.airdoc.mpd:attr/shapeAppearanceCornerExtraLarge = 0x7f0403ea
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1301ff
com.airdoc.mpd:dimen/mtrl_snackbar_padding_horizontal = 0x7f070310
com.airdoc.mpd:attr/setsTag = 0x7f0403e8
com.airdoc.mpd:attr/selectableItemBackground = 0x7f0403e4
com.airdoc.mpd:attr/seekBarStyle = 0x7f0403e3
com.airdoc.mpd:attr/thumbStrokeColor = 0x7f0404be
com.airdoc.mpd:id/masked = 0x7f0a017a
com.airdoc.mpd:color/m3_efab_ripple_color_selector = 0x7f0600ad
com.airdoc.mpd:dimen/material_clock_period_toggle_height = 0x7f070246
com.airdoc.mpd:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1303d5
com.airdoc.mpd:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.airdoc.mpd:attr/scrubber_drawable = 0x7f0403dd
com.airdoc.mpd:style/Theme.AppCompat.Light.DarkActionBar = 0x7f130243
com.airdoc.mpd:dimen/mtrl_btn_icon_btn_padding_left = 0x7f07027c
com.airdoc.mpd:attr/scrubber_disabled_size = 0x7f0403db
com.airdoc.mpd:attr/lStar = 0x7f040276
com.airdoc.mpd:attr/actionButtonStyle = 0x7f04000d
com.airdoc.mpd:attr/scaleType = 0x7f0403d5
com.airdoc.mpd:attr/colorOnContainer = 0x7f040107
com.airdoc.mpd:attr/saturation = 0x7f0403d3
com.airdoc.mpd:attr/spinBars = 0x7f040418
com.airdoc.mpd:attr/roundColor = 0x7f0403cf
com.airdoc.mpd:attr/actionModeBackground = 0x7f040012
com.airdoc.mpd:dimen/m3_card_stroke_width = 0x7f07010f
com.airdoc.mpd:color/abc_primary_text_material_dark = 0x7f06000b
com.airdoc.mpd:attr/carousel_touchUpMode = 0x7f0400b3
com.airdoc.mpd:anim/m3_bottom_sheet_slide_in = 0x7f010022
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f130276
com.airdoc.mpd:drawable/abc_btn_borderless_material = 0x7f08002b
com.airdoc.mpd:id/ll_device_info = 0x7f0a016b
com.airdoc.mpd:color/m3_ref_palette_tertiary10 = 0x7f06015e
com.airdoc.mpd:dimen/mtrl_calendar_year_corner = 0x7f0702b2
com.airdoc.mpd:color/abc_search_url_text_selected = 0x7f060010
com.airdoc.mpd:id/content = 0x7f0a0094
com.airdoc.mpd:id/action_menu_divider = 0x7f0a003f
com.airdoc.mpd:drawable/mtrl_ic_cancel = 0x7f08018e
com.airdoc.mpd:attr/passwordToggleContentDescription = 0x7f040393
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral50 = 0x7f0600d0
com.airdoc.mpd:attr/scrimVisibleHeightTrigger = 0x7f0403d9
com.airdoc.mpd:attr/colorTertiaryFixedDim = 0x7f040139
com.airdoc.mpd:attr/subMenuArrow = 0x7f040439
com.airdoc.mpd:attr/centerIfNoTextEnabled = 0x7f0400b6
com.airdoc.mpd:attr/region_widthMoreThan = 0x7f0403c7
com.airdoc.mpd:attr/region_widthLessThan = 0x7f0403c6
com.airdoc.mpd:color/selector_input_gender_text_color = 0x7f06030e
com.airdoc.mpd:attr/textureBlurFactor = 0x7f0404b0
com.airdoc.mpd:id/sawtooth = 0x7f0a020d
com.airdoc.mpd:attr/selectableItemBackgroundBorderless = 0x7f0403e5
com.airdoc.mpd:attr/prefixTextAppearance = 0x7f0403ad
com.airdoc.mpd:attr/reactiveGuide_applyToConstraintSet = 0x7f0403c1
com.airdoc.mpd:plurals/exo_controls_fastforward_by_amount_description = 0x7f100000
com.airdoc.mpd:attr/selectionRequired = 0x7f0403e6
com.airdoc.mpd:style/TextAppearance.Design.Snackbar.Message = 0x7f1301f9
com.airdoc.mpd:attr/floatingActionButtonStyle = 0x7f0401f2
com.airdoc.mpd:attr/reactiveGuide_animateChange = 0x7f0403bf
com.airdoc.mpd:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1301cc
com.airdoc.mpd:attr/ratingBarStyleSmall = 0x7f0403be
com.airdoc.mpd:id/autoCompleteToStart = 0x7f0a0059
com.airdoc.mpd:style/Widget.AppCompat.ActivityChooserView = 0x7f130317
com.airdoc.mpd:attr/ratingBarStyle = 0x7f0403bc
com.airdoc.mpd:attr/player_layout_id = 0x7f0403a6
com.airdoc.mpd:style/Widget.Material3.Button.TextButton.Dialog = 0x7f130380
com.airdoc.mpd:attr/rangeFillColor = 0x7f0403bb
com.airdoc.mpd:attr/radioButtonStyle = 0x7f0403ba
com.airdoc.mpd:style/TextAppearance.AppCompat.Tooltip = 0x7f1301d3
com.airdoc.mpd:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f0701a0
com.airdoc.mpd:attr/queryBackground = 0x7f0403b7
com.airdoc.mpd:attr/quantizeMotionSteps = 0x7f0403b6
com.airdoc.mpd:drawable/common_8f959e_stroke_20_bg = 0x7f080084
com.airdoc.mpd:attr/progressBarStyle = 0x7f0403b3
com.airdoc.mpd:attr/progress = 0x7f0403b1
com.airdoc.mpd:attr/queryHint = 0x7f0403b8
com.airdoc.mpd:attr/pressedTranslationZ = 0x7f0403b0
com.airdoc.mpd:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f130419
com.airdoc.mpd:id/view_config = 0x7f0a02b3
com.airdoc.mpd:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f130338
com.airdoc.mpd:color/abc_tint_edittext = 0x7f060015
com.airdoc.mpd:color/secondary_text_default_material_light = 0x7f060308
com.airdoc.mpd:attr/popupTheme = 0x7f0403aa
com.airdoc.mpd:dimen/m3_btn_disabled_elevation = 0x7f0700f0
com.airdoc.mpd:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0e0080
com.airdoc.mpd:drawable/exo_legacy_controls_vr = 0x7f0800ec
com.airdoc.mpd:drawable/common_google_signin_btn_text_light = 0x7f0800a0
com.airdoc.mpd:attr/popupMenuBackground = 0x7f0403a8
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1303cf
com.airdoc.mpd:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f07018c
com.airdoc.mpd:attr/polarRelativeTo = 0x7f0403a7
com.airdoc.mpd:attr/motionEasingEmphasizedInterpolator = 0x7f040359
com.airdoc.mpd:attr/thickness = 0x7f0404b5
com.airdoc.mpd:raw/speech_7 = 0x7f110010
com.airdoc.mpd:attr/buttonStyleSmall = 0x7f0400a1
com.airdoc.mpd:styleable/Carousel = 0x7f14001c
com.airdoc.mpd:id/save_overlay_view = 0x7f0a020c
com.airdoc.mpd:style/Base.ThemeOverlay.Material3.Dialog = 0x7f130082
com.airdoc.mpd:attr/percentX = 0x7f04039c
com.airdoc.mpd:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.airdoc.mpd:color/ripple_material_light = 0x7f060306
com.airdoc.mpd:dimen/m3_navigation_rail_item_padding_top = 0x7f0701ee
com.airdoc.mpd:dimen/m3_fab_translation_z_hovered_focused = 0x7f0701d4
com.airdoc.mpd:style/Widget.Material3.Badge.AdjustToBounds = 0x7f13036c
com.airdoc.mpd:layout/abc_search_view = 0x7f0d0019
com.airdoc.mpd:attr/motionDebug = 0x7f040343
com.airdoc.mpd:attr/passwordToggleTint = 0x7f040396
com.airdoc.mpd:color/m3_ref_palette_secondary40 = 0x7f060155
com.airdoc.mpd:id/material_minute_tv = 0x7f0a0189
com.airdoc.mpd:id/TOP_START = 0x7f0a000d
com.airdoc.mpd:attr/passwordToggleDrawable = 0x7f040394
com.airdoc.mpd:attr/chipGroupStyle = 0x7f0400ca
com.airdoc.mpd:layout/notification_template_big_media_narrow_custom = 0x7f0d008b
com.airdoc.mpd:drawable/abc_switch_thumb_material = 0x7f08006a
com.airdoc.mpd:styleable/AnimatedStateListDrawableCompat = 0x7f140007
com.airdoc.mpd:attr/panelMenuListTheme = 0x7f040391
com.airdoc.mpd:dimen/m3_bottom_sheet_elevation = 0x7f0700e6
com.airdoc.mpd:attr/badgeWidth = 0x7f040063
com.airdoc.mpd:attr/foregroundInsidePadding = 0x7f040216
com.airdoc.mpd:attr/boxStrokeColor = 0x7f04008d
com.airdoc.mpd:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0401e9
com.airdoc.mpd:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f13029a
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0e00c9
com.airdoc.mpd:string/abc_searchview_description_voice = 0x7f120017
com.airdoc.mpd:attr/panelBackground = 0x7f040390
com.airdoc.mpd:dimen/design_tab_text_size = 0x7f07008b
com.airdoc.mpd:attr/paddingTopSystemWindowInsets = 0x7f04038f
com.airdoc.mpd:id/exo_controller = 0x7f0a00db
com.airdoc.mpd:attr/animation_enabled = 0x7f040038
com.airdoc.mpd:attr/repeat_toggle_modes = 0x7f0403c9
com.airdoc.mpd:attr/colorSurfaceContainer = 0x7f04012d
com.airdoc.mpd:color/m3_navigation_item_ripple_color = 0x7f0600bb
com.airdoc.mpd:raw/scan_code = 0x7f110008
com.airdoc.mpd:attr/paddingStart = 0x7f04038c
com.airdoc.mpd:id/chain2 = 0x7f0a0076
com.airdoc.mpd:attr/maxAcceleration = 0x7f040329
com.airdoc.mpd:attr/onStateTransition = 0x7f040383
com.airdoc.mpd:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f070197
com.airdoc.mpd:attr/onPositiveCross = 0x7f040381
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0e0096
com.airdoc.mpd:attr/checkboxStyle = 0x7f0400bb
com.airdoc.mpd:attr/layout_constraintGuide_percent = 0x7f040297
com.airdoc.mpd:attr/cornerSizeTopRight = 0x7f040162
com.airdoc.mpd:attr/onNegativeCross = 0x7f040380
com.airdoc.mpd:dimen/m3_searchview_divider_size = 0x7f0701fd
com.airdoc.mpd:style/ThemeOverlay.AppCompat.ActionBar = 0x7f13029d
com.airdoc.mpd:attr/offsetAlignmentMode = 0x7f04037d
com.airdoc.mpd:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0e00b6
com.airdoc.mpd:attr/layout_constraintLeft_toLeftOf = 0x7f0402a1
com.airdoc.mpd:attr/numericModifiers = 0x7f04037c
com.airdoc.mpd:attr/titlePositionInterpolator = 0x7f0404da
com.airdoc.mpd:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0700ca
com.airdoc.mpd:dimen/design_bottom_sheet_modal_elevation = 0x7f07006c
com.airdoc.mpd:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f0b003d
com.airdoc.mpd:attr/navigationRailStyle = 0x7f040376
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0e0089
com.airdoc.mpd:dimen/exo_icon_padding_bottom = 0x7f070097
com.airdoc.mpd:attr/autoShowKeyboard = 0x7f040043
com.airdoc.mpd:dimen/mtrl_btn_text_btn_padding_right = 0x7f07028a
com.airdoc.mpd:id/cl_version = 0x7f0a0087
com.airdoc.mpd:attr/navigationMode = 0x7f040375
com.airdoc.mpd:style/Widget.Material3.Button.ElevatedButton = 0x7f130376
com.airdoc.mpd:color/mtrl_switch_thumb_icon_tint = 0x7f0602e8
com.airdoc.mpd:id/animateToEnd = 0x7f0a004f
com.airdoc.mpd:attr/shortcutMatchRequired = 0x7f0403f4
com.airdoc.mpd:attr/navigationIconTint = 0x7f040374
com.airdoc.mpd:attr/navigationContentDescription = 0x7f040372
com.airdoc.mpd:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0e00b5
com.airdoc.mpd:dimen/design_navigation_item_horizontal_padding = 0x7f070078
com.airdoc.mpd:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f08017a
com.airdoc.mpd:attr/moveWhenScrollAtTop = 0x7f040370
com.airdoc.mpd:dimen/m3_btn_disabled_translation_z = 0x7f0700f1
com.airdoc.mpd:attr/listPreferredItemPaddingLeft = 0x7f0402d9
com.airdoc.mpd:attr/motionTarget = 0x7f04036d
com.airdoc.mpd:attr/motionPath = 0x7f040369
com.airdoc.mpd:attr/tabIndicatorGravity = 0x7f040457
com.airdoc.mpd:attr/buttonCompat = 0x7f040098
com.airdoc.mpd:attr/motionEffect_translationX = 0x7f040365
com.airdoc.mpd:attr/textAppearanceBody2 = 0x7f040472
com.airdoc.mpd:attr/autoAdjustToWithinGrandparentBounds = 0x7f040040
com.airdoc.mpd:drawable/abc_btn_radio_material_anim = 0x7f080033
com.airdoc.mpd:color/black_30 = 0x7f060025
com.airdoc.mpd:attr/tabIndicator = 0x7f040452
com.airdoc.mpd:style/Widget.Design.TabLayout = 0x7f130361
com.airdoc.mpd:style/ThemeOverlay.Material3.Search = 0x7f1302db
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0e0062
com.airdoc.mpd:attr/motionEasingAccelerated = 0x7f040354
com.airdoc.mpd:color/mtrl_navigation_item_text_color = 0x7f0602e1
com.airdoc.mpd:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f130081
com.airdoc.mpd:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0e0131
com.airdoc.mpd:macro/m3_comp_fab_primary_small_container_shape = 0x7f0e003b
com.airdoc.mpd:macro/m3_comp_fab_primary_container_color = 0x7f0e0037
com.airdoc.mpd:attr/touch_target_height = 0x7f0404ec
com.airdoc.mpd:dimen/exo_styled_controls_padding = 0x7f0700ab
com.airdoc.mpd:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f07013b
com.airdoc.mpd:style/Widget.Design.NavigationView = 0x7f13035e
com.airdoc.mpd:style/Base.Animation.AppCompat.Tooltip = 0x7f130010
com.airdoc.mpd:attr/appBarLayoutStyle = 0x7f040039
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f13017a
com.airdoc.mpd:string/mtrl_picker_a11y_next_month = 0x7f1200b4
com.airdoc.mpd:attr/clockNumberTextColor = 0x7f0400e8
com.airdoc.mpd:attr/tabPaddingStart = 0x7f040460
com.airdoc.mpd:dimen/design_bottom_sheet_elevation = 0x7f07006b
com.airdoc.mpd:attr/motionDurationShort3 = 0x7f040352
com.airdoc.mpd:style/TextAppearance.AppCompat.Headline = 0x7f1301c0
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f070231
com.airdoc.mpd:attr/state_with_icon = 0x7f040433
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f130173
com.airdoc.mpd:id/exo_main_text = 0x7f0a00e6
com.airdoc.mpd:attr/tintNavigationIcon = 0x7f0404cf
com.airdoc.mpd:color/m3_sys_color_dark_on_background = 0x7f060179
com.airdoc.mpd:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f130117
com.airdoc.mpd:dimen/compat_control_corner_material = 0x7f07005a
com.airdoc.mpd:drawable/common_google_signin_btn_icon_light_normal = 0x7f080099
com.airdoc.mpd:dimen/mtrl_alert_dialog_background_inset_end = 0x7f070263
com.airdoc.mpd:attr/motionDurationMedium2 = 0x7f04034d
com.airdoc.mpd:attr/iconTintMode = 0x7f04023b
com.airdoc.mpd:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f1200a8
com.airdoc.mpd:bool/enable_system_foreground_service_default = 0x7f050003
com.airdoc.mpd:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.airdoc.mpd:attr/layout_constraintHorizontal_weight = 0x7f04029f
com.airdoc.mpd:integer/mtrl_switch_thumb_pressed_duration = 0x7f0b003c
com.airdoc.mpd:attr/motionDurationLong2 = 0x7f040349
com.airdoc.mpd:dimen/m3_ripple_default_alpha = 0x7f0701f0
com.airdoc.mpd:id/easeInOut = 0x7f0a00c0
com.airdoc.mpd:attr/textureHeight = 0x7f0404b2
com.airdoc.mpd:attr/mock_showLabel = 0x7f040342
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f0601a1
com.airdoc.mpd:attr/mock_labelColor = 0x7f040340
com.airdoc.mpd:dimen/mtrl_chip_pressed_translation_z = 0x7f0702bd
com.airdoc.mpd:dimen/abc_button_padding_vertical_material = 0x7f070015
com.airdoc.mpd:styleable/NavigationView = 0x7f140072
com.airdoc.mpd:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0601d2
com.airdoc.mpd:attr/mock_label = 0x7f04033e
com.airdoc.mpd:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0e011c
com.airdoc.mpd:styleable/Slider = 0x7f140087
com.airdoc.mpd:attr/tabMaxWidth = 0x7f04045a
com.airdoc.mpd:string/material_hour_24h_suffix = 0x7f120091
com.airdoc.mpd:style/Widget.MaterialComponents.Badge = 0x7f130413
com.airdoc.mpd:attr/pivotAnchor = 0x7f04039f
com.airdoc.mpd:dimen/material_emphasis_disabled_background = 0x7f07024f
com.airdoc.mpd:attr/minSeparation = 0x7f04033a
com.airdoc.mpd:style/TextAppearance.Design.Counter = 0x7f1301f2
com.airdoc.mpd:color/m3_sys_color_dynamic_light_tertiary = 0x7f0601cf
com.airdoc.mpd:animator/fragment_open_enter = 0x7f020007
com.airdoc.mpd:color/m3_ref_palette_tertiary20 = 0x7f060160
com.airdoc.mpd:attr/menu = 0x7f040334
com.airdoc.mpd:attr/measureWithLargestChild = 0x7f040333
com.airdoc.mpd:attr/endIconCheckable = 0x7f0401af
com.airdoc.mpd:layout/mtrl_search_view = 0x7f0d0083
com.airdoc.mpd:attr/dynamicColorThemeOverlay = 0x7f0401a5
com.airdoc.mpd:drawable/ic_call_decline = 0x7f080115
com.airdoc.mpd:attr/layout_editor_absoluteX = 0x7f0402b5
com.airdoc.mpd:attr/region_heightLessThan = 0x7f0403c4
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f130040
com.airdoc.mpd:attr/maxVelocity = 0x7f040331
com.airdoc.mpd:layout/mtrl_calendar_year = 0x7f0d0074
com.airdoc.mpd:attr/maxNumber = 0x7f040330
com.airdoc.mpd:attr/maxHeight = 0x7f04032d
com.airdoc.mpd:attr/lottie_clipToCompositionBounds = 0x7f0402e3
com.airdoc.mpd:attr/overlay = 0x7f040386
com.airdoc.mpd:attr/maxCharacterCount = 0x7f04032c
com.airdoc.mpd:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1300ec
com.airdoc.mpd:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0701c5
com.airdoc.mpd:attr/maxButtonHeight = 0x7f04032b
com.airdoc.mpd:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f130175
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f080017
com.airdoc.mpd:dimen/m3_slider_thumb_elevation = 0x7f070207
com.airdoc.mpd:attr/max = 0x7f040328
com.airdoc.mpd:attr/contrast = 0x7f040153
com.airdoc.mpd:attr/colorOnSecondary = 0x7f040110
com.airdoc.mpd:dimen/m3_badge_vertical_offset = 0x7f0700d9
com.airdoc.mpd:attr/materialTimePickerTitleStyle = 0x7f040327
com.airdoc.mpd:attr/actionModeWebSearchDrawable = 0x7f040020
com.airdoc.mpd:layout/select_dialog_singlechoice_material = 0x7f0d0095
com.airdoc.mpd:macro/m3_comp_snackbar_supporting_text_color = 0x7f0e0116
com.airdoc.mpd:attr/materialThemeOverlay = 0x7f040324
com.airdoc.mpd:attr/materialSearchViewToolbarStyle = 0x7f040322
com.airdoc.mpd:color/abc_tint_switch_track = 0x7f060018
com.airdoc.mpd:string/str_hrv_detection_overview = 0x7f12010d
com.airdoc.mpd:drawable/ic_menu = 0x7f080136
com.airdoc.mpd:drawable/ic_scan_frame = 0x7f080143
com.airdoc.mpd:attr/topInsetScrimEnabled = 0x7f0404e8
com.airdoc.mpd:attr/ratingBarStyleIndicator = 0x7f0403bd
com.airdoc.mpd:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f070168
com.airdoc.mpd:attr/lottie_fallbackRes = 0x7f0402e7
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130075
com.airdoc.mpd:attr/materialIconButtonOutlinedStyle = 0x7f04031c
com.airdoc.mpd:dimen/mtrl_slider_track_side_padding = 0x7f070309
com.airdoc.mpd:attr/layout_constraintHeight_max = 0x7f04029a
com.airdoc.mpd:attr/arrowHeadLength = 0x7f04003c
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f070133
com.airdoc.mpd:macro/m3_comp_filled_button_label_text_color = 0x7f0e0045
com.airdoc.mpd:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0600b7
com.airdoc.mpd:attr/materialDividerHeavyStyle = 0x7f040318
com.airdoc.mpd:id/exo_prev = 0x7f0a00f2
com.airdoc.mpd:color/mtrl_calendar_selected_range = 0x7f0602c9
com.airdoc.mpd:style/Widget.MaterialComponents.TextView = 0x7f130475
com.airdoc.mpd:style/Widget.Material3.TabLayout.OnSurface = 0x7f1303f5
com.airdoc.mpd:drawable/exo_styled_controls_repeat_one = 0x7f080103
com.airdoc.mpd:attr/materialClockStyle = 0x7f040316
com.airdoc.mpd:styleable/SearchView = 0x7f140082
com.airdoc.mpd:attr/backgroundColor = 0x7f04004e
com.airdoc.mpd:attr/materialCardViewOutlinedStyle = 0x7f040313
com.airdoc.mpd:dimen/m3_chip_hovered_translation_z = 0x7f07011b
com.airdoc.mpd:color/m3_default_color_secondary_text = 0x7f0600a2
com.airdoc.mpd:attr/materialCardViewElevatedStyle = 0x7f040311
com.airdoc.mpd:string/str_app_name_s = 0x7f1200f0
com.airdoc.mpd:drawable/navigation_empty_icon = 0x7f0801a4
com.airdoc.mpd:attr/imageButtonStyle = 0x7f040241
com.airdoc.mpd:attr/materialCalendarHeaderToggleButton = 0x7f04030b
com.airdoc.mpd:id/action_menu_presenter = 0x7f0a0040
com.airdoc.mpd:attr/colorErrorContainer = 0x7f040105
com.airdoc.mpd:attr/materialCalendarHeaderDivider = 0x7f040307
com.airdoc.mpd:id/exo_time = 0x7f0a0100
com.airdoc.mpd:attr/materialButtonStyle = 0x7f040300
com.airdoc.mpd:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1300a1
com.airdoc.mpd:attr/materialAlertDialogTitleTextStyle = 0x7f0402fe
com.airdoc.mpd:string/str_cancel_detection = 0x7f1200f5
com.airdoc.mpd:id/src_atop = 0x7f0a023e
com.airdoc.mpd:dimen/m3_bottomappbar_horizontal_padding = 0x7f0700ed
com.airdoc.mpd:drawable/common_white_round_15_bg = 0x7f0800af
com.airdoc.mpd:attr/itemHorizontalPadding = 0x7f040257
com.airdoc.mpd:attr/mock_labelBackgroundColor = 0x7f04033f
com.airdoc.mpd:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0702dd
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f130032
com.airdoc.mpd:macro/m3_comp_search_bar_supporting_text_type = 0x7f0e00f0
com.airdoc.mpd:id/activity_chooser_view_content = 0x7f0a0046
com.airdoc.mpd:attr/motionStagger = 0x7f04036c
com.airdoc.mpd:color/m3_sys_color_on_primary_fixed = 0x7f0601ff
com.airdoc.mpd:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0402fa
com.airdoc.mpd:color/error_color_material_dark = 0x7f060079
com.airdoc.mpd:attr/tabPaddingTop = 0x7f040461
com.airdoc.mpd:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f070239
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0e0095
com.airdoc.mpd:color/m3_sys_color_light_on_primary = 0x7f0601e6
com.airdoc.mpd:attr/marqueeSpeed = 0x7f0402f8
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f080010
com.airdoc.mpd:drawable/mtrl_ic_check_mark = 0x7f08018f
com.airdoc.mpd:id/time = 0x7f0a0271
com.airdoc.mpd:attr/fontProviderFetchStrategy = 0x7f04020c
com.airdoc.mpd:attr/spanCount = 0x7f040417
com.airdoc.mpd:style/Base.V14.Theme.MaterialComponents = 0x7f130092
com.airdoc.mpd:attr/marginRightSystemWindowInsets = 0x7f0402f6
com.airdoc.mpd:attr/mock_diagonalsColor = 0x7f04033d
com.airdoc.mpd:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1302d8
com.airdoc.mpd:attr/marginLeftSystemWindowInsets = 0x7f0402f5
com.airdoc.mpd:color/bright_foreground_material_dark = 0x7f060031
com.airdoc.mpd:dimen/m3_comp_elevated_button_container_elevation = 0x7f07012c
com.airdoc.mpd:dimen/mtrl_card_checked_icon_size = 0x7f0702b8
com.airdoc.mpd:string/mtrl_switch_thumb_path_name = 0x7f1200dc
com.airdoc.mpd:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0e00b9
com.airdoc.mpd:drawable/selector_read_track_figure_icon = 0x7f0801bf
com.airdoc.mpd:attr/transitionPathRotate = 0x7f0404fd
com.airdoc.mpd:dimen/exo_icon_size = 0x7f070098
com.airdoc.mpd:attr/actionModeCutDrawable = 0x7f040017
com.airdoc.mpd:dimen/m3_comp_navigation_bar_icon_size = 0x7f070161
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1303c5
com.airdoc.mpd:attr/lottie_useCompositionFrameRate = 0x7f0402f3
com.airdoc.mpd:attr/lottie_speed = 0x7f0402f1
com.airdoc.mpd:integer/mtrl_badge_max_character_count = 0x7f0b0030
com.airdoc.mpd:color/m3_tonal_button_ripple_color_selector = 0x7f060223
com.airdoc.mpd:attr/lottie_repeatMode = 0x7f0402f0
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f07022d
com.airdoc.mpd:dimen/m3_btn_text_btn_padding_right = 0x7f070104
com.airdoc.mpd:string/abc_capital_off = 0x7f120006
com.airdoc.mpd:attr/lottie_repeatCount = 0x7f0402ef
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary70 = 0x7f0600ff
com.airdoc.mpd:string/str_selection_age = 0x7f120125
com.airdoc.mpd:attr/listLayout = 0x7f0402d2
com.airdoc.mpd:attr/lottie_rawRes = 0x7f0402ed
com.airdoc.mpd:attr/backgroundSplit = 0x7f040054
com.airdoc.mpd:attr/lottie_loop = 0x7f0402eb
com.airdoc.mpd:attr/lottieAnimationViewStyle = 0x7f0402e0
com.airdoc.mpd:drawable/m3_avd_show_password = 0x7f080169
com.airdoc.mpd:attr/textAppearanceTitleLarge = 0x7f040493
com.airdoc.mpd:attr/fabAnimationMode = 0x7f0401da
com.airdoc.mpd:attr/colorSecondaryFixed = 0x7f040128
com.airdoc.mpd:attr/dividerHorizontal = 0x7f04018b
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0e0069
com.airdoc.mpd:attr/lottie_cacheComposition = 0x7f0402e2
com.airdoc.mpd:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f070268
com.airdoc.mpd:dimen/abc_star_big = 0x7f07003b
com.airdoc.mpd:style/Widget.Material3.AppBarLayout = 0x7f130366
com.airdoc.mpd:id/unchecked = 0x7f0a02aa
com.airdoc.mpd:drawable/common_8d82c6_round_bg = 0x7f080083
com.airdoc.mpd:attr/listPreferredItemHeight = 0x7f0402d5
com.airdoc.mpd:attr/expandedTitleTextColor = 0x7f0401ce
com.airdoc.mpd:layout/mtrl_auto_complete_simple_item = 0x7f0d006a
com.airdoc.mpd:id/spread = 0x7f0a023a
com.airdoc.mpd:attr/transitionDisable = 0x7f0404fa
com.airdoc.mpd:dimen/material_clock_period_toggle_vertical_gap = 0x7f070248
com.airdoc.mpd:drawable/abc_text_cursor_material = 0x7f08006e
com.airdoc.mpd:anim/abc_popup_exit = 0x7f010004
com.airdoc.mpd:attr/listPreferredItemHeightLarge = 0x7f0402d6
com.airdoc.mpd:attr/actionOverflowButtonStyle = 0x7f040021
com.airdoc.mpd:layout/notification_template_media_custom = 0x7f0d0090
com.airdoc.mpd:color/m3_textfield_input_text_color = 0x7f060216
com.airdoc.mpd:attr/drawerLayoutStyle = 0x7f0401a0
com.airdoc.mpd:attr/listMenuViewStyle = 0x7f0402d3
com.airdoc.mpd:color/color_selector_param_setting_mode = 0x7f060043
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1302e9
com.airdoc.mpd:attr/listItemLayout = 0x7f0402d1
com.airdoc.mpd:string/mtrl_checkbox_state_description_indeterminate = 0x7f1200af
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionButton = 0x7f130331
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Headline4 = 0x7f130229
com.airdoc.mpd:style/TextAppearance.MaterialComponents.Badge = 0x7f130220
com.airdoc.mpd:attr/linearProgressIndicatorStyle = 0x7f0402cc
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1301db
com.airdoc.mpd:attr/nestedScrollable = 0x7f04037a
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_background = 0x7f0601b7
com.airdoc.mpd:id/exo_audio_track = 0x7f0a00d4
com.airdoc.mpd:attr/limitBoundsTo = 0x7f0402c9
com.airdoc.mpd:attr/liftOnScrollColor = 0x7f0402c7
com.airdoc.mpd:style/Widget.AppCompat.Spinner.Underlined = 0x7f130352
com.airdoc.mpd:color/mtrl_textinput_disabled_color = 0x7f0602f3
com.airdoc.mpd:id/navigation_bar_item_small_label_view = 0x7f0a01bc
com.airdoc.mpd:attr/layout_scrollInterpolator = 0x7f0402c4
com.airdoc.mpd:attr/layout_scrollFlags = 0x7f0402c3
com.airdoc.mpd:attr/layout_scrollEffect = 0x7f0402c2
com.airdoc.mpd:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f070149
com.airdoc.mpd:attr/buttonIcon = 0x7f04009a
com.airdoc.mpd:style/TextAppearance.Design.Prefix = 0x7f1301f8
com.airdoc.mpd:attr/errorShown = 0x7f0401c1
com.airdoc.mpd:color/m3_ref_palette_black = 0x7f0600c4
com.airdoc.mpd:drawable/ic_refresh_red = 0x7f08013e
com.airdoc.mpd:attr/layout_dodgeInsetEdges = 0x7f0402b4
com.airdoc.mpd:attr/flow_firstHorizontalBias = 0x7f0401f5
com.airdoc.mpd:drawable/ic_refresh_white = 0x7f08013f
com.airdoc.mpd:drawable/exo_notification_rewind = 0x7f0800f2
com.airdoc.mpd:attr/carousel_touchUp_velocityThreshold = 0x7f0400b5
com.airdoc.mpd:color/m3_sys_color_light_error = 0x7f0601de
com.airdoc.mpd:attr/paddingBottomSystemWindowInsets = 0x7f040388
com.airdoc.mpd:integer/material_motion_duration_medium_1 = 0x7f0b002b
com.airdoc.mpd:attr/layout_constraintTag = 0x7f0402a8
com.airdoc.mpd:attr/transitionShapeAppearance = 0x7f0404fe
com.airdoc.mpd:styleable/AnimatedStateListDrawableTransition = 0x7f140009
com.airdoc.mpd:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f13009c
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1303ad
com.airdoc.mpd:attr/badgeWithTextWidth = 0x7f040068
com.airdoc.mpd:color/m3_ref_palette_error60 = 0x7f060118
com.airdoc.mpd:attr/layout_constraintRight_toLeftOf = 0x7f0402a4
com.airdoc.mpd:style/TextAppearance.Material3.LabelSmall = 0x7f130218
com.airdoc.mpd:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0401d4
com.airdoc.mpd:attr/layout_constraintRight_creator = 0x7f0402a3
com.airdoc.mpd:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0e0099
com.airdoc.mpd:string/mtrl_picker_toggle_to_day_selection = 0x7f1200d6
com.airdoc.mpd:attr/mock_showDiagonals = 0x7f040341
com.airdoc.mpd:styleable/Transition = 0x7f14009b
com.airdoc.mpd:id/none = 0x7f0a01c3
com.airdoc.mpd:attr/layout_goneMarginBaseline = 0x7f0402b7
com.airdoc.mpd:color/m3_ref_palette_neutral96 = 0x7f060133
com.airdoc.mpd:attr/layout_constraintHorizontal_chainStyle = 0x7f04029e
com.airdoc.mpd:drawable/notification_bg_low_pressed = 0x7f0801a9
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0e006b
com.airdoc.mpd:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f080185
com.airdoc.mpd:id/withText = 0x7f0a02c1
com.airdoc.mpd:id/open_search_view_status_bar_spacer = 0x7f0a01d9
com.airdoc.mpd:string/str_detection_code_s = 0x7f1200fe
com.airdoc.mpd:attr/layout_constraintWidth_default = 0x7f0402b0
com.airdoc.mpd:attr/flow_horizontalStyle = 0x7f0401fc
com.airdoc.mpd:drawable/common_google_signin_btn_icon_dark = 0x7f080092
com.airdoc.mpd:attr/layout_constraintVertical_weight = 0x7f0402ae
com.airdoc.mpd:attr/layout_constraintBaseline_toTopOf = 0x7f04028b
com.airdoc.mpd:attr/layout_constraintHeight_min = 0x7f04029b
com.airdoc.mpd:color/common_google_signin_btn_text_light_default = 0x7f06004a
com.airdoc.mpd:dimen/m3_comp_fab_primary_small_container_height = 0x7f070143
com.airdoc.mpd:dimen/exo_icon_horizontal_margin = 0x7f070095
com.airdoc.mpd:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1301de
com.airdoc.mpd:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f0702ab
com.airdoc.mpd:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1200d5
com.airdoc.mpd:style/Theme.MaterialComponents.Light = 0x7f130289
com.airdoc.mpd:drawable/common_light_blue_round_bg = 0x7f0800a9
com.airdoc.mpd:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f080044
com.airdoc.mpd:attr/contentInsetStartWithNavigation = 0x7f04014a
com.airdoc.mpd:color/material_personalized_color_outline = 0x7f060295
com.airdoc.mpd:attr/buttonTint = 0x7f0400a2
com.airdoc.mpd:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1302a8
com.airdoc.mpd:attr/layout_constraintHeight = 0x7f040298
com.airdoc.mpd:attr/layout_anchor = 0x7f040281
com.airdoc.mpd:attr/passwordToggleEnabled = 0x7f040395
com.airdoc.mpd:attr/colorSurfaceVariant = 0x7f040134
com.airdoc.mpd:attr/layout_constraintEnd_toStartOf = 0x7f040294
com.airdoc.mpd:attr/attributeName = 0x7f04003f
com.airdoc.mpd:id/exo_track_selection_view = 0x7f0a0101
com.airdoc.mpd:attr/enforceMaterialTheme = 0x7f0401b7
com.airdoc.mpd:color/m3_sys_color_dark_on_surface = 0x7f060180
com.airdoc.mpd:id/currentState = 0x7f0a009d
com.airdoc.mpd:color/m3_ref_palette_neutral_variant20 = 0x7f060139
com.airdoc.mpd:attr/layout_constraintCircleRadius = 0x7f040291
com.airdoc.mpd:dimen/notification_content_margin_start = 0x7f07032a
com.airdoc.mpd:drawable/icon_new_patient = 0x7f08015a
com.airdoc.mpd:id/tag_on_receive_content_listener = 0x7f0a0258
com.airdoc.mpd:attr/layout_constraintTop_toTopOf = 0x7f0402ab
com.airdoc.mpd:drawable/exo_styled_controls_subtitle_off = 0x7f080109
com.airdoc.mpd:attr/activeIndicatorLabelPadding = 0x7f040026
com.airdoc.mpd:attr/labelStyle = 0x7f040278
com.airdoc.mpd:attr/expanded = 0x7f0401c5
com.airdoc.mpd:attr/endIconTint = 0x7f0401b5
com.airdoc.mpd:style/Animation.Design.BottomSheetDialog = 0x7f130005
com.airdoc.mpd:drawable/$avd_hide_password__2 = 0x7f080002
com.airdoc.mpd:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0e0140
com.airdoc.mpd:drawable/$m3_avd_show_password__0 = 0x7f08000a
com.airdoc.mpd:dimen/material_textinput_default_width = 0x7f07025c
com.airdoc.mpd:dimen/m3_carousel_small_item_default_corner_size = 0x7f070113
com.airdoc.mpd:dimen/abc_dialog_min_width_minor = 0x7f070023
com.airdoc.mpd:color/m3_sys_color_light_on_primary_container = 0x7f0601e7
com.airdoc.mpd:attr/keyboardIcon = 0x7f040274
com.airdoc.mpd:styleable/TabItem = 0x7f140091
com.airdoc.mpd:attr/pathMotionArc = 0x7f040398
com.airdoc.mpd:attr/itemVerticalPadding = 0x7f040271
com.airdoc.mpd:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0701c1
com.airdoc.mpd:attr/endIconTintMode = 0x7f0401b6
com.airdoc.mpd:color/material_dynamic_primary60 = 0x7f06024c
com.airdoc.mpd:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.airdoc.mpd:attr/indicatorSize = 0x7f04024d
com.airdoc.mpd:color/m3_sys_color_dark_on_error_container = 0x7f06017b
com.airdoc.mpd:macro/m3_comp_search_bar_leading_icon_color = 0x7f0e00ec
com.airdoc.mpd:macro/m3_comp_assist_chip_container_shape = 0x7f0e0000
com.airdoc.mpd:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0702c3
com.airdoc.mpd:id/showCustom = 0x7f0a0226
com.airdoc.mpd:color/design_fab_stroke_end_inner_color = 0x7f06006f
com.airdoc.mpd:styleable/SignInButton = 0x7f140086
com.airdoc.mpd:color/material_dynamic_neutral90 = 0x7f060235
com.airdoc.mpd:dimen/mtrl_calendar_day_corner = 0x7f070292
com.airdoc.mpd:attr/hideOnContentScroll = 0x7f040227
com.airdoc.mpd:id/home = 0x7f0a012b
com.airdoc.mpd:styleable/MotionLabel = 0x7f14006b
com.airdoc.mpd:attr/itemShapeFillColor = 0x7f040264
com.airdoc.mpd:attr/itemShapeAppearance = 0x7f040262
com.airdoc.mpd:attr/closeIconEndPadding = 0x7f0400eb
com.airdoc.mpd:attr/behavior_peekHeight = 0x7f040077
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f130291
com.airdoc.mpd:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f070193
com.airdoc.mpd:attr/itemPaddingTop = 0x7f040260
com.airdoc.mpd:dimen/abc_control_inset_material = 0x7f070019
com.airdoc.mpd:attr/tooltipFrameBackground = 0x7f0404e5
com.airdoc.mpd:attr/badgeVerticalPadding = 0x7f040061
com.airdoc.mpd:attr/collapsingToolbarLayoutMediumSize = 0x7f0400f9
com.airdoc.mpd:color/design_box_stroke_color = 0x7f060050
com.airdoc.mpd:color/m3_ref_palette_primary99 = 0x7f06014f
com.airdoc.mpd:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f130384
com.airdoc.mpd:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.airdoc.mpd:attr/indicatorDirectionLinear = 0x7f04024b
com.airdoc.mpd:dimen/design_textinput_caption_translate_y = 0x7f07008d
com.airdoc.mpd:integer/m3_card_anim_duration_ms = 0x7f0b0010
com.airdoc.mpd:attr/played_color = 0x7f0403a5
com.airdoc.mpd:attr/flow_verticalAlign = 0x7f040203
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents.Badge = 0x7f13019c
com.airdoc.mpd:id/fitCenter = 0x7f0a010e
com.airdoc.mpd:color/material_dynamic_neutral_variant70 = 0x7f060240
com.airdoc.mpd:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0801a0
com.airdoc.mpd:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1300b5
com.airdoc.mpd:id/mtrl_calendar_main_pane = 0x7f0a01a3
com.airdoc.mpd:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0401ed
com.airdoc.mpd:dimen/exo_settings_offset = 0x7f0700a0
com.airdoc.mpd:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010014
com.airdoc.mpd:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0e0133
com.airdoc.mpd:styleable/CheckedTextView = 0x7f14001d
com.airdoc.mpd:drawable/exo_ic_rewind = 0x7f0800c8
com.airdoc.mpd:attr/contentInsetLeft = 0x7f040147
com.airdoc.mpd:drawable/abc_btn_default_mtrl_shape = 0x7f080031
com.airdoc.mpd:dimen/mtrl_switch_track_width = 0x7f070316
com.airdoc.mpd:dimen/material_emphasis_high_type = 0x7f070250
com.airdoc.mpd:array/exo_controls_playback_speeds = 0x7f030001
com.airdoc.mpd:color/material_personalized_color_surface_container_low = 0x7f0602a5
com.airdoc.mpd:attr/shapeCornerFamily = 0x7f0403f3
com.airdoc.mpd:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0702f1
com.airdoc.mpd:attr/drawableRightCompat = 0x7f040198
com.airdoc.mpd:string/fab_transformation_sheet_behavior = 0x7f12007b
com.airdoc.mpd:attr/wavePeriod = 0x7f040518
com.airdoc.mpd:attr/maxLines = 0x7f04032f
com.airdoc.mpd:attr/iconEndPadding = 0x7f040235
com.airdoc.mpd:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f130290
com.airdoc.mpd:attr/hoveredFocusedTranslationZ = 0x7f040233
com.airdoc.mpd:string/bottomsheet_action_expand_halfway = 0x7f120021
com.airdoc.mpd:color/material_dynamic_secondary10 = 0x7f060253
com.airdoc.mpd:color/bright_foreground_material_light = 0x7f060032
com.airdoc.mpd:attr/reactiveGuide_valueId = 0x7f0403c2
com.airdoc.mpd:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1300f6
com.airdoc.mpd:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f13009b
com.airdoc.mpd:dimen/mtrl_calendar_navigation_top_padding = 0x7f0702a9
com.airdoc.mpd:id/group_divider = 0x7f0a0124
com.airdoc.mpd:attr/hintTextAppearance = 0x7f04022d
com.airdoc.mpd:integer/m3_sys_motion_duration_long2 = 0x7f0b0017
com.airdoc.mpd:attr/hide_on_touch = 0x7f04022a
com.airdoc.mpd:attr/rippleColor = 0x7f0403cc
com.airdoc.mpd:color/material_dynamic_neutral100 = 0x7f06022d
com.airdoc.mpd:attr/hideNavigationIcon = 0x7f040226
com.airdoc.mpd:attr/helperTextTextColor = 0x7f040223
com.airdoc.mpd:attr/backgroundTintMode = 0x7f040057
com.airdoc.mpd:color/black_90 = 0x7f06002c
com.airdoc.mpd:attr/helperText = 0x7f040220
com.airdoc.mpd:attr/haloRadius = 0x7f04021d
com.airdoc.mpd:attr/colorPrimaryContainer = 0x7f04011e
com.airdoc.mpd:attr/haloColor = 0x7f04021c
com.airdoc.mpd:string/exo_controls_hide = 0x7f120049
com.airdoc.mpd:color/m3_sys_color_on_secondary_fixed = 0x7f060201
com.airdoc.mpd:color/m3_sys_color_secondary_fixed_dim = 0x7f060208
com.airdoc.mpd:layout/fragment_input_info_detection = 0x7f0d004a
com.airdoc.mpd:attr/autoSizeStepGranularity = 0x7f040047
com.airdoc.mpd:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f130336
com.airdoc.mpd:id/forever = 0x7f0a011a
com.airdoc.mpd:attr/materialCalendarDayOfWeekLabel = 0x7f040303
com.airdoc.mpd:style/Widget.MaterialComponents.BottomAppBar = 0x7f130414
com.airdoc.mpd:dimen/notification_top_pad = 0x7f070334
com.airdoc.mpd:attr/actionDropDownStyle = 0x7f04000e
com.airdoc.mpd:attr/flow_maxElementsWrap = 0x7f040201
com.airdoc.mpd:anim/abc_popup_enter = 0x7f010003
com.airdoc.mpd:color/material_personalized_color_primary_text = 0x7f06029a
com.airdoc.mpd:drawable/app_launcher_bg = 0x7f080078
com.airdoc.mpd:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f08001a
com.airdoc.mpd:color/mtrl_filled_icon_tint = 0x7f0602d8
com.airdoc.mpd:attr/lottie_ignoreDisabledSystemAnimations = 0x7f0402e9
com.airdoc.mpd:drawable/common_white_round_120_bg = 0x7f0800ae
com.airdoc.mpd:id/search_edit_frame = 0x7f0a0219
com.airdoc.mpd:dimen/mtrl_bottomappbar_height = 0x7f070274
com.airdoc.mpd:attr/flow_lastVerticalBias = 0x7f0401ff
com.airdoc.mpd:attr/flow_lastHorizontalStyle = 0x7f0401fe
com.airdoc.mpd:dimen/mtrl_progress_circular_inset = 0x7f0702f0
com.airdoc.mpd:attr/flow_horizontalAlign = 0x7f0401f9
com.airdoc.mpd:attr/popupMenuStyle = 0x7f0403a9
com.airdoc.mpd:attr/flow_firstVerticalStyle = 0x7f0401f8
com.airdoc.mpd:drawable/$m3_avd_show_password__1 = 0x7f08000b
com.airdoc.mpd:dimen/m3_comp_fab_primary_container_elevation = 0x7f070139
com.airdoc.mpd:anim/anim_common_loading = 0x7f01000c
com.airdoc.mpd:attr/dividerVertical = 0x7f040190
com.airdoc.mpd:attr/searchHintIcon = 0x7f0403df
com.airdoc.mpd:string/call_notification_answer_action = 0x7f120024
com.airdoc.mpd:id/bounceBoth = 0x7f0a0064
com.airdoc.mpd:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f13028d
com.airdoc.mpd:dimen/design_fab_size_normal = 0x7f070072
com.airdoc.mpd:attr/textAppearanceCaption = 0x7f040477
com.airdoc.mpd:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0e00bb
com.airdoc.mpd:attr/chipBackgroundColor = 0x7f0400c7
com.airdoc.mpd:dimen/m3_ripple_pressed_alpha = 0x7f0701f3
com.airdoc.mpd:color/m3_navigation_item_background_color = 0x7f0600b9
com.airdoc.mpd:attr/floatingActionButtonPrimaryStyle = 0x7f0401eb
com.airdoc.mpd:attr/motionEasingEmphasized = 0x7f040356
com.airdoc.mpd:attr/badgeStyle = 0x7f04005d
com.airdoc.mpd:attr/fontProviderFetchTimeout = 0x7f04020d
com.airdoc.mpd:attr/materialTimePickerStyle = 0x7f040325
com.airdoc.mpd:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0401ea
com.airdoc.mpd:dimen/mtrl_switch_text_padding = 0x7f070311
com.airdoc.mpd:integer/mtrl_calendar_selection_text_lines = 0x7f0b0034
com.airdoc.mpd:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0401e7
com.airdoc.mpd:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f120086
com.airdoc.mpd:attr/fastScrollHorizontalThumbDrawable = 0x7f0401e1
com.airdoc.mpd:drawable/exo_ic_check = 0x7f0800bf
com.airdoc.mpd:attr/errorEnabled = 0x7f0401bd
com.airdoc.mpd:attr/circularflow_angles = 0x7f0400de
com.airdoc.mpd:attr/drawableBottomCompat = 0x7f040195
com.airdoc.mpd:attr/fabCustomSize = 0x7f0401de
com.airdoc.mpd:attr/buttonTintMode = 0x7f0400a3
com.airdoc.mpd:attr/guidelineUseRtl = 0x7f04021b
com.airdoc.mpd:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0c0009
com.airdoc.mpd:color/m3_calendar_item_disabled_text = 0x7f060090
com.airdoc.mpd:attr/fabCradleRoundedCornerRadius = 0x7f0401dc
com.airdoc.mpd:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0e0021
com.airdoc.mpd:layout/m3_alert_dialog = 0x7f0d0051
com.airdoc.mpd:drawable/design_ic_visibility = 0x7f0800b9
com.airdoc.mpd:color/m3_dynamic_default_color_secondary_text = 0x7f0600a9
com.airdoc.mpd:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f130399
com.airdoc.mpd:attr/cardCornerRadius = 0x7f0400a5
com.airdoc.mpd:attr/iconTint = 0x7f04023a
com.airdoc.mpd:drawable/mtrl_bottomsheet_drag_handle = 0x7f08017f
com.airdoc.mpd:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f07022c
com.airdoc.mpd:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1301c9
com.airdoc.mpd:attr/subtitleTextStyle = 0x7f040443
com.airdoc.mpd:attr/helperTextTextAppearance = 0x7f040222
com.airdoc.mpd:attr/fabAlignmentModeEndMargin = 0x7f0401d8
com.airdoc.mpd:string/exo_track_selection_title_audio = 0x7f120070
com.airdoc.mpd:attr/hideMotionSpec = 0x7f040225
com.airdoc.mpd:dimen/abc_text_size_headline_material = 0x7f070047
com.airdoc.mpd:id/accessibility_custom_action_4 = 0x7f0a002a
com.airdoc.mpd:color/material_personalized_color_surface_container_high = 0x7f0602a3
com.airdoc.mpd:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f13018e
com.airdoc.mpd:attr/dragScale = 0x7f040192
com.airdoc.mpd:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f130297
com.airdoc.mpd:string/str_hrv_waiting_text = 0x7f12010e
com.airdoc.mpd:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f070214
com.airdoc.mpd:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0401d5
com.airdoc.mpd:attr/extendStrategy = 0x7f0401d0
com.airdoc.mpd:attr/imageAspectRatioAdjust = 0x7f040240
com.airdoc.mpd:attr/itemIconSize = 0x7f04025a
com.airdoc.mpd:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f130335
com.airdoc.mpd:string/character_counter_content_description = 0x7f12002b
com.airdoc.mpd:attr/tabPaddingEnd = 0x7f04045f
com.airdoc.mpd:string/exo_track_role_supplementary = 0x7f12006d
com.airdoc.mpd:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0e004f
com.airdoc.mpd:attr/searchViewStyle = 0x7f0403e2
com.airdoc.mpd:attr/expandedTitleMarginStart = 0x7f0401cb
com.airdoc.mpd:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010017
com.airdoc.mpd:anim/abc_slide_in_top = 0x7f010007
com.airdoc.mpd:attr/flow_padding = 0x7f040202
com.airdoc.mpd:attr/itemIconPadding = 0x7f040259
com.airdoc.mpd:attr/colorTertiaryContainer = 0x7f040137
com.airdoc.mpd:dimen/m3_comp_bottom_app_bar_container_height = 0x7f070125
com.airdoc.mpd:attr/customPixelDimension = 0x7f040175
com.airdoc.mpd:attr/expandedHintEnabled = 0x7f0401c6
com.airdoc.mpd:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.airdoc.mpd:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0401ee
com.airdoc.mpd:drawable/abc_action_bar_item_background_material = 0x7f08002a
com.airdoc.mpd:macro/m3_comp_sheet_side_docked_container_color = 0x7f0e010a
com.airdoc.mpd:attr/errorTextAppearance = 0x7f0401c2
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1301fe
com.airdoc.mpd:anim/mtrl_bottom_sheet_slide_in = 0x7f01002a
com.airdoc.mpd:attr/errorIconTint = 0x7f0401bf
com.airdoc.mpd:dimen/design_tab_text_size_2line = 0x7f07008c
com.airdoc.mpd:string/mtrl_checkbox_button_path_group_name = 0x7f1200ab
com.airdoc.mpd:string/exo_track_role_commentary = 0x7f12006c
com.airdoc.mpd:id/action0 = 0x7f0a0030
com.airdoc.mpd:drawable/exo_styled_controls_repeat_all = 0x7f080101
com.airdoc.mpd:attr/errorIconDrawable = 0x7f0401be
com.airdoc.mpd:drawable/mtrl_switch_thumb = 0x7f080197
com.airdoc.mpd:layout/mtrl_picker_header_fullscreen = 0x7f0d007c
com.airdoc.mpd:attr/itemStrokeWidth = 0x7f04026b
com.airdoc.mpd:dimen/m3_btn_icon_only_min_width = 0x7f0700f9
com.airdoc.mpd:attr/badgeRadius = 0x7f04005a
com.airdoc.mpd:animator/m3_btn_state_list_anim = 0x7f02000b
com.airdoc.mpd:anim/abc_slide_in_bottom = 0x7f010006
com.airdoc.mpd:attr/forceApplySystemWindowInsetTop = 0x7f040214
com.airdoc.mpd:string/exo_controls_show = 0x7f120057
com.airdoc.mpd:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010012
com.airdoc.mpd:attr/ensureMinTouchTargetSize = 0x7f0401b9
com.airdoc.mpd:id/mtrl_calendar_frame = 0x7f0a01a2
com.airdoc.mpd:attr/autoSizePresetSizes = 0x7f040046
com.airdoc.mpd:attr/chipSpacingVertical = 0x7f0400d4
com.airdoc.mpd:id/exo_subtitles = 0x7f0a00fe
com.airdoc.mpd:drawable/exo_styled_controls_repeat_off = 0x7f080102
com.airdoc.mpd:attr/chipStartPadding = 0x7f0400d6
com.airdoc.mpd:attr/colorTertiary = 0x7f040136
com.airdoc.mpd:id/search_go_btn = 0x7f0a021a
com.airdoc.mpd:attr/endIconScaleType = 0x7f0401b4
com.airdoc.mpd:string/exo_track_stereo = 0x7f120073
com.airdoc.mpd:attr/viewTransitionMode = 0x7f04050f
com.airdoc.mpd:dimen/tooltip_margin = 0x7f070338
com.airdoc.mpd:attr/motionEffect_viewTransition = 0x7f040367
com.airdoc.mpd:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0600b6
com.airdoc.mpd:attr/textAppearanceHeadlineLarge = 0x7f040481
com.airdoc.mpd:id/ll_fingertip_collection = 0x7f0a016e
com.airdoc.mpd:attr/editTextColor = 0x7f0401a7
com.airdoc.mpd:attr/toggleCheckedStateOnClick = 0x7f0404df
com.airdoc.mpd:dimen/m3_searchbar_margin_vertical = 0x7f0701f8
com.airdoc.mpd:attr/checkedChip = 0x7f0400bd
com.airdoc.mpd:attr/collapsingToolbarLayoutMediumStyle = 0x7f0400fa
com.airdoc.mpd:color/exo_bottom_bar_background = 0x7f06007d
com.airdoc.mpd:attr/drawerLayoutCornerSize = 0x7f04019f
com.airdoc.mpd:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0e0030
com.airdoc.mpd:dimen/mtrl_shape_corner_size_large_component = 0x7f0702fe
com.airdoc.mpd:color/highlighted_text_material_light = 0x7f060086
com.airdoc.mpd:attr/motionEasingLinear = 0x7f04035a
com.airdoc.mpd:attr/showDelay = 0x7f0403f8
com.airdoc.mpd:attr/drawableTintMode = 0x7f04019c
com.airdoc.mpd:macro/m3_comp_date_picker_modal_container_color = 0x7f0e000e
com.airdoc.mpd:attr/autoSizeTextType = 0x7f040048
com.airdoc.mpd:layout/mtrl_picker_text_input_date = 0x7f0d0080
com.airdoc.mpd:id/ghost_view_holder = 0x7f0a011f
com.airdoc.mpd:attr/drawableStartCompat = 0x7f04019a
com.airdoc.mpd:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f13045d
com.airdoc.mpd:dimen/mtrl_shape_corner_size_medium_component = 0x7f0702ff
com.airdoc.mpd:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0d0069
com.airdoc.mpd:attr/colorPrimaryFixed = 0x7f040120
com.airdoc.mpd:animator/m3_card_state_list_anim = 0x7f02000d
com.airdoc.mpd:attr/layout_constraintCircle = 0x7f04028f
com.airdoc.mpd:attr/layout_anchorGravity = 0x7f040282
com.airdoc.mpd:attr/textBackgroundZoom = 0x7f04049a
com.airdoc.mpd:attr/materialCalendarDay = 0x7f040302
com.airdoc.mpd:dimen/m3_chip_icon_size = 0x7f07011c
com.airdoc.mpd:attr/drawPath = 0x7f040194
com.airdoc.mpd:id/right_side = 0x7f0a0205
com.airdoc.mpd:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1300d1
com.airdoc.mpd:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f08017d
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f13003b
com.airdoc.mpd:attr/autoCompleteTextViewStyle = 0x7f040042
com.airdoc.mpd:attr/actionModeTheme = 0x7f04001f
com.airdoc.mpd:style/Widget.AppCompat.ListView.Menu = 0x7f130342
com.airdoc.mpd:id/mtrl_picker_fullscreen = 0x7f0a01ac
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f130168
com.airdoc.mpd:attr/colorPrimaryVariant = 0x7f040124
com.airdoc.mpd:dimen/mtrl_calendar_day_today_stroke = 0x7f070295
com.airdoc.mpd:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f130054
com.airdoc.mpd:attr/errorContentDescription = 0x7f0401bc
com.airdoc.mpd:attr/dividerColor = 0x7f04018a
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f070185
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f130333
com.airdoc.mpd:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f07016b
com.airdoc.mpd:id/arc = 0x7f0a0053
com.airdoc.mpd:attr/materialSearchViewToolbarHeight = 0x7f040321
com.airdoc.mpd:attr/divider = 0x7f040189
com.airdoc.mpd:dimen/abc_search_view_preferred_height = 0x7f070036
com.airdoc.mpd:attr/borderlessButtonStyle = 0x7f04007f
com.airdoc.mpd:id/decor_content_parent = 0x7f0a00a5
com.airdoc.mpd:attr/layout_goneMarginStart = 0x7f0402bc
com.airdoc.mpd:attr/itemFillColor = 0x7f040256
com.airdoc.mpd:color/abc_secondary_text_material_dark = 0x7f060011
com.airdoc.mpd:attr/indicatorInset = 0x7f04024c
com.airdoc.mpd:attr/actionModeFindDrawable = 0x7f040018
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f130201
com.airdoc.mpd:attr/layout_constraintBaseline_toBottomOf = 0x7f04028a
com.airdoc.mpd:attr/checkedIconGravity = 0x7f0400c0
com.airdoc.mpd:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f130416
com.airdoc.mpd:attr/chipStyle = 0x7f0400d9
com.airdoc.mpd:attr/default_artwork = 0x7f040181
com.airdoc.mpd:dimen/m3_appbar_scrim_height_trigger = 0x7f0700c9
com.airdoc.mpd:id/accessibility_custom_action_3 = 0x7f0a0027
com.airdoc.mpd:attr/badgeWidePadding = 0x7f040062
com.airdoc.mpd:color/material_personalized_color_on_primary = 0x7f06028c
com.airdoc.mpd:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080053
com.airdoc.mpd:styleable/OnSwipe = 0x7f140074
com.airdoc.mpd:style/MaterialAlertDialog.Material3.Animation = 0x7f130144
com.airdoc.mpd:attr/defaultQueryHint = 0x7f04017e
com.airdoc.mpd:attr/motionDurationShort1 = 0x7f040350
com.airdoc.mpd:attr/contentInsetStart = 0x7f040149
com.airdoc.mpd:attr/defaultMarginsEnabled = 0x7f04017d
com.airdoc.mpd:attr/itemBackground = 0x7f040255
com.airdoc.mpd:attr/colorOnTertiary = 0x7f040117
com.airdoc.mpd:string/abc_menu_sym_shortcut_label = 0x7f120010
com.airdoc.mpd:attr/textColorSearchUrl = 0x7f04049d
com.airdoc.mpd:attr/daySelectedStyle = 0x7f040179
com.airdoc.mpd:styleable/MaterialCheckBoxStates = 0x7f14005b
com.airdoc.mpd:id/exo_overlay = 0x7f0a00ec
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral80 = 0x7f0600d4
com.airdoc.mpd:dimen/compat_notification_large_icon_max_height = 0x7f07005b
com.airdoc.mpd:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f130435
com.airdoc.mpd:color/mtrl_card_view_foreground = 0x7f0602ca
com.airdoc.mpd:color/material_dynamic_tertiary95 = 0x7f06026a
com.airdoc.mpd:drawable/exo_legacy_controls_next = 0x7f0800e2
com.airdoc.mpd:attr/customStringValue = 0x7f040177
com.airdoc.mpd:attr/arcMode = 0x7f04003b
com.airdoc.mpd:color/common_google_signin_btn_text_dark_focused = 0x7f060047
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0600e0
com.airdoc.mpd:attr/lastItemDecorated = 0x7f04027c
com.airdoc.mpd:attr/customColorDrawableValue = 0x7f04016f
com.airdoc.mpd:string/exo_controls_settings_description = 0x7f120056
com.airdoc.mpd:attr/motionProgress = 0x7f04036b
com.airdoc.mpd:drawable/$avd_hide_password__0 = 0x7f080000
com.airdoc.mpd:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0701c4
com.airdoc.mpd:color/m3_ref_palette_tertiary90 = 0x7f060167
com.airdoc.mpd:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f070158
com.airdoc.mpd:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f13011e
com.airdoc.mpd:style/Widget.MaterialComponents.ChipGroup = 0x7f13042e
com.airdoc.mpd:drawable/exo_icon_repeat_all = 0x7f0800d7
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral0 = 0x7f0600c5
com.airdoc.mpd:attr/fontVariationSettings = 0x7f040212
com.airdoc.mpd:attr/headerLayout = 0x7f04021e
com.airdoc.mpd:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.airdoc.mpd:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f130443
com.airdoc.mpd:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f070227
com.airdoc.mpd:color/m3_ref_palette_neutral0 = 0x7f06011e
com.airdoc.mpd:attr/layout_editor_absoluteY = 0x7f0402b6
com.airdoc.mpd:attr/counterEnabled = 0x7f040163
com.airdoc.mpd:dimen/mtrl_navigation_rail_icon_margin = 0x7f0702eb
com.airdoc.mpd:attr/insetForeground = 0x7f04024f
com.airdoc.mpd:color/material_personalized_color_on_error = 0x7f06028a
com.airdoc.mpd:attr/SharedValueId = 0x7f040001
com.airdoc.mpd:id/design_menu_item_action_area_stub = 0x7f0a00ab
com.airdoc.mpd:attr/dayTodayStyle = 0x7f04017b
com.airdoc.mpd:id/useLogo = 0x7f0a02ae
com.airdoc.mpd:id/tv_user_phone = 0x7f0a02a6
com.airdoc.mpd:id/overshoot = 0x7f0a01de
com.airdoc.mpd:id/exo_extra_controls_scroll_view = 0x7f0a00e1
com.airdoc.mpd:attr/lottie_defaultFontFileExtension = 0x7f0402e5
com.airdoc.mpd:id/mtrl_calendar_months = 0x7f0a01a4
com.airdoc.mpd:drawable/selector_read_track_head_map_icon = 0x7f0801c0
com.airdoc.mpd:color/accent_material_light = 0x7f06001a
com.airdoc.mpd:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f070253
com.airdoc.mpd:integer/material_motion_duration_medium_2 = 0x7f0b002c
com.airdoc.mpd:attr/suggestionRowLayout = 0x7f040447
com.airdoc.mpd:styleable/BottomAppBar = 0x7f140016
com.airdoc.mpd:color/m3_ref_palette_dynamic_secondary99 = 0x7f060103
com.airdoc.mpd:anim/m3_side_sheet_enter_from_right = 0x7f010027
com.airdoc.mpd:string/str_browser_scan = 0x7f1200f2
com.airdoc.mpd:attr/colorOnErrorContainer = 0x7f04010a
com.airdoc.mpd:layout/material_time_chip = 0x7f0d005f
com.airdoc.mpd:color/material_harmonized_color_on_error_container = 0x7f060276
com.airdoc.mpd:id/x_right = 0x7f0a02c8
com.airdoc.mpd:attr/imagePanY = 0x7f040243
com.airdoc.mpd:id/accessibility_custom_action_25 = 0x7f0a0022
com.airdoc.mpd:layout/design_layout_tab_text = 0x7f0d002d
com.airdoc.mpd:id/homeAsUp = 0x7f0a012c
com.airdoc.mpd:attr/flow_verticalGap = 0x7f040205
com.airdoc.mpd:attr/extraMultilineHeightEnabled = 0x7f0401d6
com.airdoc.mpd:id/line1 = 0x7f0a0163
com.airdoc.mpd:mipmap/ic_launcher_round = 0x7f0f0001
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary90 = 0x7f0600f4
com.airdoc.mpd:attr/cornerFamilyBottomRight = 0x7f04015a
com.airdoc.mpd:styleable/RecyclerView = 0x7f14007e
com.airdoc.mpd:color/m3_slider_inactive_track_color = 0x7f06016f
com.airdoc.mpd:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0e0139
com.airdoc.mpd:attr/itemMinHeight = 0x7f04025d
com.airdoc.mpd:drawable/exo_styled_controls_fullscreen_enter = 0x7f0800f9
com.airdoc.mpd:dimen/mtrl_btn_z = 0x7f07028c
com.airdoc.mpd:attr/actionBarItemBackground = 0x7f040003
com.airdoc.mpd:style/ThemeOverlay.Material3.TabLayout = 0x7f1302de
com.airdoc.mpd:attr/customBoolean = 0x7f04016e
com.airdoc.mpd:attr/listPopupWindowStyle = 0x7f0402d4
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_primary = 0x7f0601b8
com.airdoc.mpd:attr/dialogCornerRadius = 0x7f040185
com.airdoc.mpd:attr/selectorSize = 0x7f0403e7
com.airdoc.mpd:attr/contentPaddingTop = 0x7f040151
com.airdoc.mpd:color/material_personalized_color_text_primary_inverse = 0x7f0602ad
com.airdoc.mpd:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1301ce
com.airdoc.mpd:attr/defaultScrollFlagsEnabled = 0x7f04017f
com.airdoc.mpd:color/material_dynamic_secondary100 = 0x7f060254
com.airdoc.mpd:attr/contentPaddingStart = 0x7f040150
com.airdoc.mpd:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f13016e
com.airdoc.mpd:attr/chipSurfaceColor = 0x7f0400da
com.airdoc.mpd:dimen/mtrl_shape_corner_size_small_component = 0x7f070300
com.airdoc.mpd:array/domain_name = 0x7f030000
com.airdoc.mpd:color/m3_ref_palette_error50 = 0x7f060117
com.airdoc.mpd:attr/contentPadding = 0x7f04014b
com.airdoc.mpd:color/m3_sys_color_light_tertiary = 0x7f0601fd
com.airdoc.mpd:color/abc_primary_text_material_light = 0x7f06000c
com.airdoc.mpd:id/progressBar = 0x7f0a01f1
com.airdoc.mpd:color/design_icon_tint = 0x7f060073
com.airdoc.mpd:style/TextAppearance.Design.Suffix = 0x7f1301fa
com.airdoc.mpd:attr/contentInsetEnd = 0x7f040145
com.airdoc.mpd:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1303ac
com.airdoc.mpd:attr/alertDialogStyle = 0x7f04002d
com.airdoc.mpd:id/tv_cancel_detection = 0x7f0a0285
com.airdoc.mpd:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0700d1
com.airdoc.mpd:attr/checkedState = 0x7f0400c5
com.airdoc.mpd:attr/materialCalendarMonth = 0x7f04030c
com.airdoc.mpd:dimen/mtrl_calendar_day_height = 0x7f070293
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Display4 = 0x7f13001f
com.airdoc.mpd:attr/perpendicularPath_percent = 0x7f04039e
com.airdoc.mpd:drawable/common_eff3f6_round_20_bg = 0x7f08008d
com.airdoc.mpd:attr/circularProgressIndicatorStyle = 0x7f0400dd
com.airdoc.mpd:id/mtrl_calendar_text_input_frame = 0x7f0a01a6
com.airdoc.mpd:attr/bar_gravity = 0x7f04006a
com.airdoc.mpd:attr/constraint_referenced_tags = 0x7f040141
com.airdoc.mpd:dimen/design_snackbar_padding_vertical_2lines = 0x7f070087
com.airdoc.mpd:id/triangle = 0x7f0a0280
com.airdoc.mpd:id/bold = 0x7f0a0061
com.airdoc.mpd:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f130367
com.airdoc.mpd:attr/actionBarSplitStyle = 0x7f040006
com.airdoc.mpd:attr/collapsedTitleGravity = 0x7f0400f4
com.airdoc.mpd:attr/listChoiceIndicatorMultipleAnimated = 0x7f0402ce
com.airdoc.mpd:string/str_test_version = 0x7f120129
com.airdoc.mpd:id/item_touch_helper_previous_elevation = 0x7f0a013e
com.airdoc.mpd:color/exo_white_opacity_70 = 0x7f060082
com.airdoc.mpd:id/right = 0x7f0a0202
com.airdoc.mpd:id/et_age = 0x7f0a00cd
com.airdoc.mpd:animator/design_appbar_state_list_animator = 0x7f020000
com.airdoc.mpd:attr/clockHandColor = 0x7f0400e6
com.airdoc.mpd:attr/clearsTag = 0x7f0400e3
com.airdoc.mpd:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f13027b
com.airdoc.mpd:string/exo_track_selection_auto = 0x7f12006e
com.airdoc.mpd:attr/checkedTextViewStyle = 0x7f0400c6
com.airdoc.mpd:attr/buttonIconDimen = 0x7f04009b
com.airdoc.mpd:attr/crossfade = 0x7f040169
com.airdoc.mpd:attr/colorSurface = 0x7f04012b
com.airdoc.mpd:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.airdoc.mpd:string/material_timepicker_clock_mode_description = 0x7f12009f
com.airdoc.mpd:dimen/m3_sys_elevation_level4 = 0x7f070210
com.airdoc.mpd:drawable/exo_styled_controls_overflow_show = 0x7f0800fd
com.airdoc.mpd:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f070223
com.airdoc.mpd:attr/isMaterial3Theme = 0x7f040252
com.airdoc.mpd:attr/textAppearanceBodyLarge = 0x7f040473
com.airdoc.mpd:color/m3_ref_palette_neutral60 = 0x7f06012b
com.airdoc.mpd:color/m3_ref_palette_tertiary50 = 0x7f060163
com.airdoc.mpd:attr/bar_height = 0x7f04006b
com.airdoc.mpd:layout/activity_main = 0x7f0d0022
com.airdoc.mpd:styleable/ConstraintLayout_placeholder = 0x7f14002b
com.airdoc.mpd:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0701fb
com.airdoc.mpd:color/m3_sys_color_dark_on_secondary = 0x7f06017e
com.airdoc.mpd:attr/controller_layout_id = 0x7f040155
com.airdoc.mpd:attr/colorOnTertiaryFixedVariant = 0x7f04011a
com.airdoc.mpd:dimen/m3_small_fab_max_image_size = 0x7f070208
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f130035
com.airdoc.mpd:attr/windowFixedWidthMajor = 0x7f040521
com.airdoc.mpd:integer/abc_config_activityShortDur = 0x7f0b0001
com.airdoc.mpd:attr/checkMarkCompat = 0x7f0400b8
com.airdoc.mpd:attr/colorOnSurfaceVariant = 0x7f040116
com.airdoc.mpd:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0e0066
com.airdoc.mpd:anim/m3_bottom_sheet_slide_out = 0x7f010023
com.airdoc.mpd:dimen/design_bottom_navigation_text_size = 0x7f07006a
com.airdoc.mpd:attr/colorOnPrimaryFixedVariant = 0x7f04010e
com.airdoc.mpd:attr/layout_constraintTop_toBottomOf = 0x7f0402aa
com.airdoc.mpd:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080062
com.airdoc.mpd:string/str_detection_code_loading = 0x7f1200fd
com.airdoc.mpd:attr/trackDecorationTintMode = 0x7f0404f4
com.airdoc.mpd:attr/thumbColor = 0x7f0404b6
com.airdoc.mpd:attr/popupWindowStyle = 0x7f0403ab
com.airdoc.mpd:attr/expandedTitleMargin = 0x7f0401c8
com.airdoc.mpd:attr/colorOnSecondaryFixed = 0x7f040112
com.airdoc.mpd:style/Widget.MaterialComponents.Chip.Action = 0x7f13042a
com.airdoc.mpd:dimen/notification_main_column_padding_top = 0x7f07032d
com.airdoc.mpd:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0e008d
com.airdoc.mpd:id/tv_display_viewpoint = 0x7f0a028f
com.airdoc.mpd:layout/design_bottom_sheet_dialog = 0x7f0d0029
com.airdoc.mpd:attr/colorOnPrimaryFixed = 0x7f04010d
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f13017c
com.airdoc.mpd:dimen/m3_carousel_small_item_size_min = 0x7f070115
com.airdoc.mpd:id/async = 0x7f0a0055
com.airdoc.mpd:attr/background = 0x7f04004d
com.airdoc.mpd:attr/prefixTextColor = 0x7f0403ae
com.airdoc.mpd:animator/fragment_fade_enter = 0x7f020005
com.airdoc.mpd:attr/fontProviderCerts = 0x7f04020b
com.airdoc.mpd:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f070262
com.airdoc.mpd:dimen/m3_comp_filled_card_icon_size = 0x7f07014c
com.airdoc.mpd:color/m3_ref_palette_secondary10 = 0x7f060151
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f13047c
com.airdoc.mpd:attr/colorOnError = 0x7f040109
com.airdoc.mpd:color/m3_button_background_color_selector = 0x7f06008b
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0601ac
com.airdoc.mpd:style/Widget.Material3.Chip.Filter.Elevated = 0x7f13038f
com.airdoc.mpd:attr/alertDialogCenterButtons = 0x7f04002c
com.airdoc.mpd:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0e011b
com.airdoc.mpd:attr/colorOnBackground = 0x7f040106
com.airdoc.mpd:color/material_personalized_color_secondary_container = 0x7f06029d
com.airdoc.mpd:integer/m3_sys_motion_duration_long1 = 0x7f0b0016
com.airdoc.mpd:dimen/m3_btn_dialog_btn_min_width = 0x7f0700ee
com.airdoc.mpd:attr/actionModeCloseButtonStyle = 0x7f040013
com.airdoc.mpd:attr/boxStrokeErrorColor = 0x7f04008e
com.airdoc.mpd:attr/layout_constraintCircleAngle = 0x7f040290
com.airdoc.mpd:dimen/mtrl_calendar_month_horizontal_padding = 0x7f0702a5
com.airdoc.mpd:style/Widget.AppCompat.ImageButton = 0x7f130327
com.airdoc.mpd:attr/endIconMode = 0x7f0401b3
com.airdoc.mpd:id/text = 0x7f0a0260
com.airdoc.mpd:attr/colorControlHighlight = 0x7f040102
com.airdoc.mpd:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0401f1
com.airdoc.mpd:attr/chipIconSize = 0x7f0400cd
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1303be
com.airdoc.mpd:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0e012e
com.airdoc.mpd:id/indeterminate = 0x7f0a0138
com.airdoc.mpd:attr/tabUnboundedRipple = 0x7f040469
com.airdoc.mpd:attr/layout_constraintWidth_min = 0x7f0402b2
com.airdoc.mpd:attr/colorOnPrimarySurface = 0x7f04010f
com.airdoc.mpd:id/search_bar = 0x7f0a0216
com.airdoc.mpd:attr/alertDialogTheme = 0x7f04002e
com.airdoc.mpd:color/call_notification_decline_color = 0x7f060036
com.airdoc.mpd:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.airdoc.mpd:id/italic = 0x7f0a013d
com.airdoc.mpd:attr/trackTint = 0x7f0404f7
com.airdoc.mpd:attr/actionBarTabStyle = 0x7f040009
com.airdoc.mpd:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f130478
com.airdoc.mpd:attr/collapsingToolbarLayoutLargeSize = 0x7f0400f7
com.airdoc.mpd:drawable/icon_login_password = 0x7f080158
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1303c8
com.airdoc.mpd:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0e00dd
com.airdoc.mpd:drawable/exo_notification_play = 0x7f0800f0
com.airdoc.mpd:id/software = 0x7f0a0231
com.airdoc.mpd:dimen/m3_side_sheet_modal_elevation = 0x7f070201
com.airdoc.mpd:attr/contentInsetRight = 0x7f040148
com.airdoc.mpd:attr/textAppearanceOverline = 0x7f04048c
com.airdoc.mpd:attr/bottomSheetDialogTheme = 0x7f040083
com.airdoc.mpd:dimen/m3_btn_padding_right = 0x7f0700fe
com.airdoc.mpd:attr/actionModePasteDrawable = 0x7f040019
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1302f2
com.airdoc.mpd:color/material_dynamic_tertiary20 = 0x7f060262
com.airdoc.mpd:attr/layout_constraintHorizontal_bias = 0x7f04029d
com.airdoc.mpd:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0e00e9
com.airdoc.mpd:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0e00c6
com.airdoc.mpd:id/graph = 0x7f0a0122
com.airdoc.mpd:color/m3_ref_palette_neutral94 = 0x7f060131
com.airdoc.mpd:dimen/mtrl_calendar_content_padding = 0x7f070291
com.airdoc.mpd:attr/fastScrollVerticalThumbDrawable = 0x7f0401e3
com.airdoc.mpd:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f130207
com.airdoc.mpd:attr/editTextBackground = 0x7f0401a6
com.airdoc.mpd:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f130427
com.airdoc.mpd:style/Base.Widget.MaterialComponents.Chip = 0x7f130114
com.airdoc.mpd:styleable/Motion = 0x7f140068
com.airdoc.mpd:color/bright_foreground_disabled_material_dark = 0x7f06002d
com.airdoc.mpd:id/submit_area = 0x7f0a024c
com.airdoc.mpd:animator/mtrl_btn_state_list_anim = 0x7f020015
com.airdoc.mpd:attr/chipSpacing = 0x7f0400d2
com.airdoc.mpd:attr/textBackgroundPanX = 0x7f040497
com.airdoc.mpd:color/primary_dark_material_light = 0x7f0602fb
com.airdoc.mpd:attr/imagePanX = 0x7f040242
com.airdoc.mpd:attr/customIntegerValue = 0x7f040173
com.airdoc.mpd:attr/autoSizeMinTextSize = 0x7f040045
com.airdoc.mpd:attr/circularflow_defaultAngle = 0x7f0400df
com.airdoc.mpd:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f130088
com.airdoc.mpd:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f070172
com.airdoc.mpd:style/Widget.Material3.TabLayout.Secondary = 0x7f1303f6
com.airdoc.mpd:color/mtrl_chip_close_icon_tint = 0x7f0602cd
com.airdoc.mpd:attr/suffixTextAppearance = 0x7f040445
com.airdoc.mpd:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0601be
com.airdoc.mpd:attr/chipIconEnabled = 0x7f0400cc
com.airdoc.mpd:dimen/material_textinput_min_width = 0x7f07025e
com.airdoc.mpd:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.airdoc.mpd:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1302ac
com.airdoc.mpd:string/bottomsheet_drag_handle_content_description = 0x7f120023
com.airdoc.mpd:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.airdoc.mpd:drawable/common_google_signin_btn_icon_dark_normal = 0x7f080094
com.airdoc.mpd:attr/floatingActionButtonLargePrimaryStyle = 0x7f0401e6
com.airdoc.mpd:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1303c3
com.airdoc.mpd:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f130469
com.airdoc.mpd:attr/checkedIconVisible = 0x7f0400c4
com.airdoc.mpd:layout/activity_detection_web = 0x7f0d0020
com.airdoc.mpd:attr/titleMarginEnd = 0x7f0404d6
com.airdoc.mpd:color/m3_sys_color_dark_on_primary = 0x7f06017c
com.airdoc.mpd:attr/checkedIconSize = 0x7f0400c2
com.airdoc.mpd:attr/values = 0x7f04050b
com.airdoc.mpd:color/mtrl_outlined_stroke_color = 0x7f0602e5
com.airdoc.mpd:animator/fragment_close_exit = 0x7f020004
com.airdoc.mpd:color/material_dynamic_neutral99 = 0x7f060237
com.airdoc.mpd:dimen/highlight_alpha_material_light = 0x7f0700b8
com.airdoc.mpd:attr/checkedIcon = 0x7f0400be
com.airdoc.mpd:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f070138
com.airdoc.mpd:color/m3_sys_color_on_tertiary_fixed = 0x7f060203
com.airdoc.mpd:attr/carousel_previousState = 0x7f0400b2
com.airdoc.mpd:attr/colorSurfaceInverse = 0x7f040133
com.airdoc.mpd:style/Base.Widget.Material3.ActionMode = 0x7f1300ff
com.airdoc.mpd:attr/carousel_forwardTransition = 0x7f0400af
com.airdoc.mpd:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010021
com.airdoc.mpd:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f070124
com.airdoc.mpd:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1300da
com.airdoc.mpd:attr/carousel_firstView = 0x7f0400ae
com.airdoc.mpd:attr/useMaterialThemeColors = 0x7f040508
com.airdoc.mpd:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f130037
com.airdoc.mpd:dimen/abc_star_medium = 0x7f07003c
com.airdoc.mpd:attr/cardForegroundColor = 0x7f0400a7
com.airdoc.mpd:color/material_dynamic_primary70 = 0x7f06024d
com.airdoc.mpd:animator/m3_appbar_state_list_animator = 0x7f020009
com.airdoc.mpd:attr/lottie_url = 0x7f0402f2
com.airdoc.mpd:style/ExoStyledControls.Button.Bottom.AudioTrack = 0x7f13012e
com.airdoc.mpd:dimen/mtrl_progress_circular_inset_small = 0x7f0702f3
com.airdoc.mpd:attr/dividerThickness = 0x7f04018f
com.airdoc.mpd:drawable/common_google_signin_btn_text_dark_normal = 0x7f08009d
com.airdoc.mpd:attr/homeAsUpIndicator = 0x7f04022f
com.airdoc.mpd:attr/blendSrc = 0x7f04007b
com.airdoc.mpd:id/bottom = 0x7f0a0062
com.airdoc.mpd:color/purple_200 = 0x7f060302
com.airdoc.mpd:id/up = 0x7f0a02ad
com.airdoc.mpd:id/parentRelative = 0x7f0a01e3
com.airdoc.mpd:anim/m3_side_sheet_exit_to_left = 0x7f010028
com.airdoc.mpd:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0e0143
com.airdoc.mpd:attr/motionEasingStandardInterpolator = 0x7f04035f
com.airdoc.mpd:color/m3_chip_background_color = 0x7f060098
com.airdoc.mpd:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f070179
com.airdoc.mpd:color/design_dark_default_color_on_primary = 0x7f060055
com.airdoc.mpd:attr/colorOnSecondaryFixedVariant = 0x7f040113
com.airdoc.mpd:attr/lineSpacing = 0x7f0402cb
com.airdoc.mpd:color/m3_ref_palette_primary100 = 0x7f060145
com.airdoc.mpd:id/center_vertical = 0x7f0a0074
com.airdoc.mpd:color/common_google_signin_btn_text_light_disabled = 0x7f06004b
com.airdoc.mpd:attr/badgeWithTextRadius = 0x7f040065
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f130184
com.airdoc.mpd:id/navigation_bar_item_large_label_view = 0x7f0a01bb
com.airdoc.mpd:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.airdoc.mpd:attr/buffered_color = 0x7f040092
com.airdoc.mpd:attr/boxCornerRadiusTopStart = 0x7f04008c
com.airdoc.mpd:attr/yearTodayStyle = 0x7f040528
com.airdoc.mpd:color/material_slider_active_tick_marks_color = 0x7f0602b5
com.airdoc.mpd:attr/expandedTitleMarginTop = 0x7f0401cc
com.airdoc.mpd:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080029
com.airdoc.mpd:dimen/m3_simple_item_color_selected_alpha = 0x7f070205
com.airdoc.mpd:id/ll_display_viewpoint = 0x7f0a016c
com.airdoc.mpd:attr/colorSurfaceContainerLowest = 0x7f040131
com.airdoc.mpd:attr/textAppearanceLargePopupMenu = 0x7f040487
com.airdoc.mpd:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010018
com.airdoc.mpd:color/exo_edit_mode_background_color = 0x7f06007e
com.airdoc.mpd:string/exo_controls_cc_enabled_description = 0x7f120044
com.airdoc.mpd:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0702c0
com.airdoc.mpd:dimen/material_input_text_to_prefix_suffix_padding = 0x7f07025b
com.airdoc.mpd:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.airdoc.mpd:attr/boxCollapsedPaddingTop = 0x7f040088
com.airdoc.mpd:attr/tabIconTint = 0x7f040450
com.airdoc.mpd:attr/boxBackgroundMode = 0x7f040087
com.airdoc.mpd:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f130426
com.airdoc.mpd:attr/fastScrollVerticalTrackDrawable = 0x7f0401e4
com.airdoc.mpd:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f13043b
com.airdoc.mpd:layout/ime_base_split_test_activity = 0x7f0d004c
com.airdoc.mpd:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f070319
com.airdoc.mpd:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001e
com.airdoc.mpd:attr/logoAdjustViewBounds = 0x7f0402dd
com.airdoc.mpd:attr/tabIndicatorAnimationMode = 0x7f040454
com.airdoc.mpd:color/m3_sys_color_on_primary_fixed_variant = 0x7f060200
com.airdoc.mpd:attr/materialIconButtonFilledTonalStyle = 0x7f04031b
com.airdoc.mpd:id/month_title = 0x7f0a019d
com.airdoc.mpd:id/exo_artwork = 0x7f0a00d3
com.airdoc.mpd:attr/bottomNavigationStyle = 0x7f040082
com.airdoc.mpd:dimen/mtrl_extended_fab_end_padding = 0x7f0702c6
com.airdoc.mpd:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0e00c7
com.airdoc.mpd:attr/titleMarginBottom = 0x7f0404d5
com.airdoc.mpd:color/material_dynamic_secondary50 = 0x7f060258
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents = 0x7f13019b
com.airdoc.mpd:attr/bottomAppBarStyle = 0x7f040080
com.airdoc.mpd:style/Platform.ThemeOverlay.AppCompat = 0x7f13015d
com.airdoc.mpd:integer/m3_sys_motion_duration_long4 = 0x7f0b0019
com.airdoc.mpd:color/m3_ref_palette_dynamic_tertiary40 = 0x7f060109
com.airdoc.mpd:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f130073
com.airdoc.mpd:attr/indicatorDirectionCircular = 0x7f04024a
com.airdoc.mpd:attr/fabCradleVerticalOffset = 0x7f0401dd
com.airdoc.mpd:dimen/mtrl_toolbar_default_height = 0x7f070320
com.airdoc.mpd:anim/m3_side_sheet_exit_to_right = 0x7f010029
com.airdoc.mpd:attr/borderRound = 0x7f04007c
com.airdoc.mpd:color/material_personalized_color_secondary_text = 0x7f06029e
com.airdoc.mpd:color/m3_sys_color_dark_background = 0x7f060173
com.airdoc.mpd:attr/behavior_overlapTop = 0x7f040076
com.airdoc.mpd:interpolator/mtrl_linear_out_slow_in = 0x7f0c0011
com.airdoc.mpd:attr/liftOnScroll = 0x7f0402c6
com.airdoc.mpd:macro/m3_comp_extended_fab_surface_container_color = 0x7f0e0033
com.airdoc.mpd:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0e00ed
com.airdoc.mpd:color/m3_assist_chip_icon_tint_color = 0x7f060088
com.airdoc.mpd:styleable/Constraint = 0x7f140028
com.airdoc.mpd:id/ll_proactively_greet = 0x7f0a0172
com.airdoc.mpd:styleable/MaterialSwitch = 0x7f14005f
com.airdoc.mpd:attr/bottomSheetDragHandleStyle = 0x7f040084
com.airdoc.mpd:style/Widget.Material3.Button.OutlinedButton = 0x7f13037d
com.airdoc.mpd:attr/placeholderTextAppearance = 0x7f0403a1
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_surface = 0x7f0601a8
com.airdoc.mpd:macro/m3_comp_icon_button_selected_icon_color = 0x7f0e005a
com.airdoc.mpd:attr/behavior_draggable = 0x7f040071
com.airdoc.mpd:attr/hideOnScroll = 0x7f040228
com.airdoc.mpd:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f13032d
com.airdoc.mpd:style/ShapeableImage_Rounded_60 = 0x7f1301b5
com.airdoc.mpd:drawable/abc_item_background_holo_light = 0x7f08004c
com.airdoc.mpd:color/background_material_light = 0x7f060020
com.airdoc.mpd:macro/m3_comp_switch_unselected_handle_color = 0x7f0e0135
com.airdoc.mpd:id/video_decoder_gl_surface_view = 0x7f0a02b1
com.airdoc.mpd:color/material_dynamic_neutral60 = 0x7f060232
com.airdoc.mpd:color/m3_elevated_chip_background_color = 0x7f0600ae
com.airdoc.mpd:attr/dropdownListPreferredItemHeight = 0x7f0401a3
com.airdoc.mpd:drawable/icon_help_center_bg = 0x7f080155
com.airdoc.mpd:attr/title = 0x7f0404d0
com.airdoc.mpd:id/mtrl_calendar_day_selector_frame = 0x7f0a01a0
com.airdoc.mpd:attr/materialAlertDialogTitlePanelStyle = 0x7f0402fd
com.airdoc.mpd:drawable/ic_more_settings = 0x7f080137
com.airdoc.mpd:color/material_grey_900 = 0x7f060272
com.airdoc.mpd:dimen/m3_comp_fab_primary_container_height = 0x7f07013a
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_on_background = 0x7f060199
com.airdoc.mpd:attr/hideAnimationBehavior = 0x7f040224
com.airdoc.mpd:dimen/m3_ripple_focused_alpha = 0x7f0701f1
com.airdoc.mpd:id/tv_prompt = 0x7f0a029a
com.airdoc.mpd:attr/itemHorizontalTranslationEnabled = 0x7f040258
com.airdoc.mpd:attr/customFloatValue = 0x7f040172
com.airdoc.mpd:attr/collapsingToolbarLayoutStyle = 0x7f0400fb
com.airdoc.mpd:dimen/mtrl_textinput_start_icon_margin_end = 0x7f07031f
com.airdoc.mpd:dimen/m3_comp_badge_size = 0x7f070123
com.airdoc.mpd:attr/colorOnContainerUnchecked = 0x7f040108
com.airdoc.mpd:attr/barrierMargin = 0x7f04006e
com.airdoc.mpd:color/abc_btn_colored_text_material = 0x7f060003
com.airdoc.mpd:string/exo_controls_custom_playback_speed = 0x7f120045
com.airdoc.mpd:drawable/mtrl_ic_checkbox_unchecked = 0x7f080191
com.airdoc.mpd:color/black_0 = 0x7f060022
com.airdoc.mpd:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f130468
com.airdoc.mpd:id/action_bar_container = 0x7f0a0036
com.airdoc.mpd:attr/tickMarkTint = 0x7f0404c7
com.airdoc.mpd:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0e00d5
com.airdoc.mpd:macro/m3_comp_outlined_autocomplete_menu_list_item_selected_container_color = 0x7f0e00a2
com.airdoc.mpd:id/carryVelocity = 0x7f0a006f
com.airdoc.mpd:macro/m3_comp_badge_large_label_text_type = 0x7f0e0004
com.airdoc.mpd:color/design_dark_default_color_on_background = 0x7f060053
com.airdoc.mpd:attr/lottie_progress = 0x7f0402ec
com.airdoc.mpd:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.airdoc.mpd:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f07030d
com.airdoc.mpd:style/Theme.Material3.Dark = 0x7f130251
com.airdoc.mpd:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010011
com.airdoc.mpd:attr/badgeWithTextHeight = 0x7f040064
com.airdoc.mpd:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1300b3
com.airdoc.mpd:color/material_dynamic_primary95 = 0x7f060250
com.airdoc.mpd:attr/buttonGravity = 0x7f040099
com.airdoc.mpd:macro/m3_comp_filled_autocomplete_menu_list_item_selected_container_color = 0x7f0e0042
com.airdoc.mpd:attr/badgeShapeAppearanceOverlay = 0x7f04005c
com.airdoc.mpd:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0702c7
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary99 = 0x7f0600f6
com.airdoc.mpd:dimen/disabled_alpha_material_dark = 0x7f07008e
com.airdoc.mpd:attr/actionBarStyle = 0x7f040007
com.airdoc.mpd:style/Widget.MaterialComponents.Button.TextButton = 0x7f130420
com.airdoc.mpd:color/switch_thumb_disabled_material_dark = 0x7f06030f
com.airdoc.mpd:color/m3_timepicker_secondary_text_button_text_color = 0x7f060221
com.airdoc.mpd:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f07017f
com.airdoc.mpd:id/exo_playback_speed = 0x7f0a00f0
com.airdoc.mpd:color/white_20 = 0x7f06031c
com.airdoc.mpd:dimen/m3_searchbar_margin_horizontal = 0x7f0701f7
com.airdoc.mpd:drawable/finish_read_bg = 0x7f08010c
com.airdoc.mpd:attr/animateMenuItems = 0x7f040034
com.airdoc.mpd:dimen/mtrl_progress_circular_size_extra_small = 0x7f0702f6
com.airdoc.mpd:attr/keylines = 0x7f040275
com.airdoc.mpd:attr/state_lifted = 0x7f040432
com.airdoc.mpd:color/m3_primary_text_disable_only = 0x7f0600c1
com.airdoc.mpd:id/iv_back = 0x7f0a013f
com.airdoc.mpd:attr/alphabeticModifiers = 0x7f040031
com.airdoc.mpd:style/shape_image_round_25dp = 0x7f130489
com.airdoc.mpd:attr/fabAnchorMode = 0x7f0401d9
com.airdoc.mpd:string/material_motion_easing_standard = 0x7f12009a
com.airdoc.mpd:attr/addElevationShadow = 0x7f04002a
com.airdoc.mpd:color/m3_slider_halo_color = 0x7f06016e
com.airdoc.mpd:attr/actionBarTheme = 0x7f04000b
com.airdoc.mpd:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0e00ac
com.airdoc.mpd:attr/titleMarginTop = 0x7f0404d8
com.airdoc.mpd:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f010010
com.airdoc.mpd:color/m3_calendar_item_stroke_color = 0x7f060091
com.airdoc.mpd:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f1301a2
com.airdoc.mpd:id/design_bottom_sheet = 0x7f0a00a9
com.airdoc.mpd:anim/design_bottom_sheet_slide_in = 0x7f010019
com.airdoc.mpd:attr/warmth = 0x7f040515
com.airdoc.mpd:attr/badgeTextColor = 0x7f040060
com.airdoc.mpd:drawable/ic_call_answer_video_low = 0x7f080114
com.airdoc.mpd:macro/m3_comp_badge_color = 0x7f0e0002
com.airdoc.mpd:attr/materialCalendarFullscreenTheme = 0x7f040304
com.airdoc.mpd:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f130300
com.airdoc.mpd:attr/backHandlingEnabled = 0x7f04004c
com.airdoc.mpd:color/cardview_shadow_start_color = 0x7f06003a
com.airdoc.mpd:color/m3_ref_palette_dynamic_primary20 = 0x7f0600ed
com.airdoc.mpd:style/Widget.Material3.CardView.Outlined = 0x7f13038a
com.airdoc.mpd:integer/exo_media_button_opacity_percentage_enabled = 0x7f0b0009
com.airdoc.mpd:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1300cf
com.airdoc.mpd:dimen/m3_comp_elevated_card_icon_size = 0x7f07012f
com.airdoc.mpd:attr/expandedTitleGravity = 0x7f0401c7
com.airdoc.mpd:attr/itemTextAppearance = 0x7f04026c
com.airdoc.mpd:id/closest = 0x7f0a008c
com.airdoc.mpd:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0e012b
com.airdoc.mpd:color/m3_ref_palette_secondary95 = 0x7f06015b
com.airdoc.mpd:attr/behavior_expandedOffset = 0x7f040072
com.airdoc.mpd:attr/subtitleTextColor = 0x7f040442
com.airdoc.mpd:attr/thumbIconSize = 0x7f0404ba
com.airdoc.mpd:drawable/common_hollow_button_disabled_bg = 0x7f0800a7
com.airdoc.mpd:integer/m3_card_anim_delay_ms = 0x7f0b000f
com.airdoc.mpd:color/m3_ref_palette_tertiary40 = 0x7f060162
com.airdoc.mpd:attr/textAppearanceDisplayLarge = 0x7f040478
com.airdoc.mpd:id/BOTTOM_START = 0x7f0a0002
com.airdoc.mpd:drawable/icon_launcher_network_anomaly = 0x7f080156
com.airdoc.mpd:attr/colorButtonNormal = 0x7f0400ff
com.airdoc.mpd:anim/linear_indeterminate_line2_head_interpolator = 0x7f010020
com.airdoc.mpd:style/ExoStyledControls.Button = 0x7f13012c
com.airdoc.mpd:attr/layout_constraintGuide_end = 0x7f040296
com.airdoc.mpd:attr/clockIcon = 0x7f0400e7
com.airdoc.mpd:color/design_fab_stroke_top_inner_color = 0x7f060071
com.airdoc.mpd:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0701bb
com.airdoc.mpd:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.airdoc.mpd:style/ExoMediaButton.Rewind = 0x7f130129
com.airdoc.mpd:color/bright_foreground_inverse_material_light = 0x7f060030
com.airdoc.mpd:color/m3_sys_color_light_surface_container_highest = 0x7f0601f8
com.airdoc.mpd:attr/badgeShapeAppearance = 0x7f04005b
com.airdoc.mpd:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0601c7
com.airdoc.mpd:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.airdoc.mpd:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.airdoc.mpd:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f130182
com.airdoc.mpd:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0702c1
com.airdoc.mpd:style/Base.Widget.AppCompat.ImageButton = 0x7f1300dd
com.airdoc.mpd:id/fitStart = 0x7f0a0110
com.airdoc.mpd:attr/autoTransition = 0x7f04004a
com.airdoc.mpd:drawable/m3_avd_hide_password = 0x7f080168
com.airdoc.mpd:attr/colorOnSurface = 0x7f040114
com.airdoc.mpd:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f1301a0
com.airdoc.mpd:color/design_bottom_navigation_shadow_color = 0x7f06004f
com.airdoc.mpd:drawable/abc_list_divider_material = 0x7f08004d
com.airdoc.mpd:attr/motionDurationLong4 = 0x7f04034b
com.airdoc.mpd:attr/colorPrimaryDark = 0x7f04011f
com.airdoc.mpd:attr/itemTextColor = 0x7f040270
com.airdoc.mpd:string/material_clock_toggle_content_description = 0x7f120090
com.airdoc.mpd:string/mtrl_picker_range_header_selected = 0x7f1200c9
com.airdoc.mpd:attr/closeIcon = 0x7f0400e9
com.airdoc.mpd:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0701da
com.airdoc.mpd:macro/m3_comp_fab_tertiary_icon_color = 0x7f0e0041
com.airdoc.mpd:id/btn_upload_cache = 0x7f0a0069
com.airdoc.mpd:color/m3_checkbox_button_tint = 0x7f060096
com.airdoc.mpd:drawable/common_google_signin_btn_text_dark = 0x7f08009b
com.airdoc.mpd:attr/barLength = 0x7f040069
com.airdoc.mpd:attr/animateRelativeTo = 0x7f040036
com.airdoc.mpd:layout/mtrl_layout_snackbar_include = 0x7f0d0076
com.airdoc.mpd:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0701f4
com.airdoc.mpd:attr/borderWidth = 0x7f04007e
com.airdoc.mpd:drawable/icon_read_content = 0x7f08015e
com.airdoc.mpd:color/material_dynamic_tertiary100 = 0x7f060261
com.airdoc.mpd:id/enterAlways = 0x7f0a00cb
com.airdoc.mpd:attr/collapsedTitleTextColor = 0x7f0400f6
com.airdoc.mpd:attr/elevationOverlayColor = 0x7f0401ab
com.airdoc.mpd:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0700ea
com.airdoc.mpd:attr/actionBarDivider = 0x7f040002
com.airdoc.mpd:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000e
com.airdoc.mpd:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f130115
com.airdoc.mpd:attr/collapseContentDescription = 0x7f0400f1
com.airdoc.mpd:attr/chainUseRtl = 0x7f0400b7
com.airdoc.mpd:attr/defaultState = 0x7f040180
com.airdoc.mpd:id/month_navigation_next = 0x7f0a019b
com.airdoc.mpd:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f060196
com.airdoc.mpd:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001f
com.airdoc.mpd:id/wrap_content = 0x7f0a02c5
com.airdoc.mpd:color/abc_search_url_text = 0x7f06000d
com.airdoc.mpd:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0e001e
com.airdoc.mpd:drawable/common_google_signin_btn_text_light_normal_background = 0x7f0800a3
com.airdoc.mpd:attr/dialogPreferredPadding = 0x7f040186
com.airdoc.mpd:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.airdoc.mpd:attr/circularflow_defaultRadius = 0x7f0400e0
com.airdoc.mpd:attr/textLocale = 0x7f0404a8
com.airdoc.mpd:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f08002e
com.airdoc.mpd:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f07031b
com.airdoc.mpd:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0600e5
