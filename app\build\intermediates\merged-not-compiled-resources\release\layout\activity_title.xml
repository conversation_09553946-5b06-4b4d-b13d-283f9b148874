<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <FrameLayout
        android:id="@+id/common_title"
        android:layout_width="match_parent"
        android:layout_height="146px"
        android:background="#CD005C"
        android:focusable="true">

        <LinearLayout
            android:id="@+id/ll_back"
            android:layout_width="200px"
            android:layout_height="90px"
            android:layout_marginLeft="60px"
            android:orientation="horizontal"
            android:background="@drawable/common_title_back_bg"
            android:gravity="center"
            android:layout_gravity="center_vertical|left">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="30px"
                android:layout_height="50px"
                android:src="@drawable/icon_common_back" />

            <TextView
                android:id="@+id/tv_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="返回"
                android:textSize="40px"
                android:textColor="@color/white"
                android:layout_marginLeft="15px"
                android:includeFontPadding="false"/>

        </LinearLayout>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="标题"
            android:textSize="50px"
            android:textColor="@color/white"
            android:textStyle="bold"
            android:layout_gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <ImageView
            android:id="@+id/iv_right_btn"
            android:layout_width="wrap_content"
            android:layout_height="90px"
            android:layout_marginRight="60px"
            android:layout_gravity="center_vertical|right"/>

    </FrameLayout>

    <FrameLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</LinearLayout>