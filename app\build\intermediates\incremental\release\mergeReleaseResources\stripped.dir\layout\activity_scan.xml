<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.camera.view.PreviewView
        android:id="@+id/preview_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <ImageView
        android:id="@+id/iv_scan_frame"
        android:layout_width="350dp"
        android:layout_height="350dp"
        android:src="@drawable/ic_scan_frame"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <ImageView
        android:id="@+id/iv_scan_anim"
        android:layout_width="350dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_scan_anim"
        app:layout_constraintBottom_toBottomOf="@+id/iv_scan_frame"
        app:layout_constraintLeft_toLeftOf="@+id/iv_scan_frame"
        app:layout_constraintRight_toRightOf="@+id/iv_scan_frame"/>

    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="43dp"
        android:layout_height="43dp"
        android:src="@drawable/ic_close_scan"
        android:padding="10dp"
        android:layout_marginTop="10dp"
        android:layout_marginStart="10dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:src="@drawable/ic_scan_camera_tips"
        android:layout_marginTop="15dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_camera_is_here"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:layout_marginTop="39dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_align_qr_code_recognition"
        android:textColor="@color/white"
        android:textSize="13sp"
        android:layout_marginBottom="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>