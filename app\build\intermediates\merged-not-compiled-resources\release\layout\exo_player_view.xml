<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

  <androidx.media3.ui.AspectRatioFrameLayout android:id="@id/exo_content_frame"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">

    <!-- Video surface will be inserted as the first child of the content frame. -->

    <View android:id="@id/exo_shutter"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:background="@android:color/black"/>

    <ImageView android:id="@id/exo_artwork"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:scaleType="fitXY"/>

    <androidx.media3.ui.SubtitleView android:id="@id/exo_subtitles"
      android:layout_width="match_parent"
      android:layout_height="match_parent"/>

    <ProgressBar android:id="@id/exo_buffering"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:indeterminate="true"
      android:layout_gravity="center"/>

    <TextView android:id="@id/exo_error_message"
      android:layout_width="wrap_content"
      android:layout_height="@dimen/exo_error_message_height"
      android:layout_gravity="center"
      android:layout_marginBottom="@dimen/exo_error_message_margin_bottom"
      android:gravity="center"
      android:textColor="@color/exo_white"
      android:textSize="@dimen/exo_error_message_text_size"
      android:background="@drawable/exo_rounded_rectangle"
      android:paddingLeft="@dimen/exo_error_message_text_padding_horizontal"
      android:paddingRight="@dimen/exo_error_message_text_padding_horizontal"
      android:paddingTop="@dimen/exo_error_message_text_padding_vertical"
      android:paddingBottom="@dimen/exo_error_message_text_padding_vertical"/>

  </androidx.media3.ui.AspectRatioFrameLayout>

  <FrameLayout android:id="@id/exo_ad_overlay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"/>

  <FrameLayout android:id="@id/exo_overlay"
    android:layout_width="match_parent"
    android:layout_height="match_parent"/>

  <View android:id="@id/exo_controller_placeholder"
    android:layout_width="match_parent"
    android:layout_height="match_parent"/>

</merge>

