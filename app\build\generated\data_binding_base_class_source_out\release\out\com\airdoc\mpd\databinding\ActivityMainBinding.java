// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clMainRoot;

  @NonNull
  public final ConstraintLayout clSetting;

  @NonNull
  public final FrameLayout flStartUpMode;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ImageView ivSetting;

  @NonNull
  public final ImageView ivSettingRedDot;

  @NonNull
  public final TextView tvCopyright;

  @NonNull
  public final View viewConfig;

  private ActivityMainBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clMainRoot, @NonNull ConstraintLayout clSetting,
      @NonNull FrameLayout flStartUpMode, @NonNull ImageView ivLogo, @NonNull ImageView ivSetting,
      @NonNull ImageView ivSettingRedDot, @NonNull TextView tvCopyright, @NonNull View viewConfig) {
    this.rootView = rootView;
    this.clMainRoot = clMainRoot;
    this.clSetting = clSetting;
    this.flStartUpMode = flStartUpMode;
    this.ivLogo = ivLogo;
    this.ivSetting = ivSetting;
    this.ivSettingRedDot = ivSettingRedDot;
    this.tvCopyright = tvCopyright;
    this.viewConfig = viewConfig;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout clMainRoot = (ConstraintLayout) rootView;

      id = R.id.cl_setting;
      ConstraintLayout clSetting = ViewBindings.findChildViewById(rootView, id);
      if (clSetting == null) {
        break missingId;
      }

      id = R.id.fl_start_up_mode;
      FrameLayout flStartUpMode = ViewBindings.findChildViewById(rootView, id);
      if (flStartUpMode == null) {
        break missingId;
      }

      id = R.id.iv_logo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.iv_setting;
      ImageView ivSetting = ViewBindings.findChildViewById(rootView, id);
      if (ivSetting == null) {
        break missingId;
      }

      id = R.id.iv_setting_red_dot;
      ImageView ivSettingRedDot = ViewBindings.findChildViewById(rootView, id);
      if (ivSettingRedDot == null) {
        break missingId;
      }

      id = R.id.tv_copyright;
      TextView tvCopyright = ViewBindings.findChildViewById(rootView, id);
      if (tvCopyright == null) {
        break missingId;
      }

      id = R.id.view_config;
      View viewConfig = ViewBindings.findChildViewById(rootView, id);
      if (viewConfig == null) {
        break missingId;
      }

      return new ActivityMainBinding((ConstraintLayout) rootView, clMainRoot, clSetting,
          flStartUpMode, ivLogo, ivSetting, ivSettingRedDot, tvCopyright, viewConfig);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
