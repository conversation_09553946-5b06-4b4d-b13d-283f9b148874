<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_config" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\activity_config.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/activity_config_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="98" endOffset="39"/></Target><Target id="@+id/tv_sn" view="TextView"><Expressions/><location startLine="13" startOffset="8" endLine="21" endOffset="46"/></Target><Target id="@+id/ll_domain" view="LinearLayout"><Expressions/><location startLine="23" startOffset="8" endLine="47" endOffset="22"/></Target><Target id="@+id/sp_domain_name" view="Spinner"><Expressions/><location startLine="39" startOffset="12" endLine="45" endOffset="41"/></Target><Target id="@+id/ll_display_viewpoint" view="LinearLayout"><Expressions/><location startLine="49" startOffset="8" endLine="83" endOffset="22"/></Target><Target id="@+id/tv_display_viewpoint" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="66" endOffset="52"/></Target><Target id="@+id/switch_display_viewpoint" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="68" startOffset="12" endLine="81" endOffset="50"/></Target><Target id="@+id/tv_mqtt_state" view="TextView"><Expressions/><location startLine="85" startOffset="8" endLine="94" endOffset="38"/></Target></Targets></Layout>