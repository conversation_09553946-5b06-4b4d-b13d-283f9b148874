<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/cl_main_root"><Targets><Target id="@+id/cl_main_root" tag="layout/activity_main_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="75" endOffset="51"/></Target><Target id="@+id/fl_start_up_mode" view="FrameLayout"><Expressions/><location startLine="10" startOffset="4" endLine="13" endOffset="45"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="15" startOffset="4" endLine="24" endOffset="41"/></Target><Target id="@+id/tv_copyright" view="TextView"><Expressions/><location startLine="26" startOffset="4" endLine="36" endOffset="54"/></Target><Target id="@+id/cl_setting" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="38" startOffset="4" endLine="63" endOffset="55"/></Target><Target id="@+id/iv_setting" view="ImageView"><Expressions/><location startLine="47" startOffset="8" endLine="51" endOffset="45"/></Target><Target id="@+id/iv_setting_red_dot" view="ImageView"><Expressions/><location startLine="53" startOffset="8" endLine="61" endOffset="39"/></Target><Target id="@+id/view_config" view="View"><Expressions/><location startLine="65" startOffset="4" endLine="71" endOffset="52"/></Target></Targets></Layout>