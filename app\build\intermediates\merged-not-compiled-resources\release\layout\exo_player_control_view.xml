<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<merge xmlns:android="http://schemas.android.com/apk/res/android">

  <!-- 0dp dimensions are used to prevent this view from influencing the size of
       the parent view if it uses "wrap_content". It is expanded to occupy the
       entirety of the parent in code, after the parent's size has been
       determined. See: https://github.com/google/ExoPlayer/issues/8726.
  -->
  <View android:id="@id/exo_controls_background"
      android:layout_width="0dp"
      android:layout_height="0dp"
      android:background="@color/exo_black_opacity_60"/>

  <FrameLayout android:id="@id/exo_bottom_bar"
      android:layout_width="match_parent"
      android:layout_height="@dimen/exo_styled_bottom_bar_height"
      android:layout_marginTop="@dimen/exo_styled_bottom_bar_margin_top"
      android:layout_gravity="bottom"
      android:background="@color/exo_bottom_bar_background"
      android:layoutDirection="ltr">

    <LinearLayout android:id="@id/exo_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/exo_styled_bottom_bar_time_padding"
        android:paddingEnd="@dimen/exo_styled_bottom_bar_time_padding"
        android:paddingLeft="@dimen/exo_styled_bottom_bar_time_padding"
        android:paddingRight="@dimen/exo_styled_bottom_bar_time_padding"
        android:layout_gravity="center_vertical|start"
        android:layoutDirection="ltr">

      <TextView android:id="@id/exo_position"
          style="@style/ExoStyledControls.TimeText.Position"/>

      <TextView
          style="@style/ExoStyledControls.TimeText.Separator"/>

      <TextView android:id="@id/exo_duration"
          style="@style/ExoStyledControls.TimeText.Duration"/>

    </LinearLayout>

    <LinearLayout android:id="@id/exo_basic_controls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:layoutDirection="ltr">

      <ImageButton android:id="@id/exo_vr"
          style="@style/ExoStyledControls.Button.Bottom.VR"/>

      <ImageButton android:id="@id/exo_shuffle"
          style="@style/ExoStyledControls.Button.Bottom.Shuffle"/>

      <ImageButton android:id="@id/exo_repeat_toggle"
          style="@style/ExoStyledControls.Button.Bottom.RepeatToggle"/>

      <ImageButton android:id="@id/exo_subtitle"
          style="@style/ExoStyledControls.Button.Bottom.CC"/>

      <ImageButton android:id="@id/exo_settings"
          style="@style/ExoStyledControls.Button.Bottom.Settings"/>

      <ImageButton android:id="@id/exo_fullscreen"
          style="@style/ExoStyledControls.Button.Bottom.FullScreen"/>

      <ImageButton android:id="@id/exo_overflow_show"
          style="@style/ExoStyledControls.Button.Bottom.OverflowShow"/>

    </LinearLayout>

    <HorizontalScrollView android:id="@id/exo_extra_controls_scroll_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|end"
        android:visibility="invisible">

      <LinearLayout android:id="@id/exo_extra_controls"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layoutDirection="ltr">

        <ImageButton android:id="@id/exo_overflow_hide"
            style="@style/ExoStyledControls.Button.Bottom.OverflowHide"/>

      </LinearLayout>

    </HorizontalScrollView>

  </FrameLayout>

  <View android:id="@id/exo_progress_placeholder"
      android:layout_width="match_parent"
      android:layout_height="@dimen/exo_styled_progress_layout_height"
      android:layout_gravity="bottom"
      android:layout_marginBottom="@dimen/exo_styled_progress_margin_bottom"/>

  <LinearLayout android:id="@id/exo_minimal_controls"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="bottom|end"
      android:layout_marginBottom="@dimen/exo_styled_minimal_controls_margin_bottom"
      android:orientation="horizontal"
      android:gravity="center_vertical"
      android:layoutDirection="ltr">

    <ImageButton android:id="@id/exo_minimal_fullscreen"
        style="@style/ExoStyledControls.Button.Bottom.FullScreen"/>

  </LinearLayout>

  <LinearLayout
      android:id="@id/exo_center_controls"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="center"
      android:background="@android:color/transparent"
      android:gravity="center"
      android:padding="@dimen/exo_styled_controls_padding"
      android:clipToPadding="false"
      android:layoutDirection="ltr">

    <ImageButton android:id="@id/exo_prev"
        style="@style/ExoStyledControls.Button.Center.Previous"/>

    <include layout="@layout/exo_player_control_rewind_button" />

    <ImageButton android:id="@id/exo_play_pause"
        style="@style/ExoStyledControls.Button.Center.PlayPause"/>

    <include layout="@layout/exo_player_control_ffwd_button" />

    <ImageButton android:id="@id/exo_next"
        style="@style/ExoStyledControls.Button.Center.Next"/>

  </LinearLayout>

</merge>
