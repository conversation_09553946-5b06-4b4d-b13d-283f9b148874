ninja: Entering directory `D:\mpd_app_dev\app\.cxx\RelWithDebInfo\i6g5o5f1\arm64-v8a'
[1/22] Building CXX object CMakeFiles/GazeTracker.dir/Blur.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/Blur.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/Blur.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[2/22] Building CXX object CMakeFiles/GazeTracker.dir/GazeTracker.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeTracker.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeTracker.h:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/gaze_mapping.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[3/22] Building CXX object CMakeFiles/GazeTracker.dir/uploadCloud.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/uploadCloud.cpp:4:
In file included from D:/mpd_app_dev/app/src/main/cpp/uploadCloud.h:10:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[4/22] Building CXX object CMakeFiles/GazeTracker.dir/GazeService.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeService.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeService.h:10:
In file included from D:/mpd_app_dev/app/src/main/cpp/detection.h:7:
In file included from D:/mpd_app_dev/app/src/main/cpp/face_det.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[5/22] Building CXX object CMakeFiles/GazeTracker.dir/poseAlign.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/poseAlign.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/poseAlign.h:7:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[6/22] Building CXX object CMakeFiles/GazeTracker.dir/GazeApplication.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeApplication.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeApplication.h:8:
In file included from D:/mpd_app_dev/app/src/main/cpp/Blur.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[7/22] Building CXX object CMakeFiles/GazeTracker.dir/GazeCalibrate.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeCalibrate.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeCalibrate.h:4:
In file included from D:/mpd_app_dev/app/src/main/cpp/calibration.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[8/22] Building CXX object CMakeFiles/GazeTracker.dir/native-lib.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/native-lib.cpp:4:
In file included from D:/mpd_app_dev/app/src/main/cpp/GazeService.h:10:
In file included from D:/mpd_app_dev/app/src/main/cpp/detection.h:7:
In file included from D:/mpd_app_dev/app/src/main/cpp/face_det.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[9/22] Building CXX object CMakeFiles/GazeTracker.dir/face_det.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/face_det.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/face_det.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[10/22] Building CXX object CMakeFiles/GazeTracker.dir/eye_track.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/eye_track.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/eye_track.h:5:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[11/22] Building CXX object CMakeFiles/GazeTracker.dir/reading.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/reading.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/reading.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[12/22] Building CXX object CMakeFiles/GazeTracker.dir/saveVideos.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/saveVideos.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/saveVideos.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[13/22] Building CXX object CMakeFiles/GazeTracker.dir/detection.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/detection.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/detection.h:7:
In file included from D:/mpd_app_dev/app/src/main/cpp/face_det.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[14/22] Building CXX object CMakeFiles/GazeTracker.dir/lights_det.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/lights_det.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/lights_det.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[15/22] Building CXX object CMakeFiles/GazeTracker.dir/app_stare.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/app_stare.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/app_stare.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[16/22] Building CXX object CMakeFiles/GazeTracker.dir/app_glance.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/app_glance.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/app_glance.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[17/22] Building CXX object CMakeFiles/GazeTracker.dir/kalmanfilter.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
[18/22] Building CXX object CMakeFiles/GazeTracker.dir/gaze_mapping.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/gaze_mapping.cpp:1:
In file included from D:/mpd_app_dev/app/src/main/cpp/gaze_mapping.h:4:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[19/22] Building CXX object CMakeFiles/GazeTracker.dir/app_follow.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/app_follow.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/app_follow.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[20/22] Building CXX object CMakeFiles/GazeTracker.dir/utils.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/utils.cpp:1:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[21/22] Building CXX object CMakeFiles/GazeTracker.dir/calibration.cpp.o
clang++: warning: argument unused during compilation: '-LD:/mpd_app_dev/app/src/main/cpp/../jniLibs/arm64-v8a' [-Wunused-command-line-argument]
In file included from D:/mpd_app_dev/app/src/main/cpp/calibration.cpp:5:
In file included from D:/mpd_app_dev/app/src/main/cpp/calibration.h:8:
D:/mpd_app_dev/app/src/main/cpp/utils.h:16:35: warning: 'filesystem' is deprecated: std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro. [-Wdeprecated-declarations]
namespace fs = std::experimental::filesystem;
                                  ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/filesystem:246:1: note: 'filesystem' has been explicitly marked deprecated here
_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM
^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:46:63: note: expanded from macro '_LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL_FILESYSTEM'
    _LIBCPP_BEGIN_NAMESPACE_EXPERIMENTAL namespace filesystem _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM { \
                                                              ^
C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/25.1.8937393/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1/experimental/__config:42:70: note: expanded from macro '_LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM'
#   define _LIBCPP_DEPRECATED_EXPERIMENTAL_FILESYSTEM __attribute__((deprecated("std::experimental::filesystem has now been deprecated in favor of C++17's std::filesystem. Please stop using it and start using std::filesystem. This experimental version will be removed in LLVM 11. You can remove this warning by defining the _LIBCPP_NO_EXPERIMENTAL_DEPRECATION_WARNING_FILESYSTEM macro.")))
                                                                     ^
1 warning generated.
[22/22] Linking CXX shared library D:\mpd_app_dev\app\build\intermediates\cxx\RelWithDebInfo\i6g5o5f1\obj\arm64-v8a\libGazeTracker.so
