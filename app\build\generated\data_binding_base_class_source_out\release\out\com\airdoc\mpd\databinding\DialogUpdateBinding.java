// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogUpdateBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvAppSize;

  @NonNull
  public final TextView tvDetails;

  @NonNull
  public final TextView tvIntroduction;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvUpdate;

  @NonNull
  public final TextView tvVersion;

  private DialogUpdateBinding(@NonNull ConstraintLayout rootView, @NonNull TextView tvAppName,
      @NonNull TextView tvAppSize, @NonNull TextView tvDetails, @NonNull TextView tvIntroduction,
      @NonNull TextView tvTitle, @NonNull TextView tvUpdate, @NonNull TextView tvVersion) {
    this.rootView = rootView;
    this.tvAppName = tvAppName;
    this.tvAppSize = tvAppSize;
    this.tvDetails = tvDetails;
    this.tvIntroduction = tvIntroduction;
    this.tvTitle = tvTitle;
    this.tvUpdate = tvUpdate;
    this.tvVersion = tvVersion;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogUpdateBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogUpdateBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_update, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogUpdateBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_app_name;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tv_app_size;
      TextView tvAppSize = ViewBindings.findChildViewById(rootView, id);
      if (tvAppSize == null) {
        break missingId;
      }

      id = R.id.tv_details;
      TextView tvDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvDetails == null) {
        break missingId;
      }

      id = R.id.tv_introduction;
      TextView tvIntroduction = ViewBindings.findChildViewById(rootView, id);
      if (tvIntroduction == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_update;
      TextView tvUpdate = ViewBindings.findChildViewById(rootView, id);
      if (tvUpdate == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      return new DialogUpdateBinding((ConstraintLayout) rootView, tvAppName, tvAppSize, tvDetails,
          tvIntroduction, tvTitle, tvUpdate, tvVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
