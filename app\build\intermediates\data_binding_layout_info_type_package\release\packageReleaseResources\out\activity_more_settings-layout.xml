<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_more_settings" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\activity_more_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_more_settings_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="344" endOffset="14"/></Target><Target id="@+id/iv_back" view="ImageView"><Expressions/><location startLine="35" startOffset="16" endLine="42" endOffset="56"/></Target><Target id="@+id/ll_proactively_greet" view="LinearLayout"><Expressions/><location startLine="75" startOffset="16" endLine="104" endOffset="30"/></Target><Target id="@+id/tv_proactively_greet" view="TextView"><Expressions/><location startLine="83" startOffset="20" endLine="91" endOffset="49"/></Target><Target id="@+id/switch_proactively_greet" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="93" startOffset="20" endLine="102" endOffset="89"/></Target><Target id="@+id/ll_test_version" view="LinearLayout"><Expressions/><location startLine="157" startOffset="16" endLine="204" endOffset="30"/></Target><Target id="@+id/tv_test_version" view="TextView"><Expressions/><location startLine="179" startOffset="24" endLine="186" endOffset="53"/></Target><Target id="@+id/btn_update_test" view="TextView"><Expressions/><location startLine="190" startOffset="20" endLine="202" endOffset="49"/></Target><Target id="@+id/ll_data_cache" view="LinearLayout"><Expressions/><location startLine="207" startOffset="16" endLine="254" endOffset="30"/></Target><Target id="@+id/tv_data_cache_status" view="TextView"><Expressions/><location startLine="229" startOffset="24" endLine="236" endOffset="53"/></Target><Target id="@+id/btn_upload_cache" view="TextView"><Expressions/><location startLine="240" startOffset="20" endLine="252" endOffset="49"/></Target><Target id="@+id/ll_fingertip_collection" view="LinearLayout"><Expressions/><location startLine="257" startOffset="16" endLine="286" endOffset="30"/></Target><Target id="@+id/tv_fingertip_collection" view="TextView"><Expressions/><location startLine="265" startOffset="20" endLine="273" endOffset="49"/></Target><Target id="@+id/switch_fingertip_collection" view="androidx.appcompat.widget.SwitchCompat"><Expressions/><location startLine="275" startOffset="20" endLine="284" endOffset="89"/></Target><Target id="@+id/ll_collector_number" view="LinearLayout"><Expressions/><location startLine="289" startOffset="16" endLine="336" endOffset="30"/></Target><Target id="@+id/tv_collector_number" view="TextView"><Expressions/><location startLine="311" startOffset="24" endLine="318" endOffset="53"/></Target><Target id="@+id/btn_change_collector" view="TextView"><Expressions/><location startLine="322" startOffset="20" endLine="334" endOffset="49"/></Target></Targets></Layout>