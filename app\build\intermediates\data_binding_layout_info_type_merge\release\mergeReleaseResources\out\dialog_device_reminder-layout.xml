<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_device_reminder" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\dialog_device_reminder.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_device_reminder_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="112" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="50"/></Target><Target id="@+id/tv_open_date" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="61" endOffset="49"/></Target><Target id="@+id/tv_valid_until" view="TextView"><Expressions/><location startLine="63" startOffset="8" endLine="75" endOffset="51"/></Target><Target id="@+id/tv_cancel" view="TextView"><Expressions/><location startLine="80" startOffset="4" endLine="94" endOffset="60"/></Target><Target id="@+id/tv_ok" view="TextView"><Expressions/><location startLine="96" startOffset="4" endLine="110" endOffset="57"/></Target></Targets></Layout>