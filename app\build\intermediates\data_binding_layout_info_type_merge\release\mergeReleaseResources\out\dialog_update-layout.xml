<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_update" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\dialog_update.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/dialog_update_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="101" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="8" startOffset="4" endLine="19" endOffset="50"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="21" startOffset="4" endLine="32" endOffset="60"/></Target><Target id="@+id/tv_version" view="TextView"><Expressions/><location startLine="34" startOffset="4" endLine="44" endOffset="63"/></Target><Target id="@+id/tv_app_size" view="TextView"><Expressions/><location startLine="46" startOffset="4" endLine="56" endOffset="62"/></Target><Target id="@+id/tv_details" view="TextView"><Expressions/><location startLine="58" startOffset="4" endLine="68" endOffset="63"/></Target><Target id="@+id/tv_introduction" view="TextView"><Expressions/><location startLine="70" startOffset="4" endLine="84" endOffset="61"/></Target><Target id="@+id/tv_update" view="TextView"><Expressions/><location startLine="86" startOffset="4" endLine="99" endOffset="57"/></Target></Targets></Layout>