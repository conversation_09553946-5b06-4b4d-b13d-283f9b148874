// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import com.airdoc.mpd.detection.hrv.HrvWebView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityHrvBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout llLoading;

  @NonNull
  public final TextView loadingText;

  @NonNull
  public final HrvWebView wbHrv;

  private ActivityHrvBinding(@NonNull ConstraintLayout rootView, @NonNull LinearLayout llLoading,
      @NonNull TextView loadingText, @NonNull HrvWebView wbHrv) {
    this.rootView = rootView;
    this.llLoading = llLoading;
    this.loadingText = loadingText;
    this.wbHrv = wbHrv;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityHrvBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityHrvBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_hrv, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityHrvBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ll_loading;
      LinearLayout llLoading = ViewBindings.findChildViewById(rootView, id);
      if (llLoading == null) {
        break missingId;
      }

      id = R.id.loading_text;
      TextView loadingText = ViewBindings.findChildViewById(rootView, id);
      if (loadingText == null) {
        break missingId;
      }

      id = R.id.wb_hrv;
      HrvWebView wbHrv = ViewBindings.findChildViewById(rootView, id);
      if (wbHrv == null) {
        break missingId;
      }

      return new ActivityHrvBinding((ConstraintLayout) rootView, llLoading, loadingText, wbHrv);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
