R_DEF: Internal format may change without notice
local
anim anim_common_loading
array domain_name
color black
color color_333333
color color_4A90E2
color color_666666
color color_999999
color color_cccccc
color color_primary
color color_selector_param_setting_mode
color purple_200
color purple_500
color purple_700
color selector_common_radio_button_tint
color selector_common_switch_compat_track_tint
color selector_input_gender_radio_button_tint
color selector_input_gender_text_color
color teal_200
color teal_700
color white
drawable app_launcher_bg
drawable common_8d82c6_round_bg
drawable common_8f959e_stroke_20_bg
drawable common_black_20_round_bg
drawable common_blue_round_bg
drawable common_ce005c_stroke_20_bg
drawable common_d6dce1_round_bg
drawable common_d7dce9_round_bg
drawable common_ea4e3d_round_bg
drawable common_eb4e89_round_bg
drawable common_eff3f6_round_20_bg
drawable common_gray_round_bg
drawable common_green_round_bg
drawable common_light_blue_round_bg
drawable common_red_round_bg
drawable common_stroke_round_bg
drawable common_white_20_round_20_bg
drawable common_white_round_15_bg
drawable common_white_round_20_bg
drawable common_white_round_25_bg
drawable common_white_round_bg
drawable config_switch_mask_therapy_selected
drawable config_switch_mask_therapy_unselected
drawable finish_read_bg
drawable gradient_pink_background
drawable ic_close_scan
drawable ic_configuration_exception
drawable ic_cross
drawable ic_detection_bg
drawable ic_detection_hrv_bg
drawable ic_device_info
drawable ic_device_info_dialog_bg
drawable ic_device_info_dialog_content_bg
drawable ic_device_info_dialog_left
drawable ic_device_info_dialog_right
drawable ic_female_avatar_round
drawable ic_input_age
drawable ic_input_gender
drawable ic_input_name
drawable ic_input_phone
drawable ic_language_en_us
drawable ic_language_settings
drawable ic_language_zh_cn
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_loading
drawable ic_main_bg
drawable ic_main_bg_1
drawable ic_main_logo
drawable ic_male_avatar_round
drawable ic_menu
drawable ic_more_settings
drawable ic_network_exception
drawable ic_red_dot
drawable ic_refresh_red
drawable ic_refresh_white
drawable ic_scan
drawable ic_scan_anim
drawable ic_scan_camera_tips
drawable ic_scan_frame
drawable ic_scanner_settings
drawable ic_update_bg
drawable ic_user_info_bg
drawable ic_version
drawable ic_view_report
drawable icon_advanced_settings
drawable icon_ai_train_guide_instructions
drawable icon_airdoc_digital_therapy_center
drawable icon_back_black_coarse
drawable icon_back_black_fine
drawable icon_back_fine_line_arrow
drawable icon_back_white_coarse
drawable icon_calibration_m
drawable icon_close_airdoc_ai
drawable icon_device_exception_menu
drawable icon_help_center_bg
drawable icon_launcher_network_anomaly
drawable icon_login_account_number
drawable icon_login_password
drawable icon_main_bg
drawable icon_new_patient
drawable icon_param_preview_bg
drawable icon_patient_library
drawable icon_read_assessment_report
drawable icon_read_content
drawable icon_read_main_ai_logo
drawable icon_read_main_logo
drawable icon_settings_unselect
drawable icon_tsc_bg
drawable icon_version
drawable icon_visual_train_no_open
drawable input_cursor
drawable login_input_bg
drawable main_bt_common_bg
drawable main_input_bg
drawable read_common_bg
drawable read_count_down_bg
drawable read_speed_standard_annotation_bg
drawable rounded_pink_button
drawable seekbar_shaded_area_progress_drawable
drawable seekbar_shaded_area_thumb
drawable selector_check_protocol_red
drawable selector_common_radio_button
drawable selector_input_gender_radio_button
drawable selector_param_setting_mode_bg
drawable selector_param_setting_scale_icon
drawable selector_play_read_track_head_map_icon
drawable selector_read_init_tab_bg
drawable selector_read_track_figure_icon
drawable selector_read_track_head_map_icon
drawable switch_behavior_guidance_style
drawable switch_behavior_guidance_thumb
drawable switch_mask_therapy_style
drawable switch_mask_therapy_thumb
drawable update_progress_drawable
id btn_change_collector
id btn_update_test
id btn_upload_cache
id cl_content
id cl_detection_root
id cl_input_age
id cl_input_gender
id cl_input_name
id cl_input_phone
id cl_language_root
id cl_loading_root
id cl_main_root
id cl_scan_code
id cl_setting
id cl_version
id et_age
id et_detection_code
id et_name
id et_phone
id fl_calibration_root
id fl_start_up_mode
id iv_back
id iv_code
id iv_code_loading
id iv_cross
id iv_detection
id iv_device_info
id iv_exception
id iv_input_age
id iv_input_gender
id iv_input_name
id iv_input_phone
id iv_language
id iv_loading
id iv_logo
id iv_more_settings
id iv_refresh
id iv_scan
id iv_scan_anim
id iv_scan_frame
id iv_scanner_settings
id iv_select
id iv_setting
id iv_setting_red_dot
id iv_user_avatar
id iv_user_info_bg
id iv_version
id iv_view_report
id ll_collector_number
id ll_data_cache
id ll_device_info
id ll_display_viewpoint
id ll_domain
id ll_fingertip_collection
id ll_language
id ll_loading
id ll_more_settings
id ll_proactively_greet
id ll_scanner_settings
id ll_test_version
id ll_view_report
id loading_text
id np_select_age
id preview_view
id progressBar
id rb_confidentiality
id rb_female
id rb_h5_qr_code
id rb_male
id rb_whatsapp_qr_code
id rg_gender
id rg_scan_type
id rv_detection_project
id rv_language
id sp_domain_name
id space_line1
id switch_display_viewpoint
id switch_fingertip_collection
id switch_proactively_greet
id tv_app_name
id tv_app_size
id tv_cancel
id tv_cancel_detection
id tv_code_loading
id tv_collector_number
id tv_copyright
id tv_customer_service_hotline
id tv_data_cache_status
id tv_details
id tv_detection
id tv_detection_code
id tv_device_info
id tv_display_viewpoint
id tv_exception
id tv_fingertip_collection
id tv_i_know
id tv_introduction
id tv_language
id tv_more_settings
id tv_mqtt_state
id tv_ok
id tv_open_date
id tv_proactively_greet
id tv_prompt
id tv_residual_degree
id tv_scan_method
id tv_scanner_settings
id tv_select_qr_code_type_tips
id tv_sn
id tv_start_detection
id tv_test_version
id tv_title
id tv_update
id tv_user_gender
id tv_user_name
id tv_user_phone
id tv_valid_until
id tv_version
id tv_view_report
id version_red_dot
id view_code
id view_config
id wb_detection
id wb_hrv
layout activity_calibration
layout activity_config
layout activity_detection
layout activity_detection1
layout activity_detection_web
layout activity_hrv
layout activity_main
layout activity_more_settings
layout activity_scan
layout activity_update
layout dialog_common_loading
layout dialog_device_info
layout dialog_device_reminder
layout dialog_language_settings
layout dialog_selection_age
layout dialog_startup_mode_settings
layout dialog_update
layout fragment_detection_code_detection
layout fragment_device_exception
layout fragment_input_info_detection
layout fragment_scan_code_detection
layout item_detection
layout item_language_settings
layout layout_menu_popup_window
mipmap ic_launcher
mipmap ic_launcher_round
raw detection_code_last_number
raw mobile_browser
raw phone_last_number
raw please_enter_detection_code
raw please_use
raw proceed_detection
raw scan_code
raw speech_0
raw speech_1
raw speech_2
raw speech_3
raw speech_4
raw speech_5
raw speech_6
raw speech_7
raw speech_8
raw speech_9
raw we_chat
raw welcome_use_stress_tolerance_assessment
raw whatsapp
string app_name
string str_align_qr_code_recognition
string str_app_name_s
string str_app_size_d
string str_browser_scan
string str_camera_is_here
string str_cancel
string str_cancel_detection
string str_change
string str_collector_number
string str_confidential
string str_configuration_exception
string str_customer_service_hotline
string str_data_cache
string str_details
string str_detection_code_loading
string str_detection_code_s
string str_detection_not_available
string str_device_info
string str_device_service_expired
string str_discovering_new_versions
string str_display_viewpoint
string str_environment_configuration
string str_exit
string str_experience_now
string str_fingertip_data_collection
string str_gender_
string str_gender_female
string str_gender_male
string str_get_download_resource_exception
string str_h5_qr_code_tips
string str_hrv_detection_overview
string str_hrv_waiting_text
string str_i_know
string str_id_s
string str_invalid_parameters_required_calibration
string str_language_settings
string str_main_all_rights_reserved
string str_model_version
string str_more_settings
string str_network_exception
string str_none
string str_number_available_times_0
string str_ok
string str_opening_date
string str_phone_last_number_
string str_please_enter_age
string str_please_enter_detection_code
string str_please_enter_name
string str_please_enter_phone
string str_proactively_greet
string str_request_timeout
string str_residual_degree
string str_scanner_settings
string str_select_scan_method
string str_selection_age
string str_settings
string str_start_detection
string str_startup_detection_failed_check_network_and_retry
string str_test_version
string str_unknown
string str_update
string str_upgrade_process_may_take_a_few_minutes
string str_upload
string str_valid_until
string str_version_format
string str_version_number_s
string str_view_report
string str_wechat_scan_code
string str_whatsapp_qr_code_tips
string str_whatsapp_scan
style Theme.AppStartLoad
style Theme.Mpd
style shape_image_circle
style shape_image_round_10dp
style shape_image_round_20dp
style shape_image_round_25dp
style shape_image_round_5dp
style shape_image_top_round_12dp
xml backup_rules
xml data_extraction_rules
xml file_paths
xml network_security_config
