// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDeviceInfoBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView tvCustomerServiceHotline;

  @NonNull
  public final TextView tvException;

  @NonNull
  public final TextView tvIKnow;

  @NonNull
  public final TextView tvOpenDate;

  @NonNull
  public final TextView tvResidualDegree;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvValidUntil;

  private DialogDeviceInfoBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView tvCustomerServiceHotline, @NonNull TextView tvException,
      @NonNull TextView tvIKnow, @NonNull TextView tvOpenDate, @NonNull TextView tvResidualDegree,
      @NonNull TextView tvTitle, @NonNull TextView tvValidUntil) {
    this.rootView = rootView;
    this.tvCustomerServiceHotline = tvCustomerServiceHotline;
    this.tvException = tvException;
    this.tvIKnow = tvIKnow;
    this.tvOpenDate = tvOpenDate;
    this.tvResidualDegree = tvResidualDegree;
    this.tvTitle = tvTitle;
    this.tvValidUntil = tvValidUntil;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDeviceInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDeviceInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_device_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDeviceInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_customer_service_hotline;
      TextView tvCustomerServiceHotline = ViewBindings.findChildViewById(rootView, id);
      if (tvCustomerServiceHotline == null) {
        break missingId;
      }

      id = R.id.tv_exception;
      TextView tvException = ViewBindings.findChildViewById(rootView, id);
      if (tvException == null) {
        break missingId;
      }

      id = R.id.tv_i_know;
      TextView tvIKnow = ViewBindings.findChildViewById(rootView, id);
      if (tvIKnow == null) {
        break missingId;
      }

      id = R.id.tv_open_date;
      TextView tvOpenDate = ViewBindings.findChildViewById(rootView, id);
      if (tvOpenDate == null) {
        break missingId;
      }

      id = R.id.tv_residual_degree;
      TextView tvResidualDegree = ViewBindings.findChildViewById(rootView, id);
      if (tvResidualDegree == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_valid_until;
      TextView tvValidUntil = ViewBindings.findChildViewById(rootView, id);
      if (tvValidUntil == null) {
        break missingId;
      }

      return new DialogDeviceInfoBinding((ConstraintLayout) rootView, tvCustomerServiceHotline,
          tvException, tvIKnow, tvOpenDate, tvResidualDegree, tvTitle, tvValidUntil);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
