1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder9com.airdoc.mpd.detection.hrv.HrvWebView.HrvActionListener0com.airdoc.component.common.ui.web.CustomWebView*com.airdoc.mpd.gaze.camera.ICameraListener1com.airdoc.mpd.gaze.listener.IGazeAppliedListener/com.airdoc.mpd.gaze.listener.IGazeTrackListenerandroid.widget.FrameLayout3com.airdoc.component.common.base.BaseCommonFragmentcom.airdoc.mpd.media.bean.Media&androidx.media3.common.Player.Listener,com.airdoc.component.common.cache.INameSpace)org.java_websocket.server.WebSocketServer3com.airdoc.component.common.base.BaseCommonActivity1com.airdoc.component.common.base.BaseCommonDialogandroid.os.Parcelablekotlin.Enum3com.airdoc.component.common.net.base.BaseRepositoryandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBindingandroid.widget.PopupWindow6com.airdoc.component.common.base.BaseCommonApplication!android.view.View.OnClickListener*com.lepu.blepro.observer.BleChangeObserver8com.airdoc.mpd.detection.DetectionWebView.ActionListenerDcom.airdoc.component.common.ui.web.CustomWebView.CustomWebViewClientFcom.airdoc.component.common.ui.web.CustomWebView.CustomWebChromeClient#androidx.lifecycle.AndroidViewModel)org.java_websocket.client.WebSocketClientandroid.app.Service!androidx.lifecycle.LifecycleOwner!kotlinx.coroutines.CoroutineScopeandroid.view.View-androidx.media3.datasource.DataSource.Factory%androidx.media3.datasource.DataSourceokhttp3.Interceptor7com.airdoc.component.common.net.base.BaseRetrofitClient                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              