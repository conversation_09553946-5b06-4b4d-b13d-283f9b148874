<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_detection1" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\activity_detection1.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout" rootNodeViewId="@+id/cl_detection_root"><Targets><Target id="@+id/cl_detection_root" tag="layout/activity_detection1_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="143" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="9" startOffset="4" endLine="18" endOffset="41"/></Target><Target id="@+id/iv_user_info_bg" view="ImageView"><Expressions/><location startLine="20" startOffset="4" endLine="26" endOffset="55"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="28" startOffset="4" endLine="36" endOffset="64"/></Target><Target id="@+id/tv_user_name" view="TextView"><Expressions/><location startLine="38" startOffset="4" endLine="49" endOffset="25"/></Target><Target id="@+id/tv_user_gender" view="TextView"><Expressions/><location startLine="51" startOffset="4" endLine="62" endOffset="64"/></Target><Target id="@+id/tv_user_phone" view="TextView"><Expressions/><location startLine="64" startOffset="4" endLine="75" endOffset="66"/></Target><Target id="@+id/tv_detection_code" view="TextView"><Expressions/><location startLine="77" startOffset="4" endLine="91" endOffset="68"/></Target><Target id="@+id/tv_cancel_detection" view="TextView"><Expressions/><location startLine="93" startOffset="4" endLine="107" endOffset="34"/></Target><Target id="@+id/cl_content" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="109" startOffset="4" endLine="141" endOffset="55"/></Target><Target id="@+id/wb_hrv" view="com.airdoc.mpd.detection.hrv.HrvWebView"><Expressions/><location startLine="119" startOffset="8" endLine="123" endOffset="61"/></Target><Target id="@+id/ll_loading" view="LinearLayout"><Expressions/><location startLine="125" startOffset="8" endLine="139" endOffset="22"/></Target></Targets></Layout>