<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_input_info_detection" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\fragment_input_info_detection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_input_info_detection_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="208" endOffset="51"/></Target><Target id="@+id/cl_input_name" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="6" startOffset="4" endLine="43" endOffset="55"/></Target><Target id="@+id/et_name" view="EditText"><Expressions/><location startLine="15" startOffset="8" endLine="31" endOffset="49"/></Target><Target id="@+id/iv_input_name" view="ImageView"><Expressions/><location startLine="33" startOffset="8" endLine="41" endOffset="56"/></Target><Target id="@+id/cl_input_gender" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="45" startOffset="4" endLine="114" endOffset="55"/></Target><Target id="@+id/iv_input_gender" view="ImageView"><Expressions/><location startLine="55" startOffset="8" endLine="63" endOffset="56"/></Target><Target id="@+id/rg_gender" view="RadioGroup"><Expressions/><location startLine="65" startOffset="8" endLine="112" endOffset="20"/></Target><Target id="@+id/rb_male" view="RadioButton"><Expressions/><location startLine="73" startOffset="12" endLine="84" endOffset="76"/></Target><Target id="@+id/rb_female" view="RadioButton"><Expressions/><location startLine="86" startOffset="12" endLine="97" endOffset="50"/></Target><Target id="@+id/rb_confidentiality" view="RadioButton"><Expressions/><location startLine="99" startOffset="12" endLine="110" endOffset="50"/></Target><Target id="@+id/cl_input_age" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="116" startOffset="4" endLine="153" endOffset="55"/></Target><Target id="@+id/et_age" view="EditText"><Expressions/><location startLine="125" startOffset="8" endLine="141" endOffset="49"/></Target><Target id="@+id/iv_input_age" view="ImageView"><Expressions/><location startLine="143" startOffset="8" endLine="151" endOffset="56"/></Target><Target id="@+id/cl_input_phone" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="155" startOffset="4" endLine="192" endOffset="55"/></Target><Target id="@+id/et_phone" view="EditText"><Expressions/><location startLine="164" startOffset="8" endLine="180" endOffset="49"/></Target><Target id="@+id/iv_input_phone" view="ImageView"><Expressions/><location startLine="182" startOffset="8" endLine="190" endOffset="56"/></Target><Target id="@+id/tv_start_detection" view="TextView"><Expressions/><location startLine="194" startOffset="4" endLine="206" endOffset="54"/></Target></Targets></Layout>