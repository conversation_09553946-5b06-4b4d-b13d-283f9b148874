{"logs": [{"outputFile": "com.airdoc.mpd.app-mergeReleaseResources-69:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bf04cdc715fc93d5a24d642c24f51c2b\\transformed\\jetified-play-services-base-18.5.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5068,5178,5335,5467,5574,5711,5837,5966,6226,6370,6477,6645,6774,6915,7083,7144,7206", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "5173,5330,5462,5569,5706,5832,5961,6071,6365,6472,6640,6769,6910,7078,7139,7201,7278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\37eb7cb3503359f8e3891a2adf804078\\transformed\\jetified-media3-ui-1.3.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3523,3589,3641,3705,3783,3861,3919", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57,69", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3518,3584,3636,3700,3778,3856,3914,3984"}, "to": {"startLines": "2,11,15,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,591,7348,7434,7523,7607,7699,7790,7866,7931,8020,8113,8184,8252,8313,8381,8536,8694,8848,8915,8997,9068,9148,9239,9333,9399,10125,10178,10236,10284,10345,10407,10483,10545,10609,10670,10731,10795,10860,10926,10978,11042,11120,11198,11256", "endLines": "10,14,18,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57,69", "endOffsets": "375,586,762,7429,7518,7602,7694,7785,7861,7926,8015,8108,8179,8247,8308,8376,8531,8689,8843,8910,8992,9063,9143,9234,9328,9394,9459,10173,10231,10279,10340,10402,10478,10540,10604,10665,10726,10790,10855,10921,10973,11037,11115,11193,11251,11321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\09e94a311f42d674eb715371ac8d596c\\transformed\\material-1.10.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2829,2919,2999,3053,3104,3170,3241,3319,3405,3484,3559,3637,3717,3800,3905,3993,4072,4162,4255,4329,4399,4490,4544,4624,4691,4775,4860,4922,4986,5049,5120,5224,5339,5436,5550,5608,5663", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2824,2914,2994,3048,3099,3165,3236,3314,3400,3479,3554,3632,3712,3795,3900,3988,4067,4157,4250,4324,4394,4485,4539,4619,4686,4770,4855,4917,4981,5044,5115,5219,5334,5431,5545,5603,5658,5742"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3624,3703,3780,3859,3946,4766,4864,4983,7283,11326,11424,11505,11564,11657,11719,11782,11840,11911,11973,12027,12148,12205,12266,12320,12391,12524,12608,12691,12824,12906,12984,13116,13206,13286,13340,13391,13457,13528,13606,13692,13771,13846,13924,14004,14087,14192,14280,14359,14449,14542,14616,14686,14777,14831,14911,14978,15062,15147,15209,15273,15336,15407,15511,15626,15723,15837,15895,15950", "endLines": "22,50,51,52,53,54,62,63,64,83,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,131,89,79,53,50,65,70,77,85,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83", "endOffsets": "927,3698,3775,3854,3941,4026,4859,4978,5063,7343,11419,11500,11559,11652,11714,11777,11835,11906,11968,12022,12143,12200,12261,12315,12386,12519,12603,12686,12819,12901,12979,13111,13201,13281,13335,13386,13452,13523,13601,13687,13766,13841,13919,13999,14082,14187,14275,14354,14444,14537,14611,14681,14772,14826,14906,14973,15057,15142,15204,15268,15331,15402,15506,15621,15718,15832,15890,15945,16029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f784686b41df3e3e9ff94a38ce261387\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6076", "endColumns": "149", "endOffsets": "6221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\efd467293e0b9b5a51777a2be79e83eb\\transformed\\jetified-media3-exoplayer-1.3.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9464,9529,9588,9653,9717,9791,9865,9956,10044", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "9524,9583,9648,9712,9786,9860,9951,10039,10120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7c63b318755d78145d01b8b87b88f3c2\\transformed\\core-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "55,56,57,58,59,60,61,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4031,4129,4231,4332,4430,4535,4647,16115", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "4124,4226,4327,4425,4530,4642,4761,16211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ede9995337ea73b4d0233d500609b091\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,195", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "932,1046,1146,1255,1341,1447,1561,1644,1725,1816,1909,2004,2100,2197,2290,2384,2476,2567,2657,2737,2844,2947,3044,3151,3253,3366,3525,16034", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "1041,1141,1250,1336,1442,1556,1639,1720,1811,1904,1999,2095,2192,2285,2379,2471,2562,2652,2732,2839,2942,3039,3146,3248,3361,3520,3619,16110"}}]}]}