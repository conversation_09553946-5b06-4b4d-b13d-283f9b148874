<dependencies>
  <compile
      roots="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified@jar,androidx.databinding:viewbinding:8.3.1@aar,io.getstream:stream-log-android-file:1.1.4@aar,io.getstream:stream-log-android:1.1.4@aar,com.google.android.material:material:1.10.0@aar,com.google.mlkit:barcode-scanning:17.3.0@aar,com.google.mlkit:face-detection:16.1.7@aar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.11.0@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,com.github.bumptech.glide:okhttp3-integration:4.15.1@aar,com.github.bumptech.glide:glide:4.15.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,io.github.jeremyliao:live-event-bus-x:1.8.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.mlkit:vision-interfaces:16.3.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment-ktx:1.6.1@aar,androidx.activity:activity:1.8.0@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.camera:camera-video:1.4.2@aar,androidx.camera:camera-lifecycle:1.4.2@aar,androidx.camera:camera-view:1.4.2@aar,androidx.camera:camera-camera2:1.4.2@aar,androidx.camera:camera-core:1.4.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.core:core-ktx:1.12.0@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.lzy.net:okgo:3.0.4@aar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8@aar,com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10@aar,com.aliyun.alink.linksdk:lp-network-core:1.0.1.1@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,com.airdoc.component:common:0.2.11-SNAPSHOT@aar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.media3:media3-exoplayer:1.3.1@aar,androidx.media3:media3-common:1.3.1@aar,androidx.annotation:annotation-experimental:1.4.1@aar,androidx.collection:collection-ktx:1.1.0@jar,no.nordicsemi.android:ble:2.2.4@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.15.1@aar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,com.google.firebase:firebase-components:16.1.0@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:16.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,io.getstream:stream-log:1.1.4@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,androidx.constraintlayout:constraintlayout:2.1.4@aar,org.java-websocket:Java-WebSocket:1.5.7@jar,me.jessyan:autosize:1.2.1@aar,androidx.multidex:multidex:2.0.1@aar,io.github.jeremyliao:lebx-processor-gson:1.8.0@aar,com.aliyun.alink.linksdk:lp-public-tmp:2.0.5@aar,com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5@aar,com.aliyun.alink.linksdk:iot-apiclient:1.0.1@jar,com.aliyun.alink.linksdk:lp-connectsdk:1.0.6@aar,com.google.code.gson:gson:2.10.1@jar,com.tencent:mmkv-static:1.2.16@aar,jp.wasabeef:glide-transformations:4.3.0@aar,com.zhy:okhttputils:2.6.2@aar,com.airbnb.android:lottie:6.0.0@aar,com.google.guava:guava:32.1.3-android@jar,com.github.PhilJay:MPAndroidChart:v3.1.0@aar,androidx.media3:media3-ui:1.3.1@aar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.google.errorprone:error_prone_annotations:2.21.1@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar,com.github.bumptech.glide:disklrucache:4.15.1@jar,com.github.bumptech.glide:annotations:4.15.1@jar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.exifinterface:exifinterface:1.3.6@aar,org.slf4j:slf4j-api:2.0.6@jar,androidx.media3:media3-container:1.3.1@aar,androidx.media3:media3-database:1.3.1@aar,androidx.media3:media3-datasource:1.3.1@aar,androidx.media3:media3-decoder:1.3.1@aar,androidx.media3:media3-extractor:1.3.1@aar,com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5@aar,com.aliyun.alink.linksdk:android_alink_id2:1.1.3@aar,com.aliyun.alink.linksdk:tools:1.3.5.1@aar,com.alibaba:fastjson:1.2.40@jar,com.aliyun.alink.linksdk:api-client-biz:1.0.0@aar,com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0@aar,com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0@aar,com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1@aar,com.aliyun.alink.linksdk:public-channel-gateway:1.6.4@aar,com.aliyun.alink.linksdk:opensource-paho:1.2.0.6@aar,com.aliyun.alink.linksdk:iot-h2-stream:1.1.6@aar,com.aliyun.alink.linksdk:iot-h2:1.1.6@aar,commons-codec:commons-codec:1.11@jar,io.netty:netty-all:4.1.23.Final@jar,javax.inject:javax.inject:1@jar,com.google.android.odml:image:1.0.0-beta1@aar,com.google.firebase:firebase-annotations:16.0.0@jar,com.google.j2objc:j2objc-annotations:2.8@jar">
    <dependency
        name="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified@jar"
        simpleName="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar"/>
    <dependency
        name="androidx.databinding:viewbinding:8.3.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="io.getstream:stream-log-android-file:1.1.4@aar"
        simpleName="io.getstream:stream-log-android-file"/>
    <dependency
        name="io.getstream:stream-log-android:1.1.4@aar"
        simpleName="io.getstream:stream-log-android"/>
    <dependency
        name="com.google.android.material:material:1.10.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.google.mlkit:barcode-scanning:17.3.0@aar"
        simpleName="com.google.mlkit:barcode-scanning"/>
    <dependency
        name="com.google.mlkit:face-detection:16.1.7@aar"
        simpleName="com.google.mlkit:face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-face-detection"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.11.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.15.1@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.15.1@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="io.github.jeremyliao:live-event-bus-x:1.8.0@aar"
        simpleName="io.github.jeremyliao:live-event-bus-x"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.3.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.camera:camera-video:1.4.2@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.4.2@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-view:1.4.2@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="androidx.camera:camera-camera2:1.4.2@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.4.2@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.lzy.net:okgo:3.0.4@aar"
        simpleName="com.lzy.net:okgo"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8@aar"
        simpleName="com.aliyun.alink.linksdk:lp-iot-linkkit"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10@aar"
        simpleName="com.aliyun.alink.linksdk:lp-iot-device-manager"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-network-core:1.0.1.1@jar"
        simpleName="com.aliyun.alink.linksdk:lp-network-core"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="com.airdoc.component:common:0.2.11-SNAPSHOT@aar"
        simpleName="com.airdoc.component:common"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.3.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-common:1.3.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="no.nordicsemi.android:ble:2.2.4@aar"
        simpleName="no.nordicsemi.android:ble"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.15.1@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:16.1.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:16.1.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="io.getstream:stream-log:1.1.4@jar"
        simpleName="io.getstream:stream-log"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="org.java-websocket:Java-WebSocket:1.5.7@jar"
        simpleName="org.java-websocket:Java-WebSocket"/>
    <dependency
        name="me.jessyan:autosize:1.2.1@aar"
        simpleName="me.jessyan:autosize"/>
    <dependency
        name="androidx.multidex:multidex:2.0.1@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="io.github.jeremyliao:lebx-processor-gson:1.8.0@aar"
        simpleName="io.github.jeremyliao:lebx-processor-gson"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-tmp:2.0.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-tmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-cmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-apiclient:1.0.1@jar"
        simpleName="com.aliyun.alink.linksdk:iot-apiclient"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-connectsdk:1.0.6@aar"
        simpleName="com.aliyun.alink.linksdk:lp-connectsdk"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.tencent:mmkv-static:1.2.16@aar"
        simpleName="com.tencent:mmkv-static"/>
    <dependency
        name="jp.wasabeef:glide-transformations:4.3.0@aar"
        simpleName="jp.wasabeef:glide-transformations"/>
    <dependency
        name="com.zhy:okhttputils:2.6.2@aar"
        simpleName="com.zhy:okhttputils"/>
    <dependency
        name="com.airbnb.android:lottie:6.0.0@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.media3:media3-ui:1.3.1@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.21.1@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.15.1@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.15.1@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.6@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="androidx.media3:media3-container:1.3.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-database:1.3.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-datasource:1.3.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.3.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-extractor:1.3.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-channel-core"/>
    <dependency
        name="com.aliyun.alink.linksdk:android_alink_id2:1.1.3@aar"
        simpleName="com.aliyun.alink.linksdk:android_alink_id2"/>
    <dependency
        name="com.aliyun.alink.linksdk:tools:1.3.5.1@aar"
        simpleName="com.aliyun.alink.linksdk:tools"/>
    <dependency
        name="com.alibaba:fastjson:1.2.40@jar"
        simpleName="com.alibaba:fastjson"/>
    <dependency
        name="com.aliyun.alink.linksdk:api-client-biz:1.0.0@aar"
        simpleName="com.aliyun.alink.linksdk:api-client-biz"/>
    <dependency
        name="com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0@aar"
        simpleName="com.aliyun.alink.linksdk:public-alcs-cmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0@aar"
        simpleName="com.aliyun.alink.linksdk:android_alcs_lpbs"/>
    <dependency
        name="com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1@aar"
        simpleName="com.aliyun.alink.linksdk:coap-sdk"/>
    <dependency
        name="com.aliyun.alink.linksdk:public-channel-gateway:1.6.4@aar"
        simpleName="com.aliyun.alink.linksdk:public-channel-gateway"/>
    <dependency
        name="com.aliyun.alink.linksdk:opensource-paho:1.2.0.6@aar"
        simpleName="com.aliyun.alink.linksdk:opensource-paho"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-h2-stream:1.1.6@aar"
        simpleName="com.aliyun.alink.linksdk:iot-h2-stream"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-h2:1.1.6@aar"
        simpleName="com.aliyun.alink.linksdk:iot-h2"/>
    <dependency
        name="commons-codec:commons-codec:1.11@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="io.netty:netty-all:4.1.23.Final@jar"
        simpleName="io.netty:netty-all"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.0.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.google.j2objc:j2objc-annotations:2.8@jar"
        simpleName="com.google.j2objc:j2objc-annotations"/>
  </compile>
  <package
      roots="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified@jar,androidx.databinding:viewbinding:8.3.1@aar,io.getstream:stream-log-android-file:1.1.4@aar,io.getstream:stream-log-android:1.1.4@aar,com.airdoc.component:common:0.2.11-SNAPSHOT@aar,com.google.android.material:material:1.10.0@aar,androidx.constraintlayout:constraintlayout:2.1.4@aar,com.airbnb.android:lottie:6.0.0@aar,androidx.camera:camera-video:1.4.2@aar,androidx.camera:camera-lifecycle:1.4.2@aar,androidx.camera:camera-camera2:1.4.2@aar,androidx.camera:camera-core:1.4.2@aar,androidx.camera:camera-view:1.4.2@aar,androidx.appcompat:appcompat-resources:1.6.1@aar,com.google.mlkit:barcode-scanning:17.3.0@aar,com.google.mlkit:face-detection:16.1.7@aar,com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar,com.google.mlkit:barcode-scanning-common:17.0.0@aar,com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar,com.google.mlkit:vision-common:17.3.0@aar,com.google.mlkit:common:18.11.0@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.appcompat:appcompat:1.6.1@aar,androidx.fragment:fragment-ktx:1.6.1@aar,com.github.bumptech.glide:okhttp3-integration:4.15.1@aar,jp.wasabeef:glide-transformations:4.3.0@aar,com.github.bumptech.glide:glide:4.15.1@aar,androidx.viewpager2:viewpager2:1.0.0@aar,io.github.jeremyliao:live-event-bus-x:1.8.0@aar,androidx.lifecycle:lifecycle-extensions:2.2.0@aar,com.google.android.gms:play-services-base:18.5.0@aar,com.google.mlkit:vision-interfaces:16.3.0@aar,com.google.android.gms:play-services-tasks:18.2.0@aar,com.google.android.gms:play-services-basement:18.4.0@aar,androidx.fragment:fragment:1.6.1@aar,androidx.fragment:fragment:1.6.1@aar,androidx.activity:activity-ktx:1.8.0@aar,androidx.activity:activity:1.8.0@aar,androidx.savedstate:savedstate-ktx:1.2.1@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.emoji2:emoji2-views-helper:1.2.0@aar,androidx.emoji2:emoji2:1.2.0@aar,androidx.work:work-runtime-ktx:2.9.0@aar,androidx.work:work-runtime:2.9.0@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-service:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-process:2.6.2@aar,androidx.lifecycle:lifecycle-common:2.6.2@jar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-livedata:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar,androidx.media3:media3-exoplayer:1.3.1@aar,androidx.media3:media3-extractor:1.3.1@aar,androidx.media3:media3-container:1.3.1@aar,androidx.media3:media3-datasource:1.3.1@aar,androidx.media3:media3-decoder:1.3.1@aar,androidx.media3:media3-database:1.3.1@aar,androidx.media3:media3-common:1.3.1@aar,androidx.media3:media3-ui:1.3.1@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.recyclerview:recyclerview:1.3.0@aar,androidx.customview:customview-poolingcontainer:1.0.0@aar,androidx.core:core-ktx:1.12.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.transition:transition:1.2.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.media:media:1.7.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.12.0@aar,androidx.core:core:1.12.0@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-runtime:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar,androidx.concurrent:concurrent-futures-ktx:1.1.0@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,androidx.room:room-ktx:2.5.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,com.lzy.net:okgo:3.0.4@aar,com.zhy:okhttputils:2.6.2@aar,com.squareup.retrofit2:converter-gson:2.11.0@jar,com.squareup.retrofit2:retrofit:2.11.0@jar,com.squareup.okhttp3:logging-interceptor:4.12.0@jar,com.squareup.okhttp3:okhttp-sse:4.10.0@jar,com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8@aar,com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10@aar,com.aliyun.alink.linksdk:lp-network-core:1.0.1.1@jar,com.squareup.okhttp3:okhttp:4.12.0@jar,io.getstream:stream-log-file:1.1.4@jar,io.getstream:stream-log:1.1.4@jar,androidx.collection:collection-ktx:1.1.0@jar,no.nordicsemi.android:ble:2.2.4@aar,com.tencent:mmkv-static:1.2.16@aar,com.github.PhilJay:MPAndroidChart:v3.1.0@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,androidx.cardview:cardview:1.0.0@aar,com.orhanobut:logger:2.2.0@aar,com.github.bumptech.glide:gifdecoder:4.15.1@aar,androidx.tracing:tracing-ktx:1.2.0@aar,androidx.profileinstaller:profileinstaller:1.3.0@aar,androidx.startup:startup-runtime:1.1.1@aar,androidx.tracing:tracing:1.2.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.2.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,androidx.exifinterface:exifinterface:1.3.6@aar,com.google.android.datatransport:transport-backend-cct:2.3.3@aar,com.google.android.datatransport:transport-runtime:2.2.6@aar,com.google.android.datatransport:transport-api:2.2.1@aar,com.google.firebase:firebase-components:16.1.0@aar,com.google.firebase:firebase-encoders-json:17.1.0@aar,com.google.firebase:firebase-encoders:16.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.room:room-runtime:2.5.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.sqlite:sqlite-framework:2.3.0@aar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.room:room-common:2.5.0@jar,androidx.sqlite:sqlite:2.3.0@aar,androidx.annotation:annotation-jvm:1.7.0@jar,org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar,androidx.annotation:annotation-experimental:1.4.1@aar,org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar,com.squareup.okio:okio-jvm:3.6.0@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar,org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar,org.java-websocket:Java-WebSocket:1.5.7@jar,me.jessyan:autosize:1.2.1@aar,androidx.multidex:multidex:2.0.1@aar,io.github.jeremyliao:lebx-processor-gson:1.8.0@aar,com.aliyun.alink.linksdk:lp-public-tmp:2.0.5@aar,com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5@aar,com.aliyun.alink.linksdk:iot-apiclient:1.0.1@jar,com.aliyun.alink.linksdk:lp-connectsdk:1.0.6@aar,com.google.code.gson:gson:2.10.1@jar,com.google.guava:guava:32.1.3-android@jar,com.google.errorprone:error_prone_annotations:2.21.1@jar,androidx.constraintlayout:constraintlayout-core:1.0.4@jar,org.slf4j:slf4j-api:2.0.6@jar,com.github.bumptech.glide:disklrucache:4.15.1@jar,com.github.bumptech.glide:annotations:4.15.1@jar,com.google.auto.value:auto-value-annotations:1.6.3@jar,com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar,com.google.guava:failureaccess:1.0.1@jar,com.google.code.findbugs:jsr305:3.0.2@jar,org.checkerframework:checker-qual:3.37.0@jar,com.aliyun.alink.linksdk:iot-h2-stream:1.1.6@aar,org.jetbrains:annotations:23.0.0@jar,com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1@aar,com.aliyun.alink.linksdk:public-channel-gateway:1.6.4@aar,com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5@aar,com.aliyun.alink.linksdk:android_alink_id2:1.1.3@aar,com.aliyun.alink.linksdk:tools:1.3.5.1@aar,com.aliyun.alink.linksdk:iot-h2:1.1.6@aar,commons-codec:commons-codec:1.11@jar,com.google.android.odml:image:1.0.0-beta1@aar,javax.inject:javax.inject:1@jar,com.google.firebase:firebase-annotations:16.0.0@jar,com.alibaba:fastjson:1.2.40@jar,com.aliyun.alink.linksdk:api-client-biz:1.0.0@aar,com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0@aar,com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0@aar,com.aliyun.alink.linksdk:opensource-paho:1.2.0.6@aar,io.netty:netty-all:4.1.23.Final@jar">
    <dependency
        name="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar:unspecified@jar"
        simpleName="__local_aars__:D:\mpd_app_dev\app\libs\lepu-blepro-1.0.8.aar"/>
    <dependency
        name="androidx.databinding:viewbinding:8.3.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="io.getstream:stream-log-android-file:1.1.4@aar"
        simpleName="io.getstream:stream-log-android-file"/>
    <dependency
        name="io.getstream:stream-log-android:1.1.4@aar"
        simpleName="io.getstream:stream-log-android"/>
    <dependency
        name="com.airdoc.component:common:0.2.11-SNAPSHOT@aar"
        simpleName="com.airdoc.component:common"/>
    <dependency
        name="com.google.android.material:material:1.10.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.airbnb.android:lottie:6.0.0@aar"
        simpleName="com.airbnb.android:lottie"/>
    <dependency
        name="androidx.camera:camera-video:1.4.2@aar"
        simpleName="androidx.camera:camera-video"/>
    <dependency
        name="androidx.camera:camera-lifecycle:1.4.2@aar"
        simpleName="androidx.camera:camera-lifecycle"/>
    <dependency
        name="androidx.camera:camera-camera2:1.4.2@aar"
        simpleName="androidx.camera:camera-camera2"/>
    <dependency
        name="androidx.camera:camera-core:1.4.2@aar"
        simpleName="androidx.camera:camera-core"/>
    <dependency
        name="androidx.camera:camera-view:1.4.2@aar"
        simpleName="androidx.camera:camera-view"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="com.google.mlkit:barcode-scanning:17.3.0@aar"
        simpleName="com.google.mlkit:barcode-scanning"/>
    <dependency
        name="com.google.mlkit:face-detection:16.1.7@aar"
        simpleName="com.google.mlkit:face-detection"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1@aar"
        simpleName="com.google.android.gms:play-services-mlkit-barcode-scanning"/>
    <dependency
        name="com.google.mlkit:barcode-scanning-common:17.0.0@aar"
        simpleName="com.google.mlkit:barcode-scanning-common"/>
    <dependency
        name="com.google.android.gms:play-services-mlkit-face-detection:17.1.0@aar"
        simpleName="com.google.android.gms:play-services-mlkit-face-detection"/>
    <dependency
        name="com.google.mlkit:vision-common:17.3.0@aar"
        simpleName="com.google.mlkit:vision-common"/>
    <dependency
        name="com.google.mlkit:common:18.11.0@aar"
        simpleName="com.google.mlkit:common"/>
    <dependency
        name="androidx.appcompat:appcompat:1.6.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="androidx.fragment:fragment-ktx:1.6.1@aar"
        simpleName="androidx.fragment:fragment-ktx"/>
    <dependency
        name="com.github.bumptech.glide:okhttp3-integration:4.15.1@aar"
        simpleName="com.github.bumptech.glide:okhttp3-integration"/>
    <dependency
        name="jp.wasabeef:glide-transformations:4.3.0@aar"
        simpleName="jp.wasabeef:glide-transformations"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.15.1@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="io.github.jeremyliao:live-event-bus-x:1.8.0@aar"
        simpleName="io.github.jeremyliao:live-event-bus-x"/>
    <dependency
        name="androidx.lifecycle:lifecycle-extensions:2.2.0@aar"
        simpleName="androidx.lifecycle:lifecycle-extensions"/>
    <dependency
        name="com.google.android.gms:play-services-base:18.5.0@aar"
        simpleName="com.google.android.gms:play-services-base"/>
    <dependency
        name="com.google.mlkit:vision-interfaces:16.3.0@aar"
        simpleName="com.google.mlkit:vision-interfaces"/>
    <dependency
        name="com.google.android.gms:play-services-tasks:18.2.0@aar"
        simpleName="com.google.android.gms:play-services-tasks"/>
    <dependency
        name="com.google.android.gms:play-services-basement:18.4.0@aar"
        simpleName="com.google.android.gms:play-services-basement"/>
    <dependency
        name="androidx.fragment:fragment:1.6.1@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity-ktx:1.8.0@aar"
        simpleName="androidx.activity:activity-ktx"/>
    <dependency
        name="androidx.activity:activity:1.8.0@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate-ktx"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2-views-helper"/>
    <dependency
        name="androidx.emoji2:emoji2:1.2.0@aar"
        simpleName="androidx.emoji2:emoji2"/>
    <dependency
        name="androidx.work:work-runtime-ktx:2.9.0@aar"
        simpleName="androidx.work:work-runtime-ktx"/>
    <dependency
        name="androidx.work:work-runtime:2.9.0@aar"
        simpleName="androidx.work:work-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-service:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-service"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-process:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-process"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.6.2@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.media3:media3-exoplayer:1.3.1@aar"
        simpleName="androidx.media3:media3-exoplayer"/>
    <dependency
        name="androidx.media3:media3-extractor:1.3.1@aar"
        simpleName="androidx.media3:media3-extractor"/>
    <dependency
        name="androidx.media3:media3-container:1.3.1@aar"
        simpleName="androidx.media3:media3-container"/>
    <dependency
        name="androidx.media3:media3-datasource:1.3.1@aar"
        simpleName="androidx.media3:media3-datasource"/>
    <dependency
        name="androidx.media3:media3-decoder:1.3.1@aar"
        simpleName="androidx.media3:media3-decoder"/>
    <dependency
        name="androidx.media3:media3-database:1.3.1@aar"
        simpleName="androidx.media3:media3-database"/>
    <dependency
        name="androidx.media3:media3-common:1.3.1@aar"
        simpleName="androidx.media3:media3-common"/>
    <dependency
        name="androidx.media3:media3-ui:1.3.1@aar"
        simpleName="androidx.media3:media3-ui"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.3.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
        simpleName="androidx.customview:customview-poolingcontainer"/>
    <dependency
        name="androidx.core:core-ktx:1.12.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.transition:transition:1.2.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.media:media:1.7.0@aar"
        simpleName="androidx.media:media"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.12.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.concurrent:concurrent-futures-ktx:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="androidx.room:room-ktx:2.5.0@aar"
        simpleName="androidx.room:room-ktx"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="com.lzy.net:okgo:3.0.4@aar"
        simpleName="com.lzy.net:okgo"/>
    <dependency
        name="com.zhy:okhttputils:2.6.2@aar"
        simpleName="com.zhy:okhttputils"/>
    <dependency
        name="com.squareup.retrofit2:converter-gson:2.11.0@jar"
        simpleName="com.squareup.retrofit2:converter-gson"/>
    <dependency
        name="com.squareup.retrofit2:retrofit:2.11.0@jar"
        simpleName="com.squareup.retrofit2:retrofit"/>
    <dependency
        name="com.squareup.okhttp3:logging-interceptor:4.12.0@jar"
        simpleName="com.squareup.okhttp3:logging-interceptor"/>
    <dependency
        name="com.squareup.okhttp3:okhttp-sse:4.10.0@jar"
        simpleName="com.squareup.okhttp3:okhttp-sse"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-iot-linkkit:1.7.3.8@aar"
        simpleName="com.aliyun.alink.linksdk:lp-iot-linkkit"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-iot-device-manager:1.7.5.10@aar"
        simpleName="com.aliyun.alink.linksdk:lp-iot-device-manager"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-network-core:1.0.1.1@jar"
        simpleName="com.aliyun.alink.linksdk:lp-network-core"/>
    <dependency
        name="com.squareup.okhttp3:okhttp:4.12.0@jar"
        simpleName="com.squareup.okhttp3:okhttp"/>
    <dependency
        name="io.getstream:stream-log-file:1.1.4@jar"
        simpleName="io.getstream:stream-log-file"/>
    <dependency
        name="io.getstream:stream-log:1.1.4@jar"
        simpleName="io.getstream:stream-log"/>
    <dependency
        name="androidx.collection:collection-ktx:1.1.0@jar"
        simpleName="androidx.collection:collection-ktx"/>
    <dependency
        name="no.nordicsemi.android:ble:2.2.4@aar"
        simpleName="no.nordicsemi.android:ble"/>
    <dependency
        name="com.tencent:mmkv-static:1.2.16@aar"
        simpleName="com.tencent:mmkv-static"/>
    <dependency
        name="com.github.PhilJay:MPAndroidChart:v3.1.0@aar"
        simpleName="com.github.PhilJay:MPAndroidChart"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.orhanobut:logger:2.2.0@aar"
        simpleName="com.orhanobut:logger"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.15.1@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.tracing:tracing-ktx:1.2.0@aar"
        simpleName="androidx.tracing:tracing-ktx"/>
    <dependency
        name="androidx.profileinstaller:profileinstaller:1.3.0@aar"
        simpleName="androidx.profileinstaller:profileinstaller"/>
    <dependency
        name="androidx.startup:startup-runtime:1.1.1@aar"
        simpleName="androidx.startup:startup-runtime"/>
    <dependency
        name="androidx.tracing:tracing:1.2.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.2.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.google.android.datatransport:transport-backend-cct:2.3.3@aar"
        simpleName="com.google.android.datatransport:transport-backend-cct"/>
    <dependency
        name="com.google.android.datatransport:transport-runtime:2.2.6@aar"
        simpleName="com.google.android.datatransport:transport-runtime"/>
    <dependency
        name="com.google.android.datatransport:transport-api:2.2.1@aar"
        simpleName="com.google.android.datatransport:transport-api"/>
    <dependency
        name="com.google.firebase:firebase-components:16.1.0@aar"
        simpleName="com.google.firebase:firebase-components"/>
    <dependency
        name="com.google.firebase:firebase-encoders-json:17.1.0@aar"
        simpleName="com.google.firebase:firebase-encoders-json"/>
    <dependency
        name="com.google.firebase:firebase-encoders:16.1.0@jar"
        simpleName="com.google.firebase:firebase-encoders"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.room:room-runtime:2.5.0@aar"
        simpleName="androidx.room:room-runtime"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.sqlite:sqlite-framework:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite-framework"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.room:room-common:2.5.0@jar"
        simpleName="androidx.room:room-common"/>
    <dependency
        name="androidx.sqlite:sqlite:2.3.0@aar"
        simpleName="androidx.sqlite:sqlite"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.7.0@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-parcelize-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-parcelize-runtime"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.1@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-android-extensions-runtime"/>
    <dependency
        name="com.squareup.okio:okio-jvm:3.6.0@jar"
        simpleName="com.squareup.okio:okio-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:1.9.22@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.java-websocket:Java-WebSocket:1.5.7@jar"
        simpleName="org.java-websocket:Java-WebSocket"/>
    <dependency
        name="me.jessyan:autosize:1.2.1@aar"
        simpleName="me.jessyan:autosize"/>
    <dependency
        name="androidx.multidex:multidex:2.0.1@aar"
        simpleName="androidx.multidex:multidex"/>
    <dependency
        name="io.github.jeremyliao:lebx-processor-gson:1.8.0@aar"
        simpleName="io.github.jeremyliao:lebx-processor-gson"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-tmp:2.0.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-tmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-cmp:1.9.3.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-cmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-apiclient:1.0.1@jar"
        simpleName="com.aliyun.alink.linksdk:iot-apiclient"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-connectsdk:1.0.6@aar"
        simpleName="com.aliyun.alink.linksdk:lp-connectsdk"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="com.google.guava:guava:32.1.3-android@jar"
        simpleName="com.google.guava:guava"/>
    <dependency
        name="com.google.errorprone:error_prone_annotations:2.21.1@jar"
        simpleName="com.google.errorprone:error_prone_annotations"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
        simpleName="androidx.constraintlayout:constraintlayout-core"/>
    <dependency
        name="org.slf4j:slf4j-api:2.0.6@jar"
        simpleName="org.slf4j:slf4j-api"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.15.1@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.15.1@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="com.google.auto.value:auto-value-annotations:1.6.3@jar"
        simpleName="com.google.auto.value:auto-value-annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="com.google.guava:failureaccess:1.0.1@jar"
        simpleName="com.google.guava:failureaccess"/>
    <dependency
        name="com.google.code.findbugs:jsr305:3.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.checkerframework:checker-qual:3.37.0@jar"
        simpleName="org.checkerframework:checker-qual"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-h2-stream:1.1.6@aar"
        simpleName="com.aliyun.alink.linksdk:iot-h2-stream"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.aliyun.alink.linksdk:coap-sdk:1.0.2sp1@aar"
        simpleName="com.aliyun.alink.linksdk:coap-sdk"/>
    <dependency
        name="com.aliyun.alink.linksdk:public-channel-gateway:1.6.4@aar"
        simpleName="com.aliyun.alink.linksdk:public-channel-gateway"/>
    <dependency
        name="com.aliyun.alink.linksdk:lp-public-channel-core:0.7.7.5@aar"
        simpleName="com.aliyun.alink.linksdk:lp-public-channel-core"/>
    <dependency
        name="com.aliyun.alink.linksdk:android_alink_id2:1.1.3@aar"
        simpleName="com.aliyun.alink.linksdk:android_alink_id2"/>
    <dependency
        name="com.aliyun.alink.linksdk:tools:1.3.5.1@aar"
        simpleName="com.aliyun.alink.linksdk:tools"/>
    <dependency
        name="com.aliyun.alink.linksdk:iot-h2:1.1.6@aar"
        simpleName="com.aliyun.alink.linksdk:iot-h2"/>
    <dependency
        name="commons-codec:commons-codec:1.11@jar"
        simpleName="commons-codec:commons-codec"/>
    <dependency
        name="com.google.android.odml:image:1.0.0-beta1@aar"
        simpleName="com.google.android.odml:image"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.google.firebase:firebase-annotations:16.0.0@jar"
        simpleName="com.google.firebase:firebase-annotations"/>
    <dependency
        name="com.alibaba:fastjson:1.2.40@jar"
        simpleName="com.alibaba:fastjson"/>
    <dependency
        name="com.aliyun.alink.linksdk:api-client-biz:1.0.0@aar"
        simpleName="com.aliyun.alink.linksdk:api-client-biz"/>
    <dependency
        name="com.aliyun.alink.linksdk:android_alcs_lpbs:1.7.0@aar"
        simpleName="com.aliyun.alink.linksdk:android_alcs_lpbs"/>
    <dependency
        name="com.aliyun.alink.linksdk:public-alcs-cmp:1.6.0@aar"
        simpleName="com.aliyun.alink.linksdk:public-alcs-cmp"/>
    <dependency
        name="com.aliyun.alink.linksdk:opensource-paho:1.2.0.6@aar"
        simpleName="com.aliyun.alink.linksdk:opensource-paho"/>
    <dependency
        name="io.netty:netty-all:4.1.23.Final@jar"
        simpleName="io.netty:netty-all"/>
  </package>
</dependencies>
