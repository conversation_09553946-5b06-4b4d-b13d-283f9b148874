// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDeviceExceptionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView ivException;

  @NonNull
  public final ImageView ivRefresh;

  @NonNull
  public final TextView tvException;

  private FragmentDeviceExceptionBinding(@NonNull ConstraintLayout rootView,
      @NonNull ImageView ivException, @NonNull ImageView ivRefresh, @NonNull TextView tvException) {
    this.rootView = rootView;
    this.ivException = ivException;
    this.ivRefresh = ivRefresh;
    this.tvException = tvException;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDeviceExceptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDeviceExceptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_device_exception, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDeviceExceptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_exception;
      ImageView ivException = ViewBindings.findChildViewById(rootView, id);
      if (ivException == null) {
        break missingId;
      }

      id = R.id.iv_refresh;
      ImageView ivRefresh = ViewBindings.findChildViewById(rootView, id);
      if (ivRefresh == null) {
        break missingId;
      }

      id = R.id.tv_exception;
      TextView tvException = ViewBindings.findChildViewById(rootView, id);
      if (tvException == null) {
        break missingId;
      }

      return new FragmentDeviceExceptionBinding((ConstraintLayout) rootView, ivException, ivRefresh,
          tvException);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
