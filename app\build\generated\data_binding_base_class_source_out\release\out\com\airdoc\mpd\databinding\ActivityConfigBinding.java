// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.widget.NestedScrollView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityConfigBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final LinearLayout llDisplayViewpoint;

  @NonNull
  public final LinearLayout llDomain;

  @NonNull
  public final Spinner spDomainName;

  @NonNull
  public final SwitchCompat switchDisplayViewpoint;

  @NonNull
  public final TextView tvDisplayViewpoint;

  @NonNull
  public final TextView tvMqttState;

  @NonNull
  public final TextView tvSn;

  private ActivityConfigBinding(@NonNull NestedScrollView rootView,
      @NonNull LinearLayout llDisplayViewpoint, @NonNull LinearLayout llDomain,
      @NonNull Spinner spDomainName, @NonNull SwitchCompat switchDisplayViewpoint,
      @NonNull TextView tvDisplayViewpoint, @NonNull TextView tvMqttState, @NonNull TextView tvSn) {
    this.rootView = rootView;
    this.llDisplayViewpoint = llDisplayViewpoint;
    this.llDomain = llDomain;
    this.spDomainName = spDomainName;
    this.switchDisplayViewpoint = switchDisplayViewpoint;
    this.tvDisplayViewpoint = tvDisplayViewpoint;
    this.tvMqttState = tvMqttState;
    this.tvSn = tvSn;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityConfigBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityConfigBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_config, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityConfigBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ll_display_viewpoint;
      LinearLayout llDisplayViewpoint = ViewBindings.findChildViewById(rootView, id);
      if (llDisplayViewpoint == null) {
        break missingId;
      }

      id = R.id.ll_domain;
      LinearLayout llDomain = ViewBindings.findChildViewById(rootView, id);
      if (llDomain == null) {
        break missingId;
      }

      id = R.id.sp_domain_name;
      Spinner spDomainName = ViewBindings.findChildViewById(rootView, id);
      if (spDomainName == null) {
        break missingId;
      }

      id = R.id.switch_display_viewpoint;
      SwitchCompat switchDisplayViewpoint = ViewBindings.findChildViewById(rootView, id);
      if (switchDisplayViewpoint == null) {
        break missingId;
      }

      id = R.id.tv_display_viewpoint;
      TextView tvDisplayViewpoint = ViewBindings.findChildViewById(rootView, id);
      if (tvDisplayViewpoint == null) {
        break missingId;
      }

      id = R.id.tv_mqtt_state;
      TextView tvMqttState = ViewBindings.findChildViewById(rootView, id);
      if (tvMqttState == null) {
        break missingId;
      }

      id = R.id.tv_sn;
      TextView tvSn = ViewBindings.findChildViewById(rootView, id);
      if (tvSn == null) {
        break missingId;
      }

      return new ActivityConfigBinding((NestedScrollView) rootView, llDisplayViewpoint, llDomain,
          spDomainName, switchDisplayViewpoint, tvDisplayViewpoint, tvMqttState, tvSn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
