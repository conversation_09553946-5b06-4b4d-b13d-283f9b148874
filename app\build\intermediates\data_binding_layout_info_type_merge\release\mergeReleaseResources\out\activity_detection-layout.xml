<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_detection" modulePackage="com.airdoc.mpd" filePath="app\src\main\res\layout\activity_detection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_detection_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="123" endOffset="51"/></Target><Target id="@+id/iv_logo" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="17" endOffset="41"/></Target><Target id="@+id/iv_user_info_bg" view="ImageView"><Expressions/><location startLine="19" startOffset="4" endLine="25" endOffset="55"/></Target><Target id="@+id/iv_user_avatar" view="ImageView"><Expressions/><location startLine="27" startOffset="4" endLine="35" endOffset="64"/></Target><Target id="@+id/tv_user_name" view="TextView"><Expressions/><location startLine="37" startOffset="4" endLine="48" endOffset="25"/></Target><Target id="@+id/tv_user_gender" view="TextView"><Expressions/><location startLine="50" startOffset="4" endLine="61" endOffset="64"/></Target><Target id="@+id/tv_user_phone" view="TextView"><Expressions/><location startLine="63" startOffset="4" endLine="74" endOffset="66"/></Target><Target id="@+id/tv_detection_code" view="TextView"><Expressions/><location startLine="76" startOffset="4" endLine="90" endOffset="68"/></Target><Target id="@+id/tv_cancel_detection" view="TextView"><Expressions/><location startLine="92" startOffset="4" endLine="106" endOffset="34"/></Target><Target id="@+id/rv_detection_project" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="108" startOffset="4" endLine="121" endOffset="54"/></Target></Targets></Layout>