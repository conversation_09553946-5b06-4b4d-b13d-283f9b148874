[{"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_update.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_update.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_device_info.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_device_info.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_config.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_config.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/fragment_input_info_detection.xml", "source": "com.airdoc.mpd.app-main-73:/layout/fragment_input_info_detection.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_startup_mode_settings.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_startup_mode_settings.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_device_reminder.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_device_reminder.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_more_settings.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_more_settings.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_detection.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_detection.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_common_loading.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_common_loading.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_scan.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_scan.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_selection_age.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_selection_age.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/fragment_scan_code_detection.xml", "source": "com.airdoc.mpd.app-main-73:/layout/fragment_scan_code_detection.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_detection_web.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_detection_web.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/fragment_device_exception.xml", "source": "com.airdoc.mpd.app-main-73:/layout/fragment_device_exception.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_hrv.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_hrv.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/fragment_detection_code_detection.xml", "source": "com.airdoc.mpd.app-main-73:/layout/fragment_detection_code_detection.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_detection1.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_detection1.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/item_language_settings.xml", "source": "com.airdoc.mpd.app-main-73:/layout/item_language_settings.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_update.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_update.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/item_detection.xml", "source": "com.airdoc.mpd.app-main-73:/layout/item_detection.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_main.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_main.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/dialog_language_settings.xml", "source": "com.airdoc.mpd.app-main-73:/layout/dialog_language_settings.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/activity_calibration.xml", "source": "com.airdoc.mpd.app-main-73:/layout/activity_calibration.xml"}, {"merged": "com.airdoc.mpd.app-mergeReleaseResources-70:/layout/layout_menu_popup_window.xml", "source": "com.airdoc.mpd.app-main-73:/layout/layout_menu_popup_window.xml"}]