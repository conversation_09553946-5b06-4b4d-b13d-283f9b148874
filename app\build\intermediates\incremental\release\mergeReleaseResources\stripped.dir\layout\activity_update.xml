<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/ic_update_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="@string/app_name"
        android:textColor="@color/white"
        android:textSize="27sp"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/progressBar"
        app:layout_constraintVertical_chainStyle="packed"/>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="450dp"
        android:layout_height="8dp"
        android:layout_marginTop="24dp"
        style="?android:attr/progressBarStyleHorizontal"
        android:max="100"
        tools:progress="50"
        android:progressDrawable="@drawable/update_progress_drawable"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/tv_prompt"/>

    <TextView
        android:id="@+id/tv_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_upgrade_process_may_take_a_few_minutes"
        android:textColor="@color/white"
        android:textSize="12dp"
        android:layout_marginTop="20dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBar"
        app:layout_constraintBottom_toBottomOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>