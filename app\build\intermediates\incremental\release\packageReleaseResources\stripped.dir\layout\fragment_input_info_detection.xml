<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_input_name"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="90dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <EditText
            android:id="@+id/et_name"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:textColorHint="#ABADB0"
            android:hint="@string/str_please_enter_name"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/main_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:paddingStart="65dp"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <ImageView
            android:id="@+id/iv_input_name"
            android:layout_width="18dp"
            android:layout_height="22dp"
            android:src="@drawable/ic_input_name"
            android:layout_marginStart="30dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_input_gender"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:background="@drawable/main_input_bg"
        app:layout_constraintTop_toBottomOf="@+id/cl_input_name"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="90dp">

        <ImageView
            android:id="@+id/iv_input_gender"
            android:layout_width="18dp"
            android:layout_height="22dp"
            android:src="@drawable/ic_input_gender"
            android:layout_marginStart="30dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

        <RadioGroup
            android:id="@+id/rg_gender"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginStart="65dp">

            <RadioButton
                android:id="@+id/rb_male"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:checked="true"
                android:text="@string/str_gender_male"
                android:button="@drawable/selector_input_gender_radio_button"
                android:buttonTint="@color/selector_input_gender_radio_button_tint"
                app:buttonTint="@color/selector_input_gender_radio_button_tint"
                android:paddingLeft="9dp"
                android:textSize="18sp"
                android:textColor="@color/selector_input_gender_text_color"/>

            <RadioButton
                android:id="@+id/rb_female"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_gender_female"
                android:button="@drawable/selector_input_gender_radio_button"
                android:buttonTint="@color/selector_input_gender_radio_button_tint"
                app:buttonTint="@color/selector_input_gender_radio_button_tint"
                android:paddingLeft="9dp"
                android:textSize="18sp"
                android:textColor="@color/selector_input_gender_text_color"
                android:layout_marginStart="25dp"/>

            <RadioButton
                android:id="@+id/rb_confidentiality"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_confidential"
                android:button="@drawable/selector_input_gender_radio_button"
                android:buttonTint="@color/selector_input_gender_radio_button_tint"
                app:buttonTint="@color/selector_input_gender_radio_button_tint"
                android:paddingLeft="9dp"
                android:textSize="18sp"
                android:textColor="@color/selector_input_gender_text_color"
                android:layout_marginStart="25dp"/>

        </RadioGroup>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_input_age"
        android:layout_width="400dp"
        android:layout_height="55dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_input_gender"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="90dp">

        <EditText
            android:id="@+id/et_age"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:textColorHint="#ABADB0"
            android:hint="@string/str_please_enter_age"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/main_input_bg"
            android:imeOptions="actionNext"
            android:singleLine="true"
            android:paddingStart="65dp"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <ImageView
            android:id="@+id/iv_input_age"
            android:layout_width="18dp"
            android:layout_height="22dp"
            android:src="@drawable/ic_input_age"
            android:layout_marginStart="30dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_input_phone"
        android:layout_width="400dp"
        android:layout_height="55dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_input_age"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="90dp">

        <EditText
            android:id="@+id/et_phone"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:textColorHint="#ABADB0"
            android:hint="@string/str_please_enter_phone"
            android:maxLines="1"
            android:includeFontPadding="false"
            android:textCursorDrawable="@drawable/input_cursor"
            android:background="@drawable/main_input_bg"
            android:imeOptions="actionDone"
            android:singleLine="true"
            android:paddingStart="65dp"
            android:focusable="true"
            android:focusableInTouchMode="true" />

        <ImageView
            android:id="@+id/iv_input_phone"
            android:layout_width="18dp"
            android:layout_height="22dp"
            android:src="@drawable/ic_input_phone"
            android:layout_marginStart="30dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_start_detection"
        android:layout_width="400dp"
        android:layout_height="55dp"
        android:background="@drawable/common_eb4e89_round_bg"
        android:text="@string/str_start_detection"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:gravity="center"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="90dp"
        app:layout_constraintTop_toBottomOf="@+id/cl_input_phone"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>