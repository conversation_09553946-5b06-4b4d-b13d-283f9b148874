<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_scan_code">

    <View
        android:id="@+id/view_code"
        android:layout_width="250dp"
        android:layout_height="250dp"
        android:background="@drawable/common_ce005c_stroke_20_bg"
        android:layout_marginTop="150dp"
        android:layout_marginEnd="140dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_code"
        android:layout_width="230dp"
        android:layout_height="230dp"
        app:shapeAppearance="@style/shape_image_round_20dp"
        app:strokeColor="@null"
        android:scaleType="centerInside"
        tools:background="@drawable/common_white_round_20_bg"
        app:layout_constraintLeft_toLeftOf="@+id/view_code"
        app:layout_constraintRight_toRightOf="@+id/view_code"
        app:layout_constraintTop_toTopOf="@+id/view_code"
        app:layout_constraintBottom_toBottomOf="@+id/view_code"/>

    <ImageView
        android:id="@+id/iv_code_loading"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:src="@drawable/ic_loading"
        app:layout_constraintLeft_toLeftOf="@+id/iv_code"
        app:layout_constraintRight_toRightOf="@+id/iv_code"
        app:layout_constraintTop_toTopOf="@+id/iv_code"
        app:layout_constraintBottom_toTopOf="@+id/tv_code_loading"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_code_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_detection_code_loading"
        android:textColor="#CCCCCC"
        android:textSize="16sp"
        android:layout_marginTop="9dp"
        app:layout_constraintLeft_toLeftOf="@+id/view_code"
        app:layout_constraintRight_toRightOf="@+id/view_code"
        app:layout_constraintTop_toBottomOf="@+id/iv_code_loading"
        app:layout_constraintBottom_toBottomOf="@+id/view_code" />

    <TextView
        android:id="@+id/tv_scan_method"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_wechat_scan_code"
        android:textColor="#CE005C"
        android:textSize="20sp"
        android:layout_marginTop="18dp"
        app:layout_constraintLeft_toLeftOf="@+id/view_code"
        app:layout_constraintRight_toRightOf="@+id/view_code"
        app:layout_constraintTop_toBottomOf="@+id/view_code"/>

</androidx.constraintlayout.widget.ConstraintLayout>