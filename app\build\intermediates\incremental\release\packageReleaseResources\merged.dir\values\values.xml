<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="domain_name">
        <item>RELEASE</item>
        <item>TEST</item>
    </string-array>
    <color name="black">#FF000000</color>
    <color name="color_333333">#FF333333</color>
    <color name="color_4A90E2">#FF4A90E2</color>
    <color name="color_666666">#FF666666</color>
    <color name="color_999999">#FF999999</color>
    <color name="color_cccccc">#FFCCCCCC</color>
    <color name="color_primary">#FF6200EE</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">鹰瞳抗压能力检测</string>
    <string name="str_align_qr_code_recognition">对准二维码进行识别</string>
    <string name="str_app_name_s">应用：%1$s</string>
    <string name="str_app_size_d">应用大小：%1$dM</string>
    <string name="str_browser_scan">浏览器扫码</string>
    <string name="str_camera_is_here">摄像头在这里</string>
    <string name="str_cancel">取消</string>
    <string name="str_cancel_detection">退出检测</string>
    <string name="str_change">变更</string>
    <string name="str_collector_number">采集器编号</string>
    <string name="str_confidential">保密</string>
    <string name="str_configuration_exception">机构配置异常，请联系支持团队</string>
    <string name="str_customer_service_hotline">客户服务热线：400–100–3999</string>
    <string name="str_data_cache">数据缓存</string>
    <string name="str_details">详情</string>
    <string name="str_detection_code_loading">检测码加载中…</string>
    <string name="str_detection_code_s">检测码：%1$s</string>
    <string name="str_detection_not_available">该项检测尚未开通</string>
    <string name="str_device_info">设备信息</string>
    <string name="str_device_service_expired">设备服务已到期，\n请联系业务人员续期！</string>
    <string name="str_discovering_new_versions">发现新版本</string>
    <string name="str_display_viewpoint">显示注视点</string>
    <string name="str_environment_configuration">环境配置</string>
    <string name="str_exit">退出</string>
    <string name="str_experience_now">立即体验</string>
    <string name="str_fingertip_data_collection">指尖式数据采集</string>
    <string name="str_gender_">性别：%1$s</string>
    <string name="str_gender_female">女</string>
    <string name="str_gender_male">男</string>
    <string name="str_get_download_resource_exception">获取下载资源异常</string>
    <string name="str_h5_qr_code_tips">使用浏览器扫码，完善个人信息，邮箱接收报告</string>
    <string name="str_hrv_detection_overview">·90秒完成检测\n·多模态数据采集\n·AI人工智能评估\n·五大核心指标精准评估\n·健康建议助力压力管理</string>
    <string name="str_hrv_waiting_text">深呼吸，即将开始检测…</string>
    <string name="str_i_know">我知道了</string>
    <string name="str_id_s">ID：%1$s</string>
    <string name="str_invalid_parameters_required_calibration">参数无效，需要校准</string>
    <string name="str_language_settings">语言设置</string>
    <string name="str_main_all_rights_reserved">©2025 Airdoc鹰瞳 版权所有</string>
    <string name="str_model_version">模型版本</string>
    <string name="str_more_settings">更多设置</string>
    <string name="str_network_exception">当前网络异常，检查网络后再试试吧</string>
    <string name="str_none">无</string>
    <string name="str_number_available_times_0">可用次数为0\n请尽快充值后使用! </string>
    <string name="str_ok">确定</string>
    <string name="str_opening_date">开通日期：%1$s</string>
    <string name="str_phone_last_number_">手机尾号：%1$s</string>
    <string name="str_please_enter_age">请输入您的年龄</string>
    <string name="str_please_enter_detection_code">请输入检测码</string>
    <string name="str_please_enter_name">请输入您的姓名</string>
    <string name="str_please_enter_phone">请输入您的手机号</string>
    <string name="str_proactively_greet">首页语音引导</string>
    <string name="str_request_timeout">请求超时</string>
    <string name="str_residual_degree">剩余次数：%1$d次</string>
    <string name="str_scanner_settings">扫码设置</string>
    <string name="str_select_scan_method">请选择扫码方式</string>
    <string name="str_selection_age">选择年龄</string>
    <string name="str_settings">设置</string>
    <string name="str_start_detection">开始检测</string>
    <string name="str_startup_detection_failed_check_network_and_retry">启动检测失败，检查网络并重试</string>
    <string name="str_test_version">测试版版本</string>
    <string name="str_unknown">未知</string>
    <string name="str_update">更新</string>
    <string name="str_upgrade_process_may_take_a_few_minutes">升级过程可能会持续几分钟，请耐心等待。升级完成之前请勿关闭电源。</string>
    <string name="str_upload">上传</string>
    <string name="str_valid_until">有效期至：%1$s</string>
    <string name="str_version_format">V%1$s</string>
    <string name="str_version_number_s">版本号：%1$s</string>
    <string name="str_view_report">查看报告</string>
    <string name="str_wechat_scan_code">微信扫码检测</string>
    <string name="str_whatsapp_qr_code_tips">使用WhatsApp扫码，完善个人信息，并接受报告推送</string>
    <string name="str_whatsapp_scan">WhatsApp扫码</string>
    <style name="Theme.AppStartLoad" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="Theme.Mpd" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
    <style name="shape_image_circle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="shape_image_round_10dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>
    <style name="shape_image_round_20dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>
    <style name="shape_image_round_25dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">25dp</item>
    </style>
    <style name="shape_image_round_5dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">5dp</item>
    </style>
    <style name="shape_image_top_round_12dp">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">12dp</item>
        <item name="cornerSizeTopRight">12dp</item>
    </style>
</resources>