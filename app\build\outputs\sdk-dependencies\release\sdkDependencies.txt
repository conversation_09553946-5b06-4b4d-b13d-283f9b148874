# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.3.1"
  }
  digests {
    sha256: "\032\311\223\205\232\243\032c\311\320\316~\352\304-\n\364`\365\027\a\220o\204\233\235\245n\273\230\317A"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "no.nordicsemi.android"
    artifactId: "ble"
    version: "2.2.4"
  }
  digests {
    sha256: "2h\\\v\r\371y\240\3557\350s\376T\037\001\331\361\\\200@\205\301\203\372\322&\310\"\364\247\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.getstream"
    artifactId: "stream-log-android"
    version: "1.1.4"
  }
  digests {
    sha256: "=#N\303G\342\034\275\230\270:\366\305\367\343Q\356R;\020N\320\266?\343\262C\376F\325\035="
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.getstream"
    artifactId: "stream-log"
    version: "1.1.4"
  }
  digests {
    sha256: "\b\310\255\245\3072\211{\215\371>\374\356s\036\266p\346\374\351\206\365\345\356v\241\3655O\245\350\342"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.getstream"
    artifactId: "stream-log-android-file"
    version: "1.1.4"
  }
  digests {
    sha256: "\306-\213Z1\217\224\340%\357\315\301\004U~\310c\203.A\340\324Lu\345\277\245s\257 \334]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.getstream"
    artifactId: "stream-log-file"
    version: "1.1.4"
  }
  digests {
    sha256: "E+$\303\030\376%3\246O\317j\264\210\001\005@C\331f\247>\344\251k\210-\226\002\303\t{"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.2.0"
  }
  digests {
    sha256: "\026\327~\214D?\245_\351\246\aM\000D]R\f\245\311\371\023\316\375\277H(5bU!NB"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\370\351U\315\315\355d\005_\330\254;\342\226\017\032l\342}\'$\373\304Pu\234\360\321>\271\354\316"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.6.2"
  }
  digests {
    sha256: "\212\316\2311?\n\346\257G\031K\312\376(\363D\343c\364\322\223\370K+\227\264\201s[\004\336\322"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.1"
  }
  digests {
    sha256: "^\353Yd\250\355\262Yf\325\314y3\376\211><X\342E\254C=5\204x\262\vm\t\324\343"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.1"
  }
  digests {
    sha256: ">E\225\315\251\276\343\222qYY6mpy\a\004\377$\fwenp/<\202 \263\331$Z"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.10.0"
  }
  digests {
    sha256: "- N\344\361\026\271\210\246\246\265\333\214\364=\343\370\247M\n\341\272\027Xo\220\252\343\216>\032-"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.21.1"
  }
  digests {
    sha256: "\321\363\306j\251\032\305%I\340\n\343\262\b\272K\232\367\327-h\3620d5S\276\263\216a\030\254"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.0"
  }
  digests {
    sha256: "\326Y(\240\017cX\232I\342\031%A.\017H\205/\211%K\a\260<\003\rV\017\221\357\374\210"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airdoc.component"
    artifactId: "common"
    version: "0.2.11-SNAPSHOT"
  }
  digests {
    sha256: "oh\301bi\257\3306l\301\330\330r(\037YS\273Xr\336\306}\327`m=\332\272\251\373\303"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\a\1775\354\202\216\024\212\311-\fj\241A\2472\265\353zJ9\026y\256\357\245\237\251\241\245\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.22"
  }
  digests {
    sha256: "\360\0057\307\002>`I3\366\251V;\033\263\270>a\373\375,\303\320\351\220\rak\253hb\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.multidex"
    artifactId: "multidex"
    version: "2.0.1"
  }
  digests {
    sha256: "B\3352\377\237\227\370Wq\270* \000:\215p\366\212\267\264\2722\211d1,\340s&\223\333\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "32.1.3-android"
  }
  digests {
    sha256: " \346\254\211\002\335\364\236x\006\314p\363\005L\215\221\254\313^\357\334\020\363 ~\200\340\2436\262c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.37.0"
  }
  digests {
    sha256: "\344\316\023v\314\'5\341\335\342 \266*\320\221?Q)w\004\332\255\025Z3\363\206\274]\260\331\367"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.tencent"
    artifactId: "mmkv-static"
    version: "1.2.16"
  }
  digests {
    sha256: "\232#1\004\317\324\021\251U\206\371\\T\005\360\224\"]\240\023\032\354 >\302Q\227\364\342@\317\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.15.1"
  }
  digests {
    sha256: "\"\234\'\363\337\232\376U(\306\2575\253@~\'\272\f\234\274\332\216\"\'R\272\320.pt\325%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.15.1"
  }
  digests {
    sha256: "\357\370\204\270$\255y\306 \\\"\355\362F\3501\375\201\035\334\216\240V2oSA1C\360\006\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.15.1"
  }
  digests {
    sha256: "\265\006M\033\345\340e\035\350T)K\271\245\303\234\265\357\270.;\271w\220\265j\263Y\2273vi"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.15.1"
  }
  digests {
    sha256: "\3031\347O(\325s1\235hc\277\276\363i\034\323wU\200\377J3\271^\323\362\'\3143\233\r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "okhttp3-integration"
    version: "4.15.1"
  }
  digests {
    sha256: "\027\325\273S\037\263\232\002\032\361-K;d>\230\224\345\035\340\257:O\251X\353\217\213\213;\355x"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "jp.wasabeef"
    artifactId: "glide-transformations"
    version: "4.3.0"
  }
  digests {
    sha256: "\316:W\350\004V\2428\n\a\263\034aVdJ\324\221\002$$\3378\244}\341\350\024\327F\326\343"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.lzy.net"
    artifactId: "okgo"
    version: "3.0.4"
  }
  digests {
    sha256: "\265b\034\223\3327\255\267\327\307\313\220\302\300\213\313\037\242\237\264\263{\352S\224\027{e\317\312M\035"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.zhy"
    artifactId: "okhttputils"
    version: "2.6.2"
  }
  digests {
    sha256: "\306O\250\312\005\252\371P9\375\2766fz4\342\002\323\272\227\341F\311W\235\f\023\\if\032\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.0.0"
  }
  digests {
    sha256: "\365^\235lK\233\274\376\310\375.\304\357\034\2218B\345\\H\207\023\003Q~UBY\255\331*\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.github.jeremyliao"
    artifactId: "live-event-bus-x"
    version: "1.8.0"
  }
  digests {
    sha256: "\020i\364\230Y\2657\240x\035\372\300D\v\023\360h\203\361q\337\231\025\277LS\021\322\332L\032\215"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-extensions"
    version: "2.2.0"
  }
  digests {
    sha256: "d\214\215\341\321\v\002]RJ.F\254\231O\303\366\277\030h&\300\236\301\246-%\v\361\270w\256"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.github.jeremyliao"
    artifactId: "lebx-processor-gson"
    version: "1.8.0"
  }
  digests {
    sha256: "\216\212\254\334\340E\244\313\332\375\352\351Y\253\314\250\207\256\355\304\222\027\314\035<\'QX:\221-H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-sse"
    version: "4.10.0"
  }
  digests {
    sha256: " \022\333\024\002t\235\222\"JMW\340\377{\3004\200\245\322\245Z\263\371(d\227\304\207O\256\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.11.0"
  }
  digests {
    sha256: "\237O\273\316pr\205\204\373\356\323\215@a\363mDw\350\233\312t\264\342\254\212\353h\031\260\376C"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.11.0"
  }
  digests {
    sha256: "=+Kf\211\tF\204e\305wd0\265r2\']\001eQ? \374\226\260\317\344\252S\023&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.orhanobut"
    artifactId: "logger"
    version: "2.2.0"
  }
  digests {
    sha256: "\034E\231=9\330\231\301@U\305\261\202\327`X=\021g\333\261\n\355NL\022\362\f\037\333S\333"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.java-websocket"
    artifactId: "Java-WebSocket"
    version: "1.5.7"
  }
  digests {
    sha256: "(\3309*\210&`\3466`<\nr\323\341\355]\241\2312\020!FEuN\223e\\\r\200j"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "2.0.6"
  }
  digests {
    sha256: "/*\222\324\020\262h\023\235}c\267^\322^!\231\\\376A\000\301\233\3625w\317\333\310\a{\332"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "me.jessyan"
    artifactId: "autosize"
    version: "1.2.1"
  }
  digests {
    sha256: "\221\245\202a\321\022\2512p.\264\370.\223\2212\023W\321k\330\002`\031H\273\356\036\253\371\"d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.4.2"
  }
  digests {
    sha256: "\273\312\023v\f\341Xu\002\353\025r=\016\027\372\n4\225\320\204\346\t\304\344\310\323\302\341\310\\\370"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "\031h\277R\003\2368cj\246\361\024\315\027\327%i\031\321\350\231t\027qo\357\235\035\241\362M\205"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.4.2"
  }
  digests {
    sha256: "\312\373\226*\251\214\232X\017M$\355\270\357$\273X\277{Z\260j\366\022W\v\357J|\b\357\267"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.4.2"
  }
  digests {
    sha256: "\203U8=\206\006\v\242\254&\017t\223\005\267\243\274\311\372\200\2654\264n\201N!@\207\031\230\316"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-video"
    version: "1.4.2"
  }
  digests {
    sha256: "\362mJz\f\306;@\267\030T\267\300\327\002\355P\317+\277*\204?@\273=r1\3606\004y"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-view"
    version: "1.4.2"
  }
  digests {
    sha256: "\233H\201\344Q3\017\255Xx3\335W\332\'P\204\250\241\003/\224)\303d\302\331d\024&\261#"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.5.0"
  }
  digests {
    sha256: "\020\367m\365b\"SL{\370|\230d=j\017\333~\2354#\226\246y\303\025\341\0206\260E\361"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.0"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.0"
  }
  digests {
    sha256: "\230\202\204\030w\244C\177 \002\355\2355?uzTR\341;J\260\353\361\225\306<\317\016.\bG"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.0"
  }
  digests {
    sha256: "\323\323~$\003\305#\245\316\341\230;\'\246\336z\330\334\273P/Dl?v\374\245\016\327<\345b"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.0"
  }
  digests {
    sha256: "\213[\323\254\357\001\352x\032\205E\275\367\020\261\300a\202Avi\241\246\214\320\256JSl\332\350\'"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.PhilJay"
    artifactId: "MPAndroidChart"
    version: "v3.1.0"
  }
  digests {
    sha256: "V\315\021<1\2330\034e\a\216\310\242o/\217g\252N\354\215i\023\311\360\n\326,5C\v\321"
  }
  repo_index {
    value: 8
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.3.1"
  }
  digests {
    sha256: "\000\304\276\001\a\374\271\\\346-_%^\016\a\261i\362[\374\332\202\000T\210\216&\250\233\360d\177"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.3.1"
  }
  digests {
    sha256: "@\317\214l\a\367Z\375\v!=iO\327\254G\214rj\224\346O\247\254\0303\301wR\363\346\364"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.3.1"
  }
  digests {
    sha256: "\306\020<\021fn\003\316\253\353dT\265\277\v\233Q\375\3116G\370M\320.\350i\251\300\206&\236"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.3.1"
  }
  digests {
    sha256: "\203\206\322\304E\321\224&\215J\362\326:\2731\260(\332_t/\303\214ce\274,@\253d\023\203"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.3.1"
  }
  digests {
    sha256: "\255\224C~<=\305\271\373J\\-\306J\251\022\251\021%Q\227B\354/\303\256\236\277\254\306#\201"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.3.1"
  }
  digests {
    sha256: "\304y\234\324\021\006\032eJ\331\307?\274=\032\225\224_\316@\361\273\316\257!\236\027\270\245o\241\301"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.3.1"
  }
  digests {
    sha256: "T@\016A\031`\332\224}\205V{\274\343\347R\372\336k\313}\a\\\022*N,\320Sy\334b"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.3.1"
  }
  digests {
    sha256: "\250\322\006\363\225\203\212\006\270+\a\211\347 \bh\332\273&\271\307 ~\230\314;1\226\004\034\034\235"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-iot-linkkit"
    version: "1.7.3.8"
  }
  digests {
    sha256: "\256@!Z\033K{\037\375\341\n\363\236\370!\aV\\Q\206\030(1y\264}r(\310Z<\017"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-iot-device-manager"
    version: "1.7.5.10"
  }
  digests {
    sha256: "*\b\t\337\252\246>\364\316Ev\362\201\267I\023w\236\001T7Bu\225\314\364Y\240\200\327\025\206"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-public-tmp"
    version: "2.0.5"
  }
  digests {
    sha256: "*\240|l\302\220Q\341\317~y\342p\334\347\2407\373\347\237\372\017C\262q\265\242C\302\276\2428"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.alibaba"
    artifactId: "fastjson"
    version: "1.2.40"
  }
  digests {
    sha256: "\\\031\26355\312\307\n\024-\035\345;~\353:\357k\273\245\315\227\357\223\247\207r\330\223\271~\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "api-client-biz"
    version: "1.0.0"
  }
  digests {
    sha256: "\345k\354\340\373Dy\230p\262w1\327\220\322\260\372t\223\227\317\260X\252\224\304\305\346\020\024\210\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-connectsdk"
    version: "1.0.6"
  }
  digests {
    sha256: "G\377w\206\357\032\377`\237Gk\363\202\033\3143\340\315\230\257\033t\2153nR\316\215\246vl\005"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-public-cmp"
    version: "1.9.3.5"
  }
  digests {
    sha256: "r\313c\0051~\377\207\367\036\370\022\200\344\310\301\344\246\032\352\250\313\204\021\n5\234\260\254\237&\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "tools"
    version: "1.3.5.1"
  }
  digests {
    sha256: "\333\270k\0232\247\3341\"\345D\200V,z\036\352\234\nT\313\345B\256\317\355\310\306\312i\006\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "iot-apiclient"
    version: "1.0.1"
  }
  digests {
    sha256: "\255\vU\213\321\271\343\367\210R\370\a\257\230\230\274#\246R2}\000\321fZ\000\227\324T\243\323\246"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "public-alcs-cmp"
    version: "1.6.0"
  }
  digests {
    sha256: "\370\253\'5\217f\250\325\365\217h\306\305\303\017\017\232/`\f\275/;\341\035W\2420\027\267@h"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "android_alcs_lpbs"
    version: "1.7.0"
  }
  digests {
    sha256: "\274S\272\361\\\254\031q\343\257M\227\033\260t%\216\025\235v=\036\252B\003\316\356\245\221L\004\006"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "coap-sdk"
    version: "1.0.2sp1"
  }
  digests {
    sha256: "\246P\305_\364\034\300\032\037\251\237\210\230\264\274.Gxd\302\250\340x\250\020\204\bN\366\354-\342"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-network-core"
    version: "1.0.1.1"
  }
  digests {
    sha256: "\b1\213\341`\206\303\267\267Ek\372w\305%{\230\313b|X\031S\305\312m\024(\016\fLW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "public-channel-gateway"
    version: "1.6.4"
  }
  digests {
    sha256: "^\006\277C\237\241\372\205\334\'\335g\350\250\270\205\274\371\005\0334\355\203\302i\035\367\244\326?\261\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "lp-public-channel-core"
    version: "0.7.7.5"
  }
  digests {
    sha256: "\227\v0\271.\227\252\374\317\365\035v\177\f*\302\b\0340DS\bH\222\246`\272$Wo\276\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "android_alink_id2"
    version: "1.1.3"
  }
  digests {
    sha256: "h\372\366\371\026\301\340\rr#\347\0243z&\226\016U\334\300\b\255\2505\377\000\"\312\216\205\212\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "opensource-paho"
    version: "1.2.0.6"
  }
  digests {
    sha256: ">\002I\321^\313GK\032\004\350\343\005\271\304\245\306\247\177V\034\250\022\235\226jnRXOm\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.11"
  }
  digests {
    sha256: "\345\231\3251\216\227\252H\364!6\242\222~m\372N\210\201\337\360\346\310\343\020\235\333\277\365\035{}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "iot-h2-stream"
    version: "1.1.6"
  }
  digests {
    sha256: "sR\263L\232&\330\353\306\017\237\354\363\264\274\303\b{\233p\366\2123\302\253\333\321\324m\217\025?"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.aliyun.alink.linksdk"
    artifactId: "iot-h2"
    version: "1.1.6"
  }
  digests {
    sha256: "\321\026<\275x(\333 \327\330p\335\346\314\316V\325\327#\333\3465\004B\334\276\214EU\203\320\377"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "io.netty"
    artifactId: "netty-all"
    version: "4.1.23.Final"
  }
  digests {
    sha256: "\260\234\226\334n\321\331\364\220\314\267\346%\227C\004\365\354\221\334\255\356\371\302\'\265\256>\rc\377\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "barcode-scanning"
    version: "17.3.0"
  }
  digests {
    sha256: "d\305\317H\273\224\266\305O,x\\f\006@\247\321g\257\366\255r\274,\222\a\006\334A\207\267\232"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-barcode-scanning"
    version: "18.3.1"
  }
  digests {
    sha256: "\365\200\226\271\210gAzi\215\265]\337\327\310\355\003\203\262\021\177\250\363i/\247\001\326\023/\024b"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "2.2.1"
  }
  digests {
    sha256: "\346/s\272\034\177]\025\365\330m\216\232)\214B\314\320d\216|\346\v\2361/.\315\022;^\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "2.3.3"
  }
  digests {
    sha256: "\262*\024\325`\245\220\334\207^\220$$\262\232\023\240-\037\257){|9\251\000yg\377\255\314\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "2.2.6"
  }
  digests {
    sha256: "\330:\322<\023D)i\021a\311$v\243\310=\3626\315\037\213\255\277\324\360\a#\264\354\315t\034"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "16.1.0"
  }
  digests {
    sha256: "\217\211\247B\230\273\314\302\305C8R\b\242\360\306\177`<\346~n\302\034\307r\351\216h\217|\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "17.1.0"
  }
  digests {
    sha256: "\202\225\307U\274\255\310z*\256\345\271\345h\362\177\3218\375E\377\021Y\250\356t|\364\303\243\215\205"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "16.1.0"
  }
  digests {
    sha256: "\202\351\032\245\355\030n\355\257\037\"\020\365\322A\272nDV\317\f2\224\235\\\333\341R\260\342\350\213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.0.0"
  }
  digests {
    sha256: "\2100\362\350\245\3444\313\305\344\275\030\0230s\266\350Q\212\214\350\354,\346\363\262s\311\035\226d\302"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "barcode-scanning-common"
    version: "17.0.0"
  }
  digests {
    sha256: "\315\016\236q\2704\a\215J\212\177\t\347-}|:\"\342X\351`-\247\254\206\024fw\004?\024"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "17.3.0"
  }
  digests {
    sha256: "\300\b\\\245\373\240\017\021R\234\n\203\261(Q\352lY\233\274\310\257\375\222~\221K)\247CE\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "18.11.0"
  }
  digests {
    sha256: "f@\212\b\333\262FV\207\377]\320\310\366A\vVT\234\343\354\364I\331\to7\022(!1\320"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-interfaces"
    version: "16.3.0"
  }
  digests {
    sha256: "\217\001\222\242m\001\267(\240\032\025\246 \330)\377\367\tX\"\202&\224C\212\223\022|\004\313\026\373"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "face-detection"
    version: "16.1.7"
  }
  digests {
    sha256: "=\205V\244\030\265GE\230\327\367G\370\3425\336\217]\242:\345)\343\260\242\330\324\333\350?m\355"
  }
  repo_index {
    value: 6
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-face-detection"
    version: "17.1.0"
  }
  digests {
    sha256: "\313\237\213Q\325\355\177(\373r`\025\220\371\002\032\\K\232\337y\233b\262\004_\343\206\215p}^"
  }
  repo_index {
    value: 1
  }
}
library {
  digests {
    sha256: "?\366\205\247M\222d\272\267\246y\177\252\234\002^4\200N\223\200]<\3371V\265%\331\266\332\024"
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 9
  library_dep_index: 6
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 3
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 44
  library_dep_index: 3
  library_dep_index: 13
}
library_dependencies {
  library_index: 15
  library_dep_index: 3
}
library_dependencies {
  library_index: 16
  library_dep_index: 1
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
  library_dep_index: 18
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 43
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 29
  library_dep_index: 38
}
library_dependencies {
  library_index: 21
  library_dep_index: 1
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 21
}
library_dependencies {
  library_index: 23
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 38
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 6
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 4
  library_dep_index: 27
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 27
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 28
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 38
}
library_dependencies {
  library_index: 29
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 38
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 30
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 38
}
library_dependencies {
  library_index: 31
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 3
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 23
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 29
  library_dep_index: 38
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 20
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 20
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 23
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 29
  library_dep_index: 38
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 29
  library_dep_index: 38
}
library_dependencies {
  library_index: 34
  library_dep_index: 1
  library_dep_index: 20
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 38
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 36
}
library_dependencies {
  library_index: 38
  library_dep_index: 20
  library_dep_index: 3
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 31
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 23
  library_dep_index: 34
  library_dep_index: 40
  library_dep_index: 29
  library_dep_index: 38
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 32
  library_dep_index: 38
  library_dep_index: 33
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 18
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 16
}
library_dependencies {
  library_index: 45
  library_dep_index: 46
  library_dep_index: 1
  library_dep_index: 48
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 13
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 55
  library_dep_index: 56
  library_dep_index: 20
  library_dep_index: 33
  library_dep_index: 61
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 48
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 33
  library_dep_index: 40
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 36
  library_dep_index: 3
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 46
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 3
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 45
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 16
}
library_dependencies {
  library_index: 50
  library_dep_index: 49
  library_dep_index: 19
  library_dep_index: 16
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 16
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 55
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 54
}
library_dependencies {
  library_index: 56
  library_dep_index: 46
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 20
  library_dep_index: 33
  library_dep_index: 40
  library_dep_index: 57
  library_dep_index: 43
  library_dep_index: 41
  library_dep_index: 58
  library_dep_index: 3
  library_dep_index: 59
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 33
}
library_dependencies {
  library_index: 58
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 53
}
library_dependencies {
  library_index: 59
  library_dep_index: 47
  library_dep_index: 60
  library_dep_index: 13
  library_dep_index: 56
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 3
  library_dep_index: 56
}
library_dependencies {
  library_index: 60
  library_dep_index: 3
  library_dep_index: 16
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 46
  library_dep_index: 1
  library_dep_index: 45
  library_dep_index: 65
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 14
  library_dep_index: 52
  library_dep_index: 69
  library_dep_index: 15
  library_dep_index: 56
  library_dep_index: 20
  library_dep_index: 74
  library_dep_index: 61
  library_dep_index: 76
  library_dep_index: 49
  library_dep_index: 77
}
library_dependencies {
  library_index: 63
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 53
  library_dep_index: 16
}
library_dependencies {
  library_index: 67
  library_dep_index: 45
  library_dep_index: 14
  library_dep_index: 68
}
library_dependencies {
  library_index: 69
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 71
  library_dep_index: 57
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 71
  library_dep_index: 1
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
}
library_dependencies {
  library_index: 73
  library_dep_index: 1
}
library_dependencies {
  library_index: 74
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 53
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 13
  library_dep_index: 3
}
library_dependencies {
  library_index: 76
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 16
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
  library_dep_index: 56
  library_dep_index: 74
  library_dep_index: 14
  library_dep_index: 16
}
library_dependencies {
  library_index: 78
  library_dep_index: 79
  library_dep_index: 13
  library_dep_index: 45
  library_dep_index: 62
  library_dep_index: 39
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 59
  library_dep_index: 47
  library_dep_index: 24
  library_dep_index: 81
  library_dep_index: 1
  library_dep_index: 82
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 93
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 103
  library_dep_index: 94
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 106
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 3
}
library_dependencies {
  library_index: 79
  library_dep_index: 3
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 3
}
library_dependencies {
  library_index: 82
  library_dep_index: 83
  library_dep_index: 18
  library_dep_index: 84
  library_dep_index: 85
  library_dep_index: 64
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
}
library_dependencies {
  library_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 56
  library_dep_index: 50
  library_dep_index: 92
  library_dep_index: 36
}
library_dependencies {
  library_index: 89
  library_dep_index: 1
}
library_dependencies {
  library_index: 92
  library_dep_index: 1
}
library_dependencies {
  library_index: 93
  library_dep_index: 88
  library_dep_index: 94
  library_dep_index: 1
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 6
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 97
  library_dep_index: 88
}
library_dependencies {
  library_index: 98
  library_dep_index: 94
}
library_dependencies {
  library_index: 99
  library_dep_index: 94
}
library_dependencies {
  library_index: 100
  library_dep_index: 45
  library_dep_index: 95
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
  library_dep_index: 28
}
library_dependencies {
  library_index: 102
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 56
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 33
}
library_dependencies {
  library_index: 103
  library_dep_index: 86
}
library_dependencies {
  library_index: 104
  library_dep_index: 94
  library_dep_index: 6
}
library_dependencies {
  library_index: 105
  library_dep_index: 94
}
library_dependencies {
  library_index: 106
  library_dep_index: 105
  library_dep_index: 86
}
library_dependencies {
  library_index: 107
  library_dep_index: 94
  library_dep_index: 6
}
library_dependencies {
  library_index: 108
  library_dep_index: 1
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
}
library_dependencies {
  library_index: 112
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 113
  library_dep_index: 14
  library_dep_index: 92
  library_dep_index: 23
  library_dep_index: 28
  library_dep_index: 36
  library_dep_index: 114
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 115
  library_dep_index: 116
  library_dep_index: 118
  library_dep_index: 117
}
library_dependencies {
  library_index: 113
  library_dep_index: 17
  library_dep_index: 3
  library_dep_index: 25
}
library_dependencies {
  library_index: 115
  library_dep_index: 1
  library_dep_index: 112
  library_dep_index: 17
  library_dep_index: 14
  library_dep_index: 36
  library_dep_index: 114
  library_dep_index: 18
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 118
  library_dep_index: 117
}
library_dependencies {
  library_index: 116
  library_dep_index: 112
  library_dep_index: 17
  library_dep_index: 113
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 37
  library_dep_index: 114
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 117
  library_dep_index: 118
}
library_dependencies {
  library_index: 117
  library_dep_index: 1
  library_dep_index: 112
  library_dep_index: 17
  library_dep_index: 14
  library_dep_index: 114
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 118
}
library_dependencies {
  library_index: 118
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 45
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 117
  library_dep_index: 17
  library_dep_index: 14
  library_dep_index: 23
  library_dep_index: 114
  library_dep_index: 18
  library_dep_index: 115
  library_dep_index: 112
  library_dep_index: 116
  library_dep_index: 117
}
library_dependencies {
  library_index: 119
  library_dep_index: 120
  library_dep_index: 120
}
library_dependencies {
  library_index: 120
  library_dep_index: 15
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 38
  library_dep_index: 121
  library_dep_index: 125
  library_dep_index: 35
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 24
  library_dep_index: 119
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
  library_dep_index: 123
  library_dep_index: 3
  library_dep_index: 24
}
library_dependencies {
  library_index: 122
  library_dep_index: 1
  library_dep_index: 6
}
library_dependencies {
  library_index: 123
  library_dep_index: 15
  library_dep_index: 22
  library_dep_index: 122
  library_dep_index: 124
  library_dep_index: 125
}
library_dependencies {
  library_index: 124
  library_dep_index: 1
  library_dep_index: 3
}
library_dependencies {
  library_index: 125
  library_dep_index: 1
  library_dep_index: 124
  library_dep_index: 3
}
library_dependencies {
  library_index: 126
  library_dep_index: 1
}
library_dependencies {
  library_index: 127
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 92
  library_dep_index: 128
  library_dep_index: 129
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 133
  library_dep_index: 130
}
library_dependencies {
  library_index: 128
  library_dep_index: 1
  library_dep_index: 82
  library_dep_index: 15
  library_dep_index: 129
  library_dep_index: 130
  library_dep_index: 131
  library_dep_index: 132
  library_dep_index: 127
  library_dep_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 129
  library_dep_index: 128
  library_dep_index: 1
}
library_dependencies {
  library_index: 130
  library_dep_index: 128
  library_dep_index: 1
}
library_dependencies {
  library_index: 131
  library_dep_index: 128
  library_dep_index: 130
  library_dep_index: 1
  library_dep_index: 92
}
library_dependencies {
  library_index: 132
  library_dep_index: 128
  library_dep_index: 1
}
library_dependencies {
  library_index: 133
  library_dep_index: 1
  library_dep_index: 128
  library_dep_index: 129
  library_dep_index: 132
}
library_dependencies {
  library_index: 134
  library_dep_index: 128
  library_dep_index: 135
  library_dep_index: 1
  library_dep_index: 74
}
library_dependencies {
  library_index: 135
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 14
}
library_dependencies {
  library_index: 136
  library_dep_index: 137
  library_dep_index: 154
}
library_dependencies {
  library_index: 137
  library_dep_index: 138
  library_dep_index: 147
  library_dep_index: 142
  library_dep_index: 144
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 143
  library_dep_index: 150
  library_dep_index: 153
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
  library_dep_index: 86
  library_dep_index: 140
  library_dep_index: 141
  library_dep_index: 142
  library_dep_index: 146
}
library_dependencies {
  library_index: 141
  library_dep_index: 139
  library_dep_index: 86
}
library_dependencies {
  library_index: 142
  library_dep_index: 139
  library_dep_index: 143
  library_dep_index: 144
  library_dep_index: 145
}
library_dependencies {
  library_index: 143
  library_dep_index: 139
}
library_dependencies {
  library_index: 144
  library_dep_index: 86
}
library_dependencies {
  library_index: 148
  library_dep_index: 94
}
library_dependencies {
  library_index: 150
  library_dep_index: 139
  library_dep_index: 143
  library_dep_index: 151
  library_dep_index: 152
}
library_dependencies {
  library_index: 151
  library_dep_index: 143
}
library_dependencies {
  library_index: 154
  library_dep_index: 155
}
library_dependencies {
  library_index: 155
  library_dep_index: 156
  library_dep_index: 153
}
library_dependencies {
  library_index: 157
  library_dep_index: 158
  library_dep_index: 159
  library_dep_index: 171
  library_dep_index: 173
  library_dep_index: 172
}
library_dependencies {
  library_index: 158
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 56
}
library_dependencies {
  library_index: 159
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 158
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 171
  library_dep_index: 173
  library_dep_index: 172
  library_dep_index: 174
}
library_dependencies {
  library_index: 160
  library_dep_index: 1
}
library_dependencies {
  library_index: 161
  library_dep_index: 1
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 164
  library_dep_index: 165
}
library_dependencies {
  library_index: 162
  library_dep_index: 1
  library_dep_index: 160
  library_dep_index: 163
}
library_dependencies {
  library_index: 164
  library_dep_index: 1
}
library_dependencies {
  library_index: 165
  library_dep_index: 1
  library_dep_index: 164
}
library_dependencies {
  library_index: 166
  library_dep_index: 16
  library_dep_index: 14
  library_dep_index: 56
  library_dep_index: 158
  library_dep_index: 167
}
library_dependencies {
  library_index: 167
  library_dep_index: 158
}
library_dependencies {
  library_index: 169
  library_dep_index: 1
  library_dep_index: 170
}
library_dependencies {
  library_index: 171
  library_dep_index: 158
  library_dep_index: 172
}
library_dependencies {
  library_index: 172
  library_dep_index: 92
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 158
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 173
}
library_dependencies {
  library_index: 173
  library_dep_index: 45
  library_dep_index: 14
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 158
  library_dep_index: 167
  library_dep_index: 169
  library_dep_index: 164
  library_dep_index: 165
}
library_dependencies {
  library_index: 174
  library_dep_index: 158
  library_dep_index: 167
}
library_dependencies {
  library_index: 175
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 158
  library_dep_index: 176
  library_dep_index: 167
  library_dep_index: 169
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 173
  library_dep_index: 5
}
library_dependencies {
  library_index: 176
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 166
  library_dep_index: 158
  library_dep_index: 167
  library_dep_index: 168
  library_dep_index: 169
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 173
  library_dep_index: 172
  library_dep_index: 174
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 6
  dependency_index: 8
  dependency_index: 9
  dependency_index: 11
  dependency_index: 13
  dependency_index: 45
  dependency_index: 62
  dependency_index: 67
  dependency_index: 39
  dependency_index: 31
  dependency_index: 32
  dependency_index: 59
  dependency_index: 47
  dependency_index: 24
  dependency_index: 1
  dependency_index: 78
  dependency_index: 109
  dependency_index: 111
  dependency_index: 81
  dependency_index: 86
  dependency_index: 87
  dependency_index: 88
  dependency_index: 93
  dependency_index: 97
  dependency_index: 98
  dependency_index: 99
  dependency_index: 100
  dependency_index: 101
  dependency_index: 103
  dependency_index: 94
  dependency_index: 105
  dependency_index: 106
  dependency_index: 107
  dependency_index: 112
  dependency_index: 115
  dependency_index: 118
  dependency_index: 116
  dependency_index: 82
  dependency_index: 119
  dependency_index: 126
  dependency_index: 127
  dependency_index: 134
  dependency_index: 128
  dependency_index: 136
  dependency_index: 157
  dependency_index: 175
  dependency_index: 79
  dependency_index: 177
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/public"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/google"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/repository/gradle-plugin"
  }
}
repositories {
  maven_repo {
    url: "http://10.1.3.144:8081/repository/airdoc-snapshot/"
  }
}
repositories {
  maven_repo {
    url: "http://10.1.3.144:8081/repository/airdoc-release/"
  }
}
repositories {
  maven_repo {
    url: "https://maven.aliyun.com/nexus/content/repositories/releases/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
