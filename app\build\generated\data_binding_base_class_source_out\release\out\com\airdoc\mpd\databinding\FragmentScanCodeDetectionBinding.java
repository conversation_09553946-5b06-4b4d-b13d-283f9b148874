// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import com.google.android.material.imageview.ShapeableImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentScanCodeDetectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clScanCode;

  @NonNull
  public final ShapeableImageView ivCode;

  @NonNull
  public final ImageView ivCodeLoading;

  @NonNull
  public final TextView tvCodeLoading;

  @NonNull
  public final TextView tvScanMethod;

  @NonNull
  public final View viewCode;

  private FragmentScanCodeDetectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clScanCode, @NonNull ShapeableImageView ivCode,
      @NonNull ImageView ivCodeLoading, @NonNull TextView tvCodeLoading,
      @NonNull TextView tvScanMethod, @NonNull View viewCode) {
    this.rootView = rootView;
    this.clScanCode = clScanCode;
    this.ivCode = ivCode;
    this.ivCodeLoading = ivCodeLoading;
    this.tvCodeLoading = tvCodeLoading;
    this.tvScanMethod = tvScanMethod;
    this.viewCode = viewCode;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentScanCodeDetectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentScanCodeDetectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_scan_code_detection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentScanCodeDetectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout clScanCode = (ConstraintLayout) rootView;

      id = R.id.iv_code;
      ShapeableImageView ivCode = ViewBindings.findChildViewById(rootView, id);
      if (ivCode == null) {
        break missingId;
      }

      id = R.id.iv_code_loading;
      ImageView ivCodeLoading = ViewBindings.findChildViewById(rootView, id);
      if (ivCodeLoading == null) {
        break missingId;
      }

      id = R.id.tv_code_loading;
      TextView tvCodeLoading = ViewBindings.findChildViewById(rootView, id);
      if (tvCodeLoading == null) {
        break missingId;
      }

      id = R.id.tv_scan_method;
      TextView tvScanMethod = ViewBindings.findChildViewById(rootView, id);
      if (tvScanMethod == null) {
        break missingId;
      }

      id = R.id.view_code;
      View viewCode = ViewBindings.findChildViewById(rootView, id);
      if (viewCode == null) {
        break missingId;
      }

      return new FragmentScanCodeDetectionBinding((ConstraintLayout) rootView, clScanCode, ivCode,
          ivCodeLoading, tvCodeLoading, tvScanMethod, viewCode);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
