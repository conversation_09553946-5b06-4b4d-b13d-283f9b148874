# 使用java中的关键字作字典：避免混淆后与其他包重名，而且混淆之后的代码更加不利于阅读
#
# This obfuscation dictionary contains reserved Java keywords. They can't
# be used in Java source files, but they can be used in compiled class files.
# Note that this hardly improves the obfuscation. Decent decompilers can
# automatically replace reserved keywords, and the effect can fairly simply be
# undone by obfuscating again with simpler names.
# Usage:
#     java -jar proguard.jar ..... -obfuscationdictionary class_dictionary.txt
#

do
doa
doab
doac
doad
doae
doaf
doag
doah
doai
doaj
doak
doal
doam
doan
dob
doba
dobc
dobd
dobe
dobf
dobg
dobh
dobi
dobj
dobk
dobl
dobm
dobn
doc
doca
docb
docd
doce
docf
docg
doch
doci
docj
dock
docl
docm
docn
doe
doea
doeb
doec
doed
doef
doeg
doeh
doei
doej
doek
doel
doem
doen
dof
dofa
dofb
dofc
dofd
dofe
dofg
dofh
dofi
dofj
dofk
dofl
dofm
dofn
dog
doga
dogb
dogc
dogd
doge
dogf
dogh
dogi
dogj
dogk
dogl
dogm
dogn
if
ifa
ifab
ifac
ifad
ifae
ifaf
ifag
ifah
ifai
ifaj
ifak
ifal
ifam
ifan
ifb
ifba
ifbc
ifbd
ifbe
ifbf
ifbg
ifbh
ifbi
ifbj
ifbk
ifbl
ifbm
ifbn
ifc
ifca
ifcb
ifcd
ifce
ifcf
ifch
ifci
ifcj
ifck
ifcl
ifcm
ifcn
ifd
ifda
ifdb
ifdc
ifde
ifdf
ifdg
ifdh
ifdi
ifdj
ifdk
ifdl
ifdm
ifdn
iff
iffa
iffb
iffc
iffd
iffe
iffg
iffh
iffi
iffj
iffk
iffl
iffm
iffn
ifg
ifga
ifgb
ifgc
ifgd
ifge
ifgf
ifgh
ifgi
ifgj
ifgk
ifgl
ifgm
ifgn
for
int
new
try
byte
case
char
else
goto
long
this
void
break
catch
class
const
final
float
short
super
throw
while
double
import
native
public
return
static
switch
throws
boolean
default
extends
finally
package
private
abstract
continue
strictfp
volatile
interface
protected
transient
implements
instanceof
synchronized