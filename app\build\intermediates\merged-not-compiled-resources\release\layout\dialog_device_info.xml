<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="380dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_device_info_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_device_info"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_left"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintRight_toLeftOf="@+id/tv_title"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_right"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="357dp"
        android:layout_height="211dp"
        android:background="@drawable/ic_device_info_dialog_content_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <TextView
            android:id="@+id/tv_open_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="开通日期：2025.01.01"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:includeFontPadding="false"
            android:layout_marginTop="35dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_valid_until"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="有效期至：2025.01.01"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:includeFontPadding="false"
            android:layout_marginTop="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_open_date" />

        <TextView
            android:id="@+id/tv_residual_degree"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="剩余次数：30次"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:includeFontPadding="false"
            android:layout_marginTop="8dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_valid_until" />

        <TextView
            android:id="@+id/tv_exception"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/str_number_available_times_0"
            android:textSize="18sp"
            android:textColor="@color/color_333333"
            android:gravity="center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="60dp"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_customer_service_hotline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/str_customer_service_hotline"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:includeFontPadding="false"
            android:layout_marginBottom="35dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tv_i_know"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_i_know"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>