// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutMenuPopupWindowBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ConstraintLayout clVersion;

  @NonNull
  public final ImageView ivDeviceInfo;

  @NonNull
  public final ImageView ivLanguage;

  @NonNull
  public final ImageView ivMoreSettings;

  @NonNull
  public final ImageView ivScannerSettings;

  @NonNull
  public final ImageView ivVersion;

  @NonNull
  public final ImageView ivViewReport;

  @NonNull
  public final LinearLayout llDeviceInfo;

  @NonNull
  public final LinearLayout llLanguage;

  @NonNull
  public final LinearLayout llMoreSettings;

  @NonNull
  public final LinearLayout llScannerSettings;

  @NonNull
  public final LinearLayout llViewReport;

  @NonNull
  public final TextView tvDeviceInfo;

  @NonNull
  public final TextView tvLanguage;

  @NonNull
  public final TextView tvMoreSettings;

  @NonNull
  public final TextView tvScannerSettings;

  @NonNull
  public final TextView tvVersion;

  @NonNull
  public final TextView tvViewReport;

  @NonNull
  public final View versionRedDot;

  private LayoutMenuPopupWindowBinding(@NonNull LinearLayout rootView,
      @NonNull ConstraintLayout clVersion, @NonNull ImageView ivDeviceInfo,
      @NonNull ImageView ivLanguage, @NonNull ImageView ivMoreSettings,
      @NonNull ImageView ivScannerSettings, @NonNull ImageView ivVersion,
      @NonNull ImageView ivViewReport, @NonNull LinearLayout llDeviceInfo,
      @NonNull LinearLayout llLanguage, @NonNull LinearLayout llMoreSettings,
      @NonNull LinearLayout llScannerSettings, @NonNull LinearLayout llViewReport,
      @NonNull TextView tvDeviceInfo, @NonNull TextView tvLanguage,
      @NonNull TextView tvMoreSettings, @NonNull TextView tvScannerSettings,
      @NonNull TextView tvVersion, @NonNull TextView tvViewReport, @NonNull View versionRedDot) {
    this.rootView = rootView;
    this.clVersion = clVersion;
    this.ivDeviceInfo = ivDeviceInfo;
    this.ivLanguage = ivLanguage;
    this.ivMoreSettings = ivMoreSettings;
    this.ivScannerSettings = ivScannerSettings;
    this.ivVersion = ivVersion;
    this.ivViewReport = ivViewReport;
    this.llDeviceInfo = llDeviceInfo;
    this.llLanguage = llLanguage;
    this.llMoreSettings = llMoreSettings;
    this.llScannerSettings = llScannerSettings;
    this.llViewReport = llViewReport;
    this.tvDeviceInfo = tvDeviceInfo;
    this.tvLanguage = tvLanguage;
    this.tvMoreSettings = tvMoreSettings;
    this.tvScannerSettings = tvScannerSettings;
    this.tvVersion = tvVersion;
    this.tvViewReport = tvViewReport;
    this.versionRedDot = versionRedDot;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutMenuPopupWindowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutMenuPopupWindowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_menu_popup_window, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutMenuPopupWindowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cl_version;
      ConstraintLayout clVersion = ViewBindings.findChildViewById(rootView, id);
      if (clVersion == null) {
        break missingId;
      }

      id = R.id.iv_device_info;
      ImageView ivDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (ivDeviceInfo == null) {
        break missingId;
      }

      id = R.id.iv_language;
      ImageView ivLanguage = ViewBindings.findChildViewById(rootView, id);
      if (ivLanguage == null) {
        break missingId;
      }

      id = R.id.iv_more_settings;
      ImageView ivMoreSettings = ViewBindings.findChildViewById(rootView, id);
      if (ivMoreSettings == null) {
        break missingId;
      }

      id = R.id.iv_scanner_settings;
      ImageView ivScannerSettings = ViewBindings.findChildViewById(rootView, id);
      if (ivScannerSettings == null) {
        break missingId;
      }

      id = R.id.iv_version;
      ImageView ivVersion = ViewBindings.findChildViewById(rootView, id);
      if (ivVersion == null) {
        break missingId;
      }

      id = R.id.iv_view_report;
      ImageView ivViewReport = ViewBindings.findChildViewById(rootView, id);
      if (ivViewReport == null) {
        break missingId;
      }

      id = R.id.ll_device_info;
      LinearLayout llDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (llDeviceInfo == null) {
        break missingId;
      }

      id = R.id.ll_language;
      LinearLayout llLanguage = ViewBindings.findChildViewById(rootView, id);
      if (llLanguage == null) {
        break missingId;
      }

      id = R.id.ll_more_settings;
      LinearLayout llMoreSettings = ViewBindings.findChildViewById(rootView, id);
      if (llMoreSettings == null) {
        break missingId;
      }

      id = R.id.ll_scanner_settings;
      LinearLayout llScannerSettings = ViewBindings.findChildViewById(rootView, id);
      if (llScannerSettings == null) {
        break missingId;
      }

      id = R.id.ll_view_report;
      LinearLayout llViewReport = ViewBindings.findChildViewById(rootView, id);
      if (llViewReport == null) {
        break missingId;
      }

      id = R.id.tv_device_info;
      TextView tvDeviceInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvDeviceInfo == null) {
        break missingId;
      }

      id = R.id.tv_language;
      TextView tvLanguage = ViewBindings.findChildViewById(rootView, id);
      if (tvLanguage == null) {
        break missingId;
      }

      id = R.id.tv_more_settings;
      TextView tvMoreSettings = ViewBindings.findChildViewById(rootView, id);
      if (tvMoreSettings == null) {
        break missingId;
      }

      id = R.id.tv_scanner_settings;
      TextView tvScannerSettings = ViewBindings.findChildViewById(rootView, id);
      if (tvScannerSettings == null) {
        break missingId;
      }

      id = R.id.tv_version;
      TextView tvVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvVersion == null) {
        break missingId;
      }

      id = R.id.tv_view_report;
      TextView tvViewReport = ViewBindings.findChildViewById(rootView, id);
      if (tvViewReport == null) {
        break missingId;
      }

      id = R.id.version_red_dot;
      View versionRedDot = ViewBindings.findChildViewById(rootView, id);
      if (versionRedDot == null) {
        break missingId;
      }

      return new LayoutMenuPopupWindowBinding((LinearLayout) rootView, clVersion, ivDeviceInfo,
          ivLanguage, ivMoreSettings, ivScannerSettings, ivVersion, ivViewReport, llDeviceInfo,
          llLanguage, llMoreSettings, llScannerSettings, llViewReport, tvDeviceInfo, tvLanguage,
          tvMoreSettings, tvScannerSettings, tvVersion, tvViewReport, versionRedDot);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
