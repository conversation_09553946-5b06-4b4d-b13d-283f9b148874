1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.airdoc.mpd"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="105"
6    android:versionName="1.0.6" >
7
8    <uses-sdk
9        android:minSdkVersion="29"
10        android:targetSdkVersion="34" />
11
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- 基本蓝牙权限 -->
15    <uses-permission android:name="android.permission.BLUETOOTH" />
15-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:9:5-68
15-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:9:22-65
16    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
16-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:10:5-74
16-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:10:22-71
17
18    <!-- 位置权限（BLE扫描需要） -->
19    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
19-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:13:5-79
19-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:13:22-76
20    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
20-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:14:5-81
20-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:14:22-78
21
22    <!-- Android 12+ 蓝牙权限 -->
23    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
23-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:17:5-73
23-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:17:22-70
24    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
24-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:18:5-76
24-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:18:22-73
25    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
25-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:19:5-78
25-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:19:22-75
26
27    <!-- 相机权限 -->
28    <uses-permission android:name="android.permission.CAMERA" />
28-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:22:5-65
28-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:22:22-62
29
30    <uses-feature android:name="android.hardware.camera" />
30-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:23:5-60
30-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:23:19-57
31    <uses-feature android:name="android.hardware.camera.autofocus" />
31-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:24:5-70
31-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:24:19-67
32
33    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:5-80
33-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:22-78
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
34-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:5-84
34-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:22-81
35    <uses-permission android:name="android.permission.SYSTEM_CAMERA" />
35-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:30:5-31:47
35-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:30:22-69
36
37    <!-- 允许应用修改系统设置（如屏幕亮度、Wi-Fi 状态、默认应用等）。 -->
38    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
38-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:34:5-73
38-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:34:22-70
39    <!-- 文件读写权限 -->
40    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
40-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:5-80
40-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:26:22-78
41    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
41-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:37:5-80
41-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:37:22-77
42    <uses-permission android:name="android.permission.RECORD_AUDIO" />
42-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:39:5-71
42-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:39:22-68
43    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
43-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:41:5-80
43-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:41:22-77
44    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
44-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:43:5-82
44-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:43:22-80
45
46    <!-- 悬浮窗权限 -->
47    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
47-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:46:5-78
47-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:46:22-75
48
49    <!-- 前台服务权限 -->
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:49:5-77
50-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:49:22-74
51    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
51-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:5-84
51-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:29:22-81
52    <uses-permission android:name="android.permission.READ_LOGS" />
52-->[lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:17:5-19:47
52-->[lepu-blepro-1.0.8.aar] C:\Users\<USER>\.gradle\caches\transforms-3\87973bcf1f14d2a3440fdeec694d1caa\transformed\jetified-lepu-blepro-1.0.8\AndroidManifest.xml:18:9-52
53    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
53-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:10:5-79
53-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:10:22-76
54    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
54-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:12:5-76
54-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:12:22-73
55    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
55-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:13:5-75
55-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:13:22-72
56    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
56-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:15:5-17:47
56-->[com.airdoc.component:common:0.2.11-SNAPSHOT] C:\Users\<USER>\.gradle\caches\transforms-3\a35da5682bc621e8ae7aed90ff451a9b\transformed\jetified-common-0.2.11-SNAPSHOT\AndroidManifest.xml:16:9-70
57    <uses-permission android:name="android.permission.WAKE_LOCK" />
57-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
57-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
58    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
58-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
58-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
59
60    <permission
60-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
61        android:name="com.airdoc.mpd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
61-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
62        android:protectionLevel="signature" />
62-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
63
64    <uses-permission android:name="com.airdoc.mpd.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
64-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
64-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
65    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
65-->[com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:5-84
65-->[com.lzy.net:okgo:3.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\db3d65654abbba0b08636ad72f2f12e9\transformed\jetified-okgo-3.0.4\AndroidManifest.xml:29:22-81
66    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
66-->[com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:15:5-86
66-->[com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:15:22-83
67    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
67-->[com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:16:5-76
67-->[com.aliyun.alink.linksdk:lp-public-tmp:2.0.5] C:\Users\<USER>\.gradle\caches\transforms-3\daa41511d26ea3e7d134b8137642f6cc\transformed\jetified-lp-public-tmp-2.0.5\AndroidManifest.xml:16:22-73
68    <uses-permission android:name="android.permission.READ_SETTINGS" />
68-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:14:5-72
68-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:14:22-69
69    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
69-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:20:5-79
69-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:20:22-76
70    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" />
70-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:21:5-79
70-->[com.aliyun.alink.linksdk:android_alink_id2:1.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f5ce7829abfa730cda5c548bd565c7ce\transformed\jetified-android_alink_id2-1.1.3\AndroidManifest.xml:21:22-76
71
72    <application
72-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:52:5-134:19
73        android:name="com.airdoc.mpd.MpdApplication"
73-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:53:9-39
74        android:allowBackup="true"
74-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:54:9-35
75        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
75-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c63b318755d78145d01b8b87b88f3c2\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
76        android:dataExtractionRules="@xml/data_extraction_rules"
76-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:55:9-65
77        android:extractNativeLibs="true"
77-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:65:9-41
78        android:fullBackupContent="@xml/backup_rules"
78-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:56:9-54
79        android:icon="@mipmap/ic_launcher"
79-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:57:9-43
80        android:label="@string/app_name"
80-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:59:9-41
81        android:networkSecurityConfig="@xml/network_security_config"
81-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:63:9-69
82        android:requestLegacyExternalStorage="true"
82-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:66:9-52
83        android:roundIcon="@mipmap/ic_launcher"
83-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:60:9-48
84        android:supportsRtl="true"
84-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:61:9-35
85        android:theme="@style/Theme.AppStartLoad"
85-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:62:9-50
86        android:usesCleartextTraffic="true" >
86-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:64:9-44
87        <meta-data
87-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:68:9-70:34
88            android:name="design_width_in_dp"
88-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:69:13-46
89            android:value="960" />
89-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:70:13-32
90        <meta-data
90-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:71:9-73:34
91            android:name="design_height_in_dp"
91-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:72:13-47
92            android:value="540" />
92-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:73:13-32
93
94        <activity
94-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:75:9-85:20
95            android:name="com.airdoc.mpd.MainActivity"
95-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:76:13-41
96            android:exported="true"
96-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:77:13-36
97            android:launchMode="singleTop" >
97-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:78:13-43
98            <intent-filter>
98-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:80:13-84:29
99                <action android:name="android.intent.action.MAIN" />
99-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:81:17-69
99-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:81:25-66
100
101                <category android:name="android.intent.category.LAUNCHER" />
101-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:83:17-77
101-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:83:27-74
102            </intent-filter>
103        </activity>
104        <activity
104-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:87:9-90:20
105            android:name="com.airdoc.mpd.detection.DetectionActivity"
105-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:88:13-56
106            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
106-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:89:13-94
107        </activity>
108        <activity
108-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:92:9-95:20
109            android:name="com.airdoc.mpd.detection.DetectionActivity1"
109-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:93:13-57
110            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
110-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:94:13-94
111        </activity>
112        <activity
112-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:97:9-100:20
113            android:name="com.airdoc.mpd.update.UpdateActivity"
113-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:98:13-50
114            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
114-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:99:13-94
115        </activity>
116        <activity
116-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:102:9-105:20
117            android:name="com.airdoc.mpd.detection.hrv.HrvActivity"
117-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:103:13-54
118            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
118-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:104:13-94
119        </activity>
120        <activity
120-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:107:9-110:20
121            android:name="com.airdoc.mpd.detection.DetectionWebActivity"
121-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:108:13-59
122            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
122-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:109:13-94
123        </activity>
124        <activity
124-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:112:9-115:20
125            android:name="com.airdoc.mpd.config.ConfigActivity"
125-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:113:13-50
126            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
126-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:114:13-94
127        </activity>
128        <activity
128-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:117:9-120:20
129            android:name="com.airdoc.mpd.scan.ScanActivity"
129-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:118:13-46
130            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
130-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:119:13-94
131        </activity>
132        <activity
132-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:122:9-125:20
133            android:name="com.airdoc.mpd.MoreSettingsActivity"
133-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:123:13-49
134            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|navigation" >
134-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:124:13-94
135        </activity>
136
137        <service
137-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:127:9-131:38
138            android:name="com.airdoc.mpd.gaze.track.GazeTrackService"
138-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:127:18-61
139            android:enabled="true"
139-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:129:13-35
140            android:exported="true"
140-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:128:13-36
141            android:foregroundServiceType="camera"
141-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:130:13-51
142            android:process=":gaze" />
142-->D:\mpd_app_dev\app\src\main\AndroidManifest.xml:131:13-36
143        <service
143-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:25:9-35:19
144            android:name="io.getstream.log.android.file.StreamLogFileService"
144-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:26:13-78
145            android:enabled="true"
145-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:27:13-35
146            android:exported="true" >
146-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:28:13-36
147            <intent-filter>
147-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:29:13-31:29
148                <action android:name="io.getstream.log.android.SHARE" />
148-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:30:17-73
148-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:30:25-70
149            </intent-filter>
150            <intent-filter>
150-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:32:13-34:29
151                <action android:name="io.getstream.log.android.CLEAR" />
151-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:33:17-73
151-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:33:25-70
152            </intent-filter>
153        </service>
154
155        <provider
155-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:37:9-45:20
156            android:name="io.getstream.log.android.file.StreamLogFileProvider"
156-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:38:13-79
157            android:authorities="com.airdoc.mpd.streamlogfileprovider"
157-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:39:13-73
158            android:exported="false"
158-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:40:13-37
159            android:grantUriPermissions="true" >
159-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:41:13-47
160            <meta-data
160-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:42:13-44:58
161                android:name="android.support.FILE_PROVIDER_PATHS"
161-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:43:17-67
162                android:resource="@xml/log_file_paths" />
162-->[io.getstream:stream-log-android-file:1.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d98dc4e5d1fdb676f6a6b730a5db21\transformed\jetified-stream-log-android-file-1.1.4\AndroidManifest.xml:44:17-55
163        </provider>
164
165        <service
165-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:24:9-33:19
166            android:name="androidx.camera.core.impl.MetadataHolderService"
166-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:25:13-75
167            android:enabled="false"
167-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:26:13-36
168            android:exported="false" >
168-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:27:13-37
169            <meta-data
169-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:30:13-32:89
170                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
170-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:31:17-103
171                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
171-->[androidx.camera:camera-camera2:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\31e954d85fa906b14e495f549a2fa4fe\transformed\jetified-camera-camera2-1.4.2\AndroidManifest.xml:32:17-86
172        </service>
173        <service
173-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:9:9-15:19
174            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
174-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:10:13-91
175            android:directBootAware="true"
175-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:17:13-43
176            android:exported="false" >
176-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:11:13-37
177            <meta-data
177-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:12:13-14:85
178                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
178-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:13:17-120
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\4d5f74c9be55b6da88466e1ef6811625\transformed\jetified-play-services-mlkit-barcode-scanning-18.3.1\AndroidManifest.xml:14:17-82
180            <meta-data
180-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:12:13-14:85
181                android:name="com.google.firebase.components:com.google.mlkit.vision.face.internal.FaceRegistrar"
181-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:13:17-114
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.android.gms:play-services-mlkit-face-detection:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c8f59c3f9427dfb0ad9d4e567aee0f0\transformed\jetified-play-services-mlkit-face-detection-17.1.0\AndroidManifest.xml:14:17-82
183            <meta-data
183-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
184                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
184-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:13:17-124
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90bcbd9d4fcdf8ed1e5e86fb39442d54\transformed\jetified-vision-common-17.3.0\AndroidManifest.xml:14:17-82
186            <meta-data
186-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:20:13-22:85
187                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
187-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:21:17-120
188                android:value="com.google.firebase.components.ComponentRegistrar" />
188-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:22:17-82
189        </service>
190
191        <provider
191-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:9:9-13:38
192            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
192-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:10:13-78
193            android:authorities="com.airdoc.mpd.mlkitinitprovider"
193-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:11:13-69
194            android:exported="false"
194-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:12:13-37
195            android:initOrder="99" />
195-->[com.google.mlkit:common:18.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbd1b81befa30ad1afc448c4dc894f7b\transformed\jetified-common-18.11.0\AndroidManifest.xml:13:13-35
196
197        <meta-data
197-->[com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:10:9-12:43
198            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
198-->[com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:11:13-84
199            android:value="GlideModule" />
199-->[com.github.bumptech.glide:okhttp3-integration:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-3\a1b0d3b0b374e7f815d6e1cf407b2654\transformed\jetified-okhttp3-integration-4.15.1\AndroidManifest.xml:12:13-40
200
201        <activity
201-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
202            android:name="com.google.android.gms.common.api.GoogleApiActivity"
202-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
203            android:exported="false"
203-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
204            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
204-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\bf04cdc715fc93d5a24d642c24f51c2b\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
205
206        <meta-data
206-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
207            android:name="com.google.android.gms.version"
207-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
208            android:value="@integer/google_play_services_version" />
208-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f784686b41df3e3e9ff94a38ce261387\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
209
210        <provider
210-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
211            android:name="androidx.startup.InitializationProvider"
211-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
212            android:authorities="com.airdoc.mpd.androidx-startup"
212-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
213            android:exported="false" >
213-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
214            <meta-data
214-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
215                android:name="androidx.emoji2.text.EmojiCompatInitializer"
215-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
216                android:value="androidx.startup" />
216-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c551971f99cdcb0198faf52960f1e752\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
217            <meta-data
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
218                android:name="androidx.work.WorkManagerInitializer"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
219                android:value="androidx.startup" />
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
220            <meta-data
220-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
221                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
221-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
222                android:value="androidx.startup" />
222-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\021978f4f15d0633db9b12aea23c09ce\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
223            <meta-data
223-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
224                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
224-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
225                android:value="androidx.startup" />
225-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
226        </provider>
227
228        <service
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
229            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
231            android:enabled="@bool/enable_system_alarm_service_default"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
232            android:exported="false" />
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
233        <service
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
234            android:name="androidx.work.impl.background.systemjob.SystemJobService"
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
236            android:enabled="@bool/enable_system_job_service_default"
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
237            android:exported="true"
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
238            android:permission="android.permission.BIND_JOB_SERVICE" />
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
239        <service
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
240            android:name="androidx.work.impl.foreground.SystemForegroundService"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
242            android:enabled="@bool/enable_system_foreground_service_default"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
243            android:exported="false" />
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
244
245        <receiver
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
246            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
247            android:directBootAware="false"
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
248            android:enabled="true"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
249            android:exported="false" />
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
250        <receiver
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
251            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
252            android:directBootAware="false"
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
253            android:enabled="false"
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
254            android:exported="false" >
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
255            <intent-filter>
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
256                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
257                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
257-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
258            </intent-filter>
259        </receiver>
260        <receiver
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
261            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
262            android:directBootAware="false"
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
263            android:enabled="false"
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
264            android:exported="false" >
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
265            <intent-filter>
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
266                <action android:name="android.intent.action.BATTERY_OKAY" />
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
266-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
267                <action android:name="android.intent.action.BATTERY_LOW" />
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
267-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
268            </intent-filter>
269        </receiver>
270        <receiver
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
271            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
272            android:directBootAware="false"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
273            android:enabled="false"
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
274            android:exported="false" >
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
275            <intent-filter>
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
276                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
277                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
278            </intent-filter>
279        </receiver>
280        <receiver
280-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
281            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
281-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
282            android:directBootAware="false"
282-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
283            android:enabled="false"
283-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
284            android:exported="false" >
284-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
285            <intent-filter>
285-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
286                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
286-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
287            </intent-filter>
288        </receiver>
289        <receiver
289-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
290            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
291            android:directBootAware="false"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
292            android:enabled="false"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
293            android:exported="false" >
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
294            <intent-filter>
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
295                <action android:name="android.intent.action.BOOT_COMPLETED" />
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
296                <action android:name="android.intent.action.TIME_SET" />
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
297                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
298            </intent-filter>
299        </receiver>
300        <receiver
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
301            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
302            android:directBootAware="false"
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
303            android:enabled="@bool/enable_system_alarm_service_default"
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
304            android:exported="false" >
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
305            <intent-filter>
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
306                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
306-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
307            </intent-filter>
308        </receiver>
309        <receiver
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
310            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
311            android:directBootAware="false"
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
312            android:enabled="true"
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
313            android:exported="true"
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
314            android:permission="android.permission.DUMP" >
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
315            <intent-filter>
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
316                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a99dba1794d8cb59e2ce70900f2b65e\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
317            </intent-filter>
318        </receiver>
319        <receiver
319-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
320            android:name="androidx.profileinstaller.ProfileInstallReceiver"
320-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
321            android:directBootAware="false"
321-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
322            android:enabled="true"
322-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
323            android:exported="true"
323-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
324            android:permission="android.permission.DUMP" >
324-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
325            <intent-filter>
325-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
326                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
326-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
327            </intent-filter>
328            <intent-filter>
328-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
329                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
329-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
329-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
330            </intent-filter>
331            <intent-filter>
331-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
332                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
332-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
332-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
333            </intent-filter>
334            <intent-filter>
334-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
335                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
335-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\df39360754794f21ea2acc03765a7dc4\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
336            </intent-filter>
337        </receiver>
338
339        <service
339-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
340            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
340-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
341            android:exported="false" >
341-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
342            <meta-data
342-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
343                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
343-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
344                android:value="cct" />
344-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\edcff5a84172a086492b67a8809ad4ef\transformed\jetified-transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
345        </service>
346        <service
346-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
347            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
347-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
348            android:exported="false"
348-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
349            android:permission="android.permission.BIND_JOB_SERVICE" >
349-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
350        </service>
351
352        <receiver
352-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
353            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
353-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
354            android:exported="false" />
354-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\transforms-3\baf16a8448c3035bc3e55bb17e7a6503\transformed\jetified-transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
355
356        <service
356-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
357            android:name="androidx.room.MultiInstanceInvalidationService"
357-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
358            android:directBootAware="true"
358-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
359            android:exported="false" />
359-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\372f1ac5c51ff48f8326e7f9214b1b48\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
360
361        <provider
361-->[me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:12:9-16:43
362            android:name="me.jessyan.autosize.InitProvider"
362-->[me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:13:13-60
363            android:authorities="com.airdoc.mpd.autosize-init-provider"
363-->[me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:14:13-74
364            android:exported="false"
364-->[me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:15:13-37
365            android:multiprocess="true" />
365-->[me.jessyan:autosize:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\acc3380b610080c052441ed976673abb\transformed\jetified-autosize-1.2.1\AndroidManifest.xml:16:13-40
366    </application>
367
368</manifest>
