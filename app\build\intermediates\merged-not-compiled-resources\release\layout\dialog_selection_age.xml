<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="330dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_white_round_25_bg">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_selection_age"
        android:textColor="@color/color_333333"
        android:textSize="19sp"
        android:includeFontPadding="false"
        android:layout_marginStart="20dp"
        android:layout_marginTop="15dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <NumberPicker
        android:id="@+id/np_select_age"
        android:layout_width="355dp"
        android:layout_height="wrap_content"
        android:selectionDividerHeight="1px"
        android:layout_marginTop="65dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d6dce1_round_bg"
        android:focusable="true"
        android:layout_marginBottom="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="30dp"
        android:layout_marginStart="15dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>