// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLanguageSettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clLanguageRoot;

  @NonNull
  public final ImageView ivLanguage;

  @NonNull
  public final ImageView ivSelect;

  @NonNull
  public final TextView tvLanguage;

  private ItemLanguageSettingsBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clLanguageRoot, @NonNull ImageView ivLanguage,
      @NonNull ImageView ivSelect, @NonNull TextView tvLanguage) {
    this.rootView = rootView;
    this.clLanguageRoot = clLanguageRoot;
    this.ivLanguage = ivLanguage;
    this.ivSelect = ivSelect;
    this.tvLanguage = tvLanguage;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLanguageSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLanguageSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_language_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLanguageSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      ConstraintLayout clLanguageRoot = (ConstraintLayout) rootView;

      id = R.id.iv_language;
      ImageView ivLanguage = ViewBindings.findChildViewById(rootView, id);
      if (ivLanguage == null) {
        break missingId;
      }

      id = R.id.iv_select;
      ImageView ivSelect = ViewBindings.findChildViewById(rootView, id);
      if (ivSelect == null) {
        break missingId;
      }

      id = R.id.tv_language;
      TextView tvLanguage = ViewBindings.findChildViewById(rootView, id);
      if (tvLanguage == null) {
        break missingId;
      }

      return new ItemLanguageSettingsBinding((ConstraintLayout) rootView, clLanguageRoot,
          ivLanguage, ivSelect, tvLanguage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
