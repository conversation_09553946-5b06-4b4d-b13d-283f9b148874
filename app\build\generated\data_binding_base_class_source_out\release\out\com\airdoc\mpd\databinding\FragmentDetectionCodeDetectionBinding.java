// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDetectionCodeDetectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final EditText etDetectionCode;

  @NonNull
  public final ImageView ivCross;

  @NonNull
  public final ImageView ivScan;

  @NonNull
  public final TextView tvStartDetection;

  private FragmentDetectionCodeDetectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull EditText etDetectionCode, @NonNull ImageView ivCross, @NonNull ImageView ivScan,
      @NonNull TextView tvStartDetection) {
    this.rootView = rootView;
    this.etDetectionCode = etDetectionCode;
    this.ivCross = ivCross;
    this.ivScan = ivScan;
    this.tvStartDetection = tvStartDetection;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDetectionCodeDetectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDetectionCodeDetectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_detection_code_detection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDetectionCodeDetectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_detection_code;
      EditText etDetectionCode = ViewBindings.findChildViewById(rootView, id);
      if (etDetectionCode == null) {
        break missingId;
      }

      id = R.id.iv_cross;
      ImageView ivCross = ViewBindings.findChildViewById(rootView, id);
      if (ivCross == null) {
        break missingId;
      }

      id = R.id.iv_scan;
      ImageView ivScan = ViewBindings.findChildViewById(rootView, id);
      if (ivScan == null) {
        break missingId;
      }

      id = R.id.tv_start_detection;
      TextView tvStartDetection = ViewBindings.findChildViewById(rootView, id);
      if (tvStartDetection == null) {
        break missingId;
      }

      return new FragmentDetectionCodeDetectionBinding((ConstraintLayout) rootView, etDetectionCode,
          ivCross, ivScan, tvStartDetection);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
