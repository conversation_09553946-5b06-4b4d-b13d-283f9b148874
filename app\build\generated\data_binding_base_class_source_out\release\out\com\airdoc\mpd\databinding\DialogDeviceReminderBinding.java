// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogDeviceReminderBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final TextView tvCancel;

  @NonNull
  public final TextView tvOk;

  @NonNull
  public final TextView tvOpenDate;

  @NonNull
  public final TextView tvTitle;

  @NonNull
  public final TextView tvValidUntil;

  private DialogDeviceReminderBinding(@NonNull ConstraintLayout rootView,
      @NonNull TextView tvCancel, @NonNull TextView tvOk, @NonNull TextView tvOpenDate,
      @NonNull TextView tvTitle, @NonNull TextView tvValidUntil) {
    this.rootView = rootView;
    this.tvCancel = tvCancel;
    this.tvOk = tvOk;
    this.tvOpenDate = tvOpenDate;
    this.tvTitle = tvTitle;
    this.tvValidUntil = tvValidUntil;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogDeviceReminderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogDeviceReminderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_device_reminder, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogDeviceReminderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.tv_cancel;
      TextView tvCancel = ViewBindings.findChildViewById(rootView, id);
      if (tvCancel == null) {
        break missingId;
      }

      id = R.id.tv_ok;
      TextView tvOk = ViewBindings.findChildViewById(rootView, id);
      if (tvOk == null) {
        break missingId;
      }

      id = R.id.tv_open_date;
      TextView tvOpenDate = ViewBindings.findChildViewById(rootView, id);
      if (tvOpenDate == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      id = R.id.tv_valid_until;
      TextView tvValidUntil = ViewBindings.findChildViewById(rootView, id);
      if (tvValidUntil == null) {
        break missingId;
      }

      return new DialogDeviceReminderBinding((ConstraintLayout) rootView, tvCancel, tvOk,
          tvOpenDate, tvTitle, tvValidUntil);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
