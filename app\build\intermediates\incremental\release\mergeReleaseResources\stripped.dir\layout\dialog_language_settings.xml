<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="380dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_device_info_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_language_settings"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_left"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintRight_toLeftOf="@+id/tv_title"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_right"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_language"
        android:layout_width="357dp"
        android:layout_height="180dp"
        android:background="@drawable/common_white_round_20_bg"
        android:layout_marginTop="15dp"
        android:padding="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_cancel"
        android:textColor="#8D9EAC"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_d6dce1_round_bg"
        android:focusable="true"
        android:layout_marginBottom="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_ok"
        app:layout_constraintHorizontal_chainStyle="packed"/>

    <TextView
        android:id="@+id/tv_ok"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="@string/str_ok"
        android:textColor="@color/white"
        android:textSize="17sp"
        android:gravity="center"
        android:background="@drawable/common_eb4e89_round_bg"
        android:focusable="true"
        android:layout_marginBottom="25dp"
        android:layout_marginStart="30dp"
        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>