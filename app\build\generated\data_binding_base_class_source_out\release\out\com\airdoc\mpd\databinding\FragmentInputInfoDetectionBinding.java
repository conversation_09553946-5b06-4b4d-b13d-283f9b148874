// Generated by view binder compiler. Do not edit!
package com.airdoc.mpd.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.airdoc.mpd.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentInputInfoDetectionBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ConstraintLayout clInputAge;

  @NonNull
  public final ConstraintLayout clInputGender;

  @NonNull
  public final ConstraintLayout clInputName;

  @NonNull
  public final ConstraintLayout clInputPhone;

  @NonNull
  public final EditText etAge;

  @NonNull
  public final EditText etName;

  @NonNull
  public final EditText etPhone;

  @NonNull
  public final ImageView ivInputAge;

  @NonNull
  public final ImageView ivInputGender;

  @NonNull
  public final ImageView ivInputName;

  @NonNull
  public final ImageView ivInputPhone;

  @NonNull
  public final RadioButton rbConfidentiality;

  @NonNull
  public final RadioButton rbFemale;

  @NonNull
  public final RadioButton rbMale;

  @NonNull
  public final RadioGroup rgGender;

  @NonNull
  public final TextView tvStartDetection;

  private FragmentInputInfoDetectionBinding(@NonNull ConstraintLayout rootView,
      @NonNull ConstraintLayout clInputAge, @NonNull ConstraintLayout clInputGender,
      @NonNull ConstraintLayout clInputName, @NonNull ConstraintLayout clInputPhone,
      @NonNull EditText etAge, @NonNull EditText etName, @NonNull EditText etPhone,
      @NonNull ImageView ivInputAge, @NonNull ImageView ivInputGender,
      @NonNull ImageView ivInputName, @NonNull ImageView ivInputPhone,
      @NonNull RadioButton rbConfidentiality, @NonNull RadioButton rbFemale,
      @NonNull RadioButton rbMale, @NonNull RadioGroup rgGender,
      @NonNull TextView tvStartDetection) {
    this.rootView = rootView;
    this.clInputAge = clInputAge;
    this.clInputGender = clInputGender;
    this.clInputName = clInputName;
    this.clInputPhone = clInputPhone;
    this.etAge = etAge;
    this.etName = etName;
    this.etPhone = etPhone;
    this.ivInputAge = ivInputAge;
    this.ivInputGender = ivInputGender;
    this.ivInputName = ivInputName;
    this.ivInputPhone = ivInputPhone;
    this.rbConfidentiality = rbConfidentiality;
    this.rbFemale = rbFemale;
    this.rbMale = rbMale;
    this.rgGender = rgGender;
    this.tvStartDetection = tvStartDetection;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentInputInfoDetectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentInputInfoDetectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_input_info_detection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentInputInfoDetectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cl_input_age;
      ConstraintLayout clInputAge = ViewBindings.findChildViewById(rootView, id);
      if (clInputAge == null) {
        break missingId;
      }

      id = R.id.cl_input_gender;
      ConstraintLayout clInputGender = ViewBindings.findChildViewById(rootView, id);
      if (clInputGender == null) {
        break missingId;
      }

      id = R.id.cl_input_name;
      ConstraintLayout clInputName = ViewBindings.findChildViewById(rootView, id);
      if (clInputName == null) {
        break missingId;
      }

      id = R.id.cl_input_phone;
      ConstraintLayout clInputPhone = ViewBindings.findChildViewById(rootView, id);
      if (clInputPhone == null) {
        break missingId;
      }

      id = R.id.et_age;
      EditText etAge = ViewBindings.findChildViewById(rootView, id);
      if (etAge == null) {
        break missingId;
      }

      id = R.id.et_name;
      EditText etName = ViewBindings.findChildViewById(rootView, id);
      if (etName == null) {
        break missingId;
      }

      id = R.id.et_phone;
      EditText etPhone = ViewBindings.findChildViewById(rootView, id);
      if (etPhone == null) {
        break missingId;
      }

      id = R.id.iv_input_age;
      ImageView ivInputAge = ViewBindings.findChildViewById(rootView, id);
      if (ivInputAge == null) {
        break missingId;
      }

      id = R.id.iv_input_gender;
      ImageView ivInputGender = ViewBindings.findChildViewById(rootView, id);
      if (ivInputGender == null) {
        break missingId;
      }

      id = R.id.iv_input_name;
      ImageView ivInputName = ViewBindings.findChildViewById(rootView, id);
      if (ivInputName == null) {
        break missingId;
      }

      id = R.id.iv_input_phone;
      ImageView ivInputPhone = ViewBindings.findChildViewById(rootView, id);
      if (ivInputPhone == null) {
        break missingId;
      }

      id = R.id.rb_confidentiality;
      RadioButton rbConfidentiality = ViewBindings.findChildViewById(rootView, id);
      if (rbConfidentiality == null) {
        break missingId;
      }

      id = R.id.rb_female;
      RadioButton rbFemale = ViewBindings.findChildViewById(rootView, id);
      if (rbFemale == null) {
        break missingId;
      }

      id = R.id.rb_male;
      RadioButton rbMale = ViewBindings.findChildViewById(rootView, id);
      if (rbMale == null) {
        break missingId;
      }

      id = R.id.rg_gender;
      RadioGroup rgGender = ViewBindings.findChildViewById(rootView, id);
      if (rgGender == null) {
        break missingId;
      }

      id = R.id.tv_start_detection;
      TextView tvStartDetection = ViewBindings.findChildViewById(rootView, id);
      if (tvStartDetection == null) {
        break missingId;
      }

      return new FragmentInputInfoDetectionBinding((ConstraintLayout) rootView, clInputAge,
          clInputGender, clInputName, clInputPhone, etAge, etName, etPhone, ivInputAge,
          ivInputGender, ivInputName, ivInputPhone, rbConfidentiality, rbFemale, rbMale, rgGender,
          tvStartDetection);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
